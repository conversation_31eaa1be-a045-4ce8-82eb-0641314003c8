#!/usr/bin/env python3
"""
测试时间信息优化功能
"""
import sys
import os
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from tools.allure_to_excel import AllureToExcelConverter

def test_summary_json_parsing():
    """测试summary.json解析功能"""
    print("🧪 测试summary.json解析功能...")
    print("=" * 60)
    
    # 创建转换器实例
    converter = AllureToExcelConverter("reports")
    
    try:
        # 获取时间信息
        time_info = converter._get_test_time_info()
        
        print("✅ 时间信息获取成功:")
        print(f"   开始时间: {time_info['start_time']}")
        print(f"   结束时间: {time_info['end_time']}")
        print(f"   持续时长: {time_info['duration_minutes']} 分钟")
        print(f"   开始时间戳: {time_info['start_timestamp']}")
        print(f"   结束时间戳: {time_info['stop_timestamp']}")
        print(f"   持续时长(毫秒): {time_info['duration_ms']}")
        
        return time_info
        
    except Exception as e:
        print(f"❌ 时间信息获取失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_summary_json_content():
    """测试summary.json文件内容"""
    print("\n📄 测试summary.json文件内容...")
    print("=" * 60)
    
    try:
        summary_path = Path("reports/allure-report/widgets/summary.json")
        
        if not summary_path.exists():
            print(f"❌ summary.json文件不存在: {summary_path}")
            return None
        
        with open(summary_path, 'r', encoding='utf-8') as f:
            summary_data = json.load(f)
        
        print("✅ summary.json文件读取成功:")
        print(f"   报告名称: {summary_data.get('reportName', 'Unknown')}")
        
        # 统计信息
        statistic = summary_data.get('statistic', {})
        print(f"   总用例数: {statistic.get('total', 0)}")
        print(f"   通过数: {statistic.get('passed', 0)}")
        print(f"   失败数: {statistic.get('failed', 0)}")
        print(f"   跳过数: {statistic.get('skipped', 0)}")
        print(f"   损坏数: {statistic.get('broken', 0)}")
        
        # 时间信息
        time_data = summary_data.get('time', {})
        print(f"   开始时间戳: {time_data.get('start', 0)}")
        print(f"   结束时间戳: {time_data.get('stop', 0)}")
        print(f"   持续时长(毫秒): {time_data.get('duration', 0)}")
        
        return summary_data
        
    except Exception as e:
        print(f"❌ summary.json文件读取失败: {e}")
        return None

def test_time_conversion():
    """测试时间转换功能"""
    print("\n🕐 测试时间转换功能...")
    print("=" * 60)
    
    try:
        # 从summary.json获取原始时间戳
        summary_path = Path("reports/allure-report/widgets/summary.json")
        
        if not summary_path.exists():
            print("❌ summary.json文件不存在，无法测试时间转换")
            return
        
        with open(summary_path, 'r', encoding='utf-8') as f:
            summary_data = json.load(f)
        
        time_data = summary_data.get('time', {})
        start_ms = time_data.get('start', 0)
        stop_ms = time_data.get('stop', 0)
        duration_ms = time_data.get('duration', 0)
        
        print("📊 原始时间戳信息:")
        print(f"   开始时间戳(毫秒): {start_ms}")
        print(f"   结束时间戳(毫秒): {stop_ms}")
        print(f"   持续时长(毫秒): {duration_ms}")
        
        # 转换为可读格式
        if start_ms > 0:
            start_datetime = datetime.fromtimestamp(start_ms / 1000)
            print(f"   开始时间: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if stop_ms > 0:
            stop_datetime = datetime.fromtimestamp(stop_ms / 1000)
            print(f"   结束时间: {stop_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if duration_ms > 0:
            duration_minutes = duration_ms / (1000 * 60)
            duration_seconds = duration_ms / 1000
            print(f"   持续时长: {duration_minutes:.1f} 分钟 ({duration_seconds:.1f} 秒)")
        
    except Exception as e:
        print(f"❌ 时间转换测试失败: {e}")

def test_optimized_notification():
    """测试优化后的通知功能"""
    print("\n📱 测试优化后的通知功能...")
    print("=" * 60)
    
    try:
        converter = AllureToExcelConverter("reports")
        
        # 模拟测试结果
        mock_test_results = [
            {'测试状态': 'passed'},
            {'测试状态': 'passed'},
            {'测试状态': 'failed'},
            {'测试状态': 'passed'},
        ]
        
        status_counts = {'passed': 3, 'failed': 1}
        total_cases = 4
        
        print("📊 模拟测试数据:")
        print(f"   总用例数: {total_cases}")
        print(f"   成功数: {status_counts['passed']}")
        print(f"   失败数: {status_counts['failed']}")
        
        print("\n📱 正在发送优化后的飞书通知...")
        converter._send_feishu_notification(mock_test_results, status_counts, total_cases)
        print("✅ 优化后的飞书通知测试完成")
        
    except Exception as e:
        print(f"❌ 优化后的通知测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🎯 时间信息优化功能测试")
    print("=" * 70)
    
    test_summary_json_content()
    test_time_conversion()
    test_summary_json_parsing()
    test_optimized_notification()
    
    print("\n" + "=" * 70)
    print("🎉 测试完成!")
    print("\n💡 优化说明:")
    print("   - 持续时长现在直接从summary.json的duration字段获取")
    print("   - 开始时间和结束时间从start/stop时间戳转换而来")
    print("   - 时间格式为HH:MM:SS，更加直观易读")
    print("   - 支持文件不存在时的默认值处理")

if __name__ == '__main__':
    main()
