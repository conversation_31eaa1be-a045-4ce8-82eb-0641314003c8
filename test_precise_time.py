#!/usr/bin/env python3
"""
测试精确到秒的时间显示功能
"""
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from tools.allure_to_excel import AllureToExcelConverter

def test_precise_time_display():
    """测试精确到秒的时间显示"""
    print("🧪 测试精确到秒的时间显示...")
    print("=" * 60)
    
    # 创建转换器实例
    converter = AllureToExcelConverter("reports")
    
    try:
        # 获取时间信息
        time_info = converter._get_test_time_info()
        
        print("✅ 精确时间信息获取成功:")
        print(f"   开始时间: {time_info['start_time']}")
        print(f"   结束时间: {time_info['end_time']}")
        print(f"   持续时长(分钟): {time_info['duration_minutes']} 分钟")
        print(f"   持续时长(秒): {time_info['duration_seconds']} 秒")
        print(f"   格式化显示: {time_info['duration_display']}")
        print(f"   原始毫秒: {time_info['duration_ms']} ms")
        
        return time_info
        
    except Exception as e:
        print(f"❌ 精确时间信息获取失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_filename_precision():
    """测试文件名精确到秒"""
    print("\n📁 测试文件名精确到秒...")
    print("=" * 60)
    
    converter = AllureToExcelConverter("reports")
    
    try:
        filename = converter._get_today_filename()
        print(f"✅ 生成的文件名: {filename}")
        
        # 验证文件名格式
        if "_" in filename and ":" not in filename:
            print("✅ 文件名格式正确（使用-代替:避免文件名冲突）")
        else:
            print("⚠️  文件名格式可能需要调整")
            
    except Exception as e:
        print(f"❌ 文件名生成失败: {e}")

def test_duration_formatting():
    """测试不同时长的格式化显示"""
    print("\n⏱ 测试不同时长的格式化显示...")
    print("=" * 60)
    
    # 模拟不同的时长（毫秒）
    test_durations = [
        (30000, "30秒"),           # 30秒
        (90000, "1分30秒"),        # 1分30秒
        (3600000, "1小时0分0秒"),   # 1小时
        (3723000, "1小时2分3秒"),   # 1小时2分3秒
        (19056365, "5小时17分36秒") # 实际测试数据
    ]
    
    for duration_ms, expected_format in test_durations:
        # 计算格式化显示
        duration_seconds = duration_ms / 1000
        
        if duration_seconds >= 3600:  # 超过1小时
            hours = int(duration_seconds // 3600)
            minutes = int((duration_seconds % 3600) // 60)
            seconds = int(duration_seconds % 60)
            duration_display = f"{hours}小时{minutes}分{seconds}秒"
        elif duration_seconds >= 60:  # 超过1分钟
            minutes = int(duration_seconds // 60)
            seconds = int(duration_seconds % 60)
            duration_display = f"{minutes}分{seconds}秒"
        else:  # 小于1分钟
            duration_display = f"{duration_seconds:.1f}秒"
        
        print(f"   {duration_ms}ms → {duration_display}")

def test_notification_with_precise_time():
    """测试包含精确时间的通知"""
    print("\n📱 测试包含精确时间的飞书通知...")
    print("=" * 60)
    
    try:
        converter = AllureToExcelConverter("reports")
        
        # 模拟测试结果
        mock_test_results = [
            {'测试状态': 'passed'},
            {'测试状态': 'passed'},
            {'测试状态': 'failed'},
            {'测试状态': 'passed'},
            {'测试状态': 'passed'},
        ]
        
        status_counts = {'passed': 4, 'failed': 1}
        total_cases = 5
        
        print("📊 模拟测试数据:")
        print(f"   总用例数: {total_cases}")
        print(f"   成功数: {status_counts['passed']}")
        print(f"   失败数: {status_counts['failed']}")
        print(f"   成功率: {(status_counts['passed'] / total_cases * 100):.1f}%")
        
        print("\n📱 正在发送包含精确时间的飞书通知...")
        converter._send_feishu_notification(mock_test_results, status_counts, total_cases)
        print("✅ 精确时间飞书通知测试完成")
        
    except Exception as e:
        print(f"❌ 精确时间通知测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_time_comparison():
    """对比优化前后的时间显示"""
    print("\n🔄 对比优化前后的时间显示...")
    print("=" * 60)
    
    converter = AllureToExcelConverter("reports")
    time_info = converter._get_test_time_info()
    
    print("📊 时间显示对比:")
    print(f"   优化前: {time_info['duration_minutes']} 分钟")
    print(f"   优化后: {time_info['duration_display']}")
    print(f"   精确度提升: 从分钟级别提升到秒级别")
    
    # 计算精确度提升
    duration_seconds = time_info['duration_seconds']
    duration_minutes = time_info['duration_minutes']
    
    if duration_seconds >= 60:
        precision_improvement = f"提升了 {duration_seconds % 60:.0f} 秒的精度"
    else:
        precision_improvement = "小于1分钟的测试现在可以精确显示"
    
    print(f"   精确度提升详情: {precision_improvement}")

def main():
    """主函数"""
    print("🎯 精确到秒的时间显示测试")
    print("=" * 70)
    
    test_precise_time_display()
    test_filename_precision()
    test_duration_formatting()
    test_time_comparison()
    test_notification_with_precise_time()
    
    print("\n" + "=" * 70)
    print("🎉 测试完成!")
    print("\n💡 优化效果:")
    print("   - 文件名精确到秒，避免同一分钟内的文件名冲突")
    print("   - 持续时长显示精确到秒，提供更准确的执行时间信息")
    print("   - 支持小时、分钟、秒的智能格式化显示")
    print("   - 飞书通知中的时间信息更加精确和直观")

if __name__ == '__main__':
    main()
