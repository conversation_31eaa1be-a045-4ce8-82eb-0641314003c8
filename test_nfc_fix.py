#!/usr/bin/env python3
"""
测试NFC状态检查修复
"""
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from pages.base.system_status_checker import SystemStatusChecker

def test_nfc_status():
    """测试NFC状态检查"""
    print("🧪 测试NFC状态检查修复...")
    print("=" * 50)
    
    try:
        checker = SystemStatusChecker()
        
        print("📱 正在检查NFC状态...")
        nfc_status = checker.check_nfc_status()
        
        if nfc_status is not None:
            print(f"✅ NFC状态检查成功: {'开启' if nfc_status else '关闭'}")
        else:
            print("⚠️  NFC状态检查返回None（可能设备不支持或命令失败）")
            
    except Exception as e:
        print(f"❌ NFC状态检查失败: {e}")
        import traceback
        traceback.print_exc()

def test_other_status_checks():
    """测试其他状态检查方法"""
    print("\n🧪 测试其他状态检查方法...")
    print("=" * 50)
    
    checker = SystemStatusChecker()
    
    test_methods = [
        ("WiFi状态", checker.check_wifi_status),
        ("蓝牙状态", checker.check_bluetooth_status),
        ("Active Halo Lighting状态", checker.check_active_halo_lighting_status),
    ]
    
    for name, method in test_methods:
        try:
            print(f"📱 正在检查{name}...")
            result = method()
            print(f"✅ {name}检查成功: {'开启' if result else '关闭'}")
        except Exception as e:
            print(f"❌ {name}检查失败: {e}")

def test_safe_strip_method():
    """测试安全strip方法"""
    print("\n🧪 测试安全strip方法...")
    print("=" * 50)
    
    checker = SystemStatusChecker()
    
    test_cases = [
        ("正常字符串", "  hello world  "),
        ("空字符串", ""),
        ("None值", None),
        ("只有空格", "   "),
    ]
    
    for name, test_input in test_cases:
        try:
            result = checker._safe_strip(test_input)
            print(f"✅ {name}: '{test_input}' -> '{result}'")
        except Exception as e:
            print(f"❌ {name}测试失败: {e}")

def main():
    """主函数"""
    print("🔧 SystemStatusChecker NFC修复测试")
    print("=" * 60)
    
    test_safe_strip_method()
    test_nfc_status()
    test_other_status_checks()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")

if __name__ == '__main__':
    main()
