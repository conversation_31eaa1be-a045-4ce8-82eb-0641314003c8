#!/usr/bin/env python3
"""
测试增强后的 get_response_all_text 方法
特别验证联系人和电话号码相关文案的获取能力
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pages.apps.ella.floating_page import EllaFloatingPage
from core.logger import log


def test_contact_text_detection():
    """测试联系人相关文本检测功能"""
    print("\n=== 测试联系人相关文本检测 ===")
    
    floating_page = EllaFloatingPage()
    
    # 测试用例：各种联系人相关文案
    test_texts = [
        "The following number is recognized. You can save it as a contact after confirmation.",
        "Phone number: 13800138000",
        "Contact name: <PERSON>",
        "Add this number to contacts",
        "Save as contact",
        "Number recognized: +86 138-0013-8000",
        "联系人已保存",
        "识别到号码：13800138000",
        "确认后可保存为联系人",
        "Hello, how can I help you?",  # 非联系人相关
        "Good morning!",  # 系统文本
        "13800138000",  # 纯号码
        "138",  # 短号码
        "Contact info saved successfully"
    ]
    
    contact_count = 0
    for text in test_texts:
        is_contact = floating_page._is_contact_related_text(text)
        status = "✅" if is_contact else "❌"
        print(f"{status} '{text}' -> 联系人相关: {is_contact}")
        if is_contact:
            contact_count += 1
    
    print(f"\n检测结果: {contact_count}/{len(test_texts)} 个文本被识别为联系人相关")


def test_contact_element_methods():
    """测试联系人元素相关方法"""
    print("\n=== 测试联系人元素方法 ===")
    
    floating_page = EllaFloatingPage()
    
    # 测试新增的方法
    methods_to_test = [
        '_get_contact_related_texts',
        '_get_contact_element_texts',
        '_filter_contact_related_texts',
        '_get_response_contact_texts',
        '_prioritize_contact_texts'
    ]
    
    for method_name in methods_to_test:
        try:
            method = getattr(floating_page, method_name)
            if callable(method):
                print(f"✅ {method_name}: 方法定义正确")
            else:
                print(f"❌ {method_name}: 不是可调用方法")
        except AttributeError:
            print(f"❌ {method_name}: 方法未找到")
        except Exception as e:
            print(f"❌ {method_name}: 方法异常 - {e}")


def test_contact_text_filtering():
    """测试联系人文本筛选功能"""
    print("\n=== 测试联系人文本筛选 ===")
    
    floating_page = EllaFloatingPage()
    
    # 模拟从页面获取的文本列表
    mock_texts = [
        "Good morning!",
        "The following number is recognized: 13800138000",
        "You can save it as a contact after confirmation",
        "DeepSeek-R1",
        "Contact name: Zhang San",
        "Phone: +86 138-0013-8000",
        "Feel free to ask me any questions…",
        "Add to contacts",
        "Cancel",
        "Save",
        "13912345678",
        "Hello world",
        "Number detected successfully"
    ]
    
    print("原始文本列表:")
    for i, text in enumerate(mock_texts):
        print(f"  {i+1}. {text}")
    
    # 测试联系人相关文本筛选
    contact_texts = floating_page._filter_contact_related_texts(mock_texts)
    print(f"\n筛选出的联系人相关文本 ({len(contact_texts)} 个):")
    for i, text in enumerate(contact_texts):
        print(f"  {i+1}. {text}")
    
    # 测试有意义文本过滤
    meaningful_texts = floating_page._filter_meaningful_texts(mock_texts)
    print(f"\n过滤后的有意义文本 ({len(meaningful_texts)} 个):")
    for i, text in enumerate(meaningful_texts):
        print(f"  {i+1}. {text}")
    
    # 测试优先级排序
    prioritized_texts = floating_page._prioritize_contact_texts(meaningful_texts)
    print(f"\n优先级排序后的文本 ({len(prioritized_texts)} 个):")
    for i, text in enumerate(prioritized_texts):
        is_contact = floating_page._is_contact_related_text(text)
        marker = "📞" if is_contact else "💬"
        print(f"  {i+1}. {marker} {text}")


def test_phone_number_patterns():
    """测试电话号码模式识别"""
    print("\n=== 测试电话号码模式识别 ===")
    
    floating_page = EllaFloatingPage()
    
    # 各种电话号码格式
    phone_test_cases = [
        "13800138000",
        "+86 138-0013-8000",
        "138 0013 8000",
        "010-12345678",
        "******-123-4567",
        "************",
        "95588",
        "10086",
        "123",  # 太短
        "abc123def",  # 包含字母
        "The number is 13800138000",
        "Call me at +86-138-0013-8000",
        "Phone: 13912345678"
    ]
    
    for text in phone_test_cases:
        is_contact = floating_page._is_contact_related_text(text)
        status = "📞" if is_contact else "❌"
        print(f"{status} '{text}' -> 包含号码: {is_contact}")


def test_enhanced_get_response_all_text():
    """测试增强后的 get_response_all_text 方法"""
    print("\n=== 测试增强后的 get_response_all_text 方法 ===")
    
    try:
        floating_page = EllaFloatingPage()
        
        # 模拟调用 get_response_all_text 方法
        print("调用 get_response_all_text() 方法...")
        
        # 注意：这个测试在没有真实设备连接时会返回空列表
        # 但我们可以验证方法是否正常工作
        response_texts = floating_page.get_response_all_text()
        
        print(f"返回结果类型: {type(response_texts)}")
        print(f"返回结果长度: {len(response_texts)}")
        
        if response_texts:
            print("获取到的响应文案:")
            for i, text in enumerate(response_texts):
                is_contact = floating_page._is_contact_related_text(text)
                marker = "📞" if is_contact else "💬"
                print(f"  {i+1}. {marker} {text}")
        else:
            print("未获取到响应文案（这在没有连接设备时是正常的）")
        
        print("✅ get_response_all_text 方法调用成功")
        
    except Exception as e:
        print(f"❌ get_response_all_text 方法调用异常: {e}")


def test_contact_info_integration():
    """测试联系人信息获取的集成功能"""
    print("\n=== 测试联系人信息获取集成 ===")
    
    floating_page = EllaFloatingPage()
    
    # 测试联系人信息获取
    try:
        contact_info = floating_page.get_contact_info()
        print(f"联系人信息结构: {contact_info}")
        
        # 测试单独的获取方法
        name = floating_page.get_contact_name()
        phone = floating_page.get_phone_number()
        visible = floating_page.is_contact_info_visible()
        
        print(f"联系人名称: '{name}'")
        print(f"手机号码: '{phone}'")
        print(f"信息可见: {visible}")
        
        print("✅ 联系人信息获取方法正常")
        
    except Exception as e:
        print(f"❌ 联系人信息获取异常: {e}")


def main():
    """主测试函数"""
    print("🚀 开始测试增强后的响应文案获取功能")
    
    # 运行所有测试
    test_contact_text_detection()
    test_contact_element_methods()
    test_contact_text_filtering()
    test_phone_number_patterns()
    test_enhanced_get_response_all_text()
    test_contact_info_integration()
    
    print("\n🎉 所有测试完成!")
    print("\n📝 说明:")
    print("   - 联系人文本检测功能已增强")
    print("   - 电话号码模式识别已优化")
    print("   - 响应文案获取已支持联系人优先级")
    print("   - 实际设备测试时会获取到真实的响应文案")


if __name__ == "__main__":
    main()
