"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenPlayPoliticalNews(SimpleEllaTest):
    """Ella打开Add the images and text on the screen to the note测试类"""

    @allure.title("测试Add the images and text on the screen to the note")
    @allure.description("测试Add the images and text on the screen to the note指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_Add_the_images_and_text_on_the_screen_to_the_note(self, ella_app):
        """测试Add the images and text on the screen to the note命令"""
        command = "Add the images and text on the screen to the note"
        expected_text = ['Sorry', 'Oops', 'out of my reach']

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望内容"):

            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"


        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
