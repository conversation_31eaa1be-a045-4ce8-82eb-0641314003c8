"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
import logging
from testcases.test_ella.base_ella_test import SimpleEllaTest

log = logging.getLogger(__name__)


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestEllaWhatSWeatherLikeShanghaiToday(SimpleEllaTest):
    """Ella What's the weather like today 测试类"""
    command = "What's the weather like today"
    expected_text = ['the high is forecast','℃']


    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_what_s_the_weather_like_today(self, ella_app):
        f"""{self.command}"""

        command = self.command

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status=False  # 第三方集成通常不验证状态
            )

        with allure.step("验证响应包含期望内容"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"
            
        with allure.step("使用优化后的天气数据验证范围"):
            import re
            from pages.base.detectors.weather_detector import WeatherDetector
            
            # 确保response_text是字符串类型
            if isinstance(response_text, list):
                # 如果是列表，将其合并为一个字符串
                response_str = " ".join([str(text) for text in response_text if text])
            else:
                # 如果已经是字符串，直接使用
                response_str = str(response_text)
            
            # 从响应文本中提取温度值（包括负数）
            temp_pattern = r"(-?\d+)\s*℃"
            temp_match = re.search(temp_pattern, response_str)
            
            # 提取高温和低温
            high_temp_pattern = r"high is forecast as (-?\d+)\s*℃"
            low_temp_pattern = r"low as (-?\d+)\s*℃"
            
            high_temp_match = re.search(high_temp_pattern, response_str)
            low_temp_match = re.search(low_temp_pattern, response_str)
            
            # 构建天气数据字典
            weather_data = {}
            
            if temp_match:
                weather_data['temperature'] = temp_match.group(1) + "℃"
            
            if high_temp_match:
                weather_data['high_temperature'] = high_temp_match.group(1) + "℃"
                
            if low_temp_match:
                weather_data['low_temperature'] = low_temp_match.group(1) + "℃"
            
            # 使用天气检测器验证数据
            weather_detector = WeatherDetector()
            is_valid, error_message = weather_detector.validate_weather_data(weather_data)
            
            if is_valid:
                allure.attach(str(weather_data), "天气数据验证通过", allure.attachment_type.TEXT)
            else:
                allure.attach(f"{error_message}\n数据: {weather_data}", "天气数据验证失败", allure.attachment_type.TEXT)
                log.warning(f"⚠️ 天气数据验证失败: {error_message}, 数据: {weather_data}")
                assert is_valid, f"天气数据验证失败: {error_message}"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
