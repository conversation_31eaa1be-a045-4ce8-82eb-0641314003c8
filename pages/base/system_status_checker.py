"""
系统状态检查器
负责检查各种系统状态、应用状态和服务状态
"""
import subprocess
import time
import re
from typing import Optional, Dict, List, Tuple, Any
from core.logger import log


class SystemStatusChecker:
    """系统设置状态检查器"""

    def __init__(self, driver=None):
        """
        初始化状态检查器

        Args:
            driver: UIAutomator2驱动实例
        """
        self.driver = driver
        self._default_timeout = 5
        self._audio_timeout = 8

    # ==================== 通用工具方法 ====================

    def _run_adb_command(self, command: List[str], timeout: int = None,
                        encoding: str = 'utf-8', errors: str = 'ignore',
                        shell: bool = False) -> Tuple[bool, str, str]:
        """
        通用ADB命令执行方法

        Args:
            command: ADB命令列表
            timeout: 超时时间
            encoding: 编码方式
            errors: 错误处理方式
            shell: 是否使用shell执行

        Returns:
            Tuple[bool, str, str]: (是否成功, stdout, stderr)
        """
        try:
            timeout = timeout or self._default_timeout
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding=encoding,
                errors=errors,
                shell=shell
            )

            success = result.returncode == 0
            stdout = result.stdout.strip() if result.stdout else ""
            stderr = result.stderr.strip() if result.stderr else ""
            return success, stdout, stderr

        except subprocess.TimeoutExpired:
            log.warning(f"ADB命令超时: {' '.join(command)}")
            return False, "", "Timeout"
        except Exception as e:
            log.warning(f"ADB命令执行失败: {e}")
            return False, "", str(e)

    def _safe_strip(self, text) -> str:
        """
        安全地对文本执行strip操作，避免NoneType错误

        Args:
            text: 要处理的文本，可能为None

        Returns:
            str: 处理后的文本，如果输入为None则返回空字符串
        """
        return text.strip() if text is not None else ""

    def _get_setting(self, namespace: str, setting: str, timeout: int = None) -> Optional[str]:
        """
        获取系统设置值

        Args:
            namespace: 设置命名空间 (system/secure/global)
            setting: 设置名称
            timeout: 超时时间

        Returns:
            Optional[str]: 设置值，失败返回None
        """
        command = ["adb", "shell", "settings", "get", namespace, setting]
        success, stdout, stderr = self._run_adb_command(command, timeout)

        if success and stdout and stdout != "null":
            log.debug(f"获取设置 {namespace}.{setting}: {stdout}")
            return stdout

        return None

    def _check_settings_batch(self, settings_list: List[Tuple[str, str]],
                             target_value: str = "1") -> Tuple[bool, str, str]:
        """
        批量检查设置值

        Args:
            settings_list: 设置列表 [(namespace, setting), ...]
            target_value: 目标值

        Returns:
            Tuple[bool, str, str]: (是否找到目标值, 命名空间, 设置名)
        """
        for namespace, setting in settings_list:
            value = self._get_setting(namespace, setting)
            if value == target_value:
                log.info(f"找到目标值 {target_value} 在 {namespace}.{setting}")
                return True, namespace, setting
            elif value is not None:
                log.debug(f"{namespace}.{setting} = {value}")

        return False, "", ""

    def _get_system_property(self, property_name: str) -> Optional[str]:
        """
        获取系统属性值

        Args:
            property_name: 属性名称

        Returns:
            Optional[str]: 属性值，失败返回None
        """
        command = ["adb", "shell", "getprop", property_name]
        success, stdout, stderr = self._run_adb_command(command)

        if success and stdout:
            log.debug(f"获取属性 {property_name}: {stdout}")
            return stdout

        return None

    def _parse_boolean_setting(self, value: str) -> bool:
        """
        解析布尔设置值

        Args:
            value: 设置值

        Returns:
            bool: 解析结果
        """
        if value is None:
            return False
        return self._safe_strip(value) == "1"

    # ==================== 状态检查方法 ====================

    def check_bluetooth_status(self) -> bool:
        """
        检查蓝牙状态

        Returns:
            bool: 蓝牙是否已开启
        """
        try:
            log.info("检查蓝牙状态")

            bluetooth_value = self._get_setting("global", "bluetooth_on")
            is_on = self._parse_boolean_setting(bluetooth_value)

            log.info(f"蓝牙状态: {'开启' if is_on else '关闭'} (值: {bluetooth_value})")
            return is_on

        except Exception as e:
            log.error(f"检查蓝牙状态失败: {e}")
            return False

    def check_active_halo_lighting_status(self) -> bool:
        """
        检查系统 Active Halo Lighting 状态

        Returns:
            bool: Active Halo Lighting 是否已开启
        """
        try:
            log.info("检查 Active Halo Lighting 状态")

            # 方法1: 优先检查标准的 "active halo light" 设置
            primary_settings = [
                ("system", "active_halo_light"),
                ("secure", "active_halo_light"),
                ("global", "active_halo_light")
            ]

            found, namespace, setting = self._check_settings_batch(primary_settings, "1")
            if found:
                log.info(f"Active Halo Lighting 状态: 开启 (通过{namespace}.{setting}检测)")
                return True

            # 检查是否明确关闭
            found_off, namespace_off, setting_off = self._check_settings_batch(primary_settings, "0")
            if found_off:
                log.info(f"Active Halo Lighting 状态: 关闭 (通过{namespace_off}.{setting_off}检测)")
                return False

            # 方法2: 检查更多可能的设置名称（包括TECNO等品牌的实现）
            extended_settings = [
                ("system", "INCOMING_CALL_FLASH_LIGHT"),  # TECNO的来电闪光灯
                ("system", "breathing_light"),
                ("system", "breathing_light_enabled"),
                ("secure", "flashlight_enabled"),  # 手电筒状态
                ("system", "led_indicator"),
                ("system", "led_indicator_enabled")
            ]

            found_ext, namespace_ext, setting_ext = self._check_settings_batch(extended_settings, "1")
            if found_ext:
                log.info(f"Active Halo Lighting 状态: 开启 (通过{namespace_ext}.{setting_ext}检测)")
                return True

            # 方法3: 检查其他可能的 Halo Lighting 设置名称
            alternative_settings = [
                ("system", "halo_lighting_enabled"),
                ("system", "active_halo_lighting"),
                ("system", "notification_halo_light"),
                ("system", "halo_light_enable"),
                ("secure", "halo_lighting_enabled"),
                ("secure", "active_halo_lighting_enabled")
            ]

            found_alt, namespace_alt, setting_alt = self._check_settings_batch(alternative_settings, "1")
            if found_alt:
                log.info(f"Active Halo Lighting 状态: 开启 (通过{namespace_alt}.{setting_alt}检测)")
                return True

            # 方法4: 检查通知LED脉冲作为 Active Halo Light 的指标
            pulse_value = self._get_setting("system", "notification_light_pulse")
            if self._parse_boolean_setting(pulse_value):
                log.info("Active Halo Lighting 状态: 开启 (通过notification_light_pulse检测)")
                return True

            # 方法5: 检查系统属性中的 halo lighting
            halo_properties = [
                "persist.vendor.halo.lighting",
                "ro.vendor.halo_lighting.support",
                "persist.sys.halo_light",
                "vendor.halo.lighting.enable",
                "persist.sys.active_halo_light"
            ]

            for prop in halo_properties:
                prop_value = self._get_system_property(prop)
                if prop_value and prop_value in ["1", "true", "enabled"]:
                    log.info(f"Active Halo Lighting 状态: 开启 (通过系统属性{prop}检测)")
                    return True

            log.info("Active Halo Lighting 状态: 关闭 (所有检测方法均未发现开启状态)")
            return False

        except Exception as e:
            log.error(f"检查 Active Halo Lighting 状态失败: {e}")
            return False

    def _check_halo_lighting_device_support(self) -> bool:
        """
        检查设备是否支持 Active Halo Lighting

        Returns:
            bool: 设备是否支持 Active Halo Lighting
        """
        try:
            log.info("检查设备 Active Halo Lighting 支持")

            # 检查设备品牌和型号
            brand_result = subprocess.run(
                ["adb", "shell", "getprop", "ro.product.brand"],
                capture_output=True,
                text=True,
                timeout=3
            )

            model_result = subprocess.run(
                ["adb", "shell", "getprop", "ro.product.model"],
                capture_output=True,
                text=True,
                timeout=3
            )

            if brand_result.returncode == 0 and model_result.returncode == 0:
                brand = brand_result.stdout.strip().lower()
                model = model_result.stdout.strip().lower()

                log.info(f"设备品牌: {brand}, 型号: {model}")

                # 检查是否是支持 Active Halo Lighting 的品牌和型号
                supported_brands = ["infinix", "tecno"]  # 添加 TECNO 支持
                supported_models = ["note 40", "note40", "cm8"]  # 添加 CM8 支持

                if brand in supported_brands:
                    log.info(f"检测到可能支持 Active Halo Lighting 的品牌: {brand}")

                    # 进一步检查型号
                    if any(supported_model in model for supported_model in supported_models):
                        log.info(f"检测到可能支持 Active Halo Lighting 的型号: {model}")
                        return True
                    else:
                        # 检查是否有 halo lighting 相关的系统属性
                        return self._check_halo_lighting_system_support()
                else:
                    # 即使品牌不在列表中，也检查系统支持
                    log.info(f"品牌 {brand} 不在已知支持列表中，检查系统支持")
                    return self._check_halo_lighting_system_support()

            return False

        except Exception as e:
            log.warning(f"检查设备 Active Halo Lighting 支持失败: {e}")
            return False

    def _check_halo_lighting_system_support(self) -> bool:
        """
        检查系统是否有 Halo Lighting 支持

        Returns:
            bool: 系统是否支持 Halo Lighting
        """
        try:
            # 检查系统属性中是否有 halo lighting 支持标识
            support_properties = [
                "ro.vendor.halo_lighting.support",
                "persist.vendor.halo.lighting.support",
                "ro.config.halo_lighting"
            ]

            for prop in support_properties:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        value = result.stdout.strip()
                        if value in ["1", "true", "enabled"]:
                            log.info(f"检测到系统支持 Active Halo Lighting (属性: {prop})")
                            return True
                except:
                    continue

            # 检查是否有任何 lighting 相关的设置（更宽松的检查）
            lighting_settings = [
                ("system", "halo_lighting_enabled"),
                ("system", "active_halo_lighting"),
                ("secure", "halo_lighting_enabled"),
                ("system", "notification_light_pulse"),  # 通知LED
                ("system", "INCOMING_CALL_FLASH_LIGHT"),  # TECNO来电闪光
                ("secure", "flashlight_enabled"),  # 手电筒
                ("system", "breathing_light_enabled")  # 呼吸灯
            ]

            for namespace, setting in lighting_settings:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "settings", "get", namespace, setting],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        value = result.stdout.strip()
                        if value and value != "null":
                            log.info(f"检测到 Lighting 相关设置存在: {namespace}.{setting} = {value}")
                            return True  # 只要有相关设置就认为支持
                except:
                    continue

            log.info("未检测到任何 Lighting 相关设置，但允许继续检测")
            return True  # 改为默认支持，让具体检测来判断

        except Exception as e:
            log.warning(f"检查 Halo Lighting 系统支持失败: {e}")
            return False

    def get_active_halo_lighting_detailed_status(self) -> dict:
        """
        获取 Active Halo Lighting 详细状态信息

        Returns:
            dict: Active Halo Lighting 详细状态信息
        """
        try:
            log.info("获取 Active Halo Lighting 详细状态")

            status_info = {
                'halo_lighting_enabled': False,
                'notification_led_enabled': False,
                'detection_method': None,
                'system_settings': {},
                'secure_settings': {},
                'system_properties': {},
                'supported_features': []
            }

            # 检查基本状态
            status_info['halo_lighting_enabled'] = self.check_active_halo_lighting_status()

            # 获取系统设置详细信息 (优先检查标准名称)
            system_settings = [
                "active_halo_light",  # 标准的开关名称
                "halo_lighting_enabled",
                "active_halo_lighting",
                "notification_halo_light",
                "halo_light_enable",
                "notification_light_pulse",
                "led_notification_enabled"
            ]

            for setting in system_settings:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "settings", "get", "system", setting],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        value = result.stdout.strip()
                        if value and value != "null":
                            status_info['system_settings'][setting] = value
                            if value == "1" and not status_info['detection_method']:
                                status_info['detection_method'] = f'SYSTEM_SETTINGS_{setting.upper()}'
                            log.info(f"系统设置 {setting}: {value}")
                except:
                    continue

            # 获取secure设置详细信息 (优先检查标准名称)
            secure_settings = [
                "active_halo_light",  # 标准的开关名称
                "halo_lighting_enabled",
                "active_halo_lighting_enabled",
                "notification_led_enabled"
            ]

            for setting in secure_settings:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "settings", "get", "secure", setting],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        value = result.stdout.strip()
                        if value and value != "null":
                            status_info['secure_settings'][setting] = value
                            if value == "1" and not status_info['detection_method']:
                                status_info['detection_method'] = f'SECURE_SETTINGS_{setting.upper()}'
                            log.info(f"安全设置 {setting}: {value}")
                except:
                    continue

            # 获取系统属性详细信息 (包含标准名称)
            halo_properties = [
                "persist.sys.active_halo_light",  # 标准的系统属性名称
                "persist.vendor.halo.lighting",
                "ro.vendor.halo_lighting.support",
                "persist.sys.halo_light",
                "vendor.halo.lighting.enable",
                "ro.config.notification_led",
                "persist.vendor.notification.led"
            ]

            for prop in halo_properties:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        value = result.stdout.strip()
                        if value and value not in ["", "null"]:
                            status_info['system_properties'][prop] = value
                            if value in ["1", "true", "enabled"] and not status_info['detection_method']:
                                status_info['detection_method'] = f'SYSTEM_PROPERTY_{prop.upper()}'
                            log.info(f"系统属性 {prop}: {value}")

                            # 检查支持的功能
                            if "support" in prop.lower() and value in ["1", "true"]:
                                status_info['supported_features'].append(prop)
                except:
                    continue

            # 检查通知LED状态
            if status_info['system_settings'].get('notification_light_pulse') == "1":
                status_info['notification_led_enabled'] = True

            # 如果没有检测方法，设置为备用方法
            if not status_info['detection_method']:
                status_info['detection_method'] = 'NO_DETECTION_METHOD'

            return status_info

        except Exception as e:
            log.error(f"获取 Active Halo Lighting 详细状态失败: {e}")
            return {
                'halo_lighting_enabled': False,
                'notification_led_enabled': False,
                'detection_method': 'ERROR',
                'system_settings': {},
                'secure_settings': {},
                'system_properties': {},
                'supported_features': []
            }

    def check_light_theme_status(self) -> bool:
        """
        检查系统light theme状态

        Returns:
            bool: 系统是否为浅色主题模式
        """
        try:
            log.info("检查系统主题状态")

            # 通过ADB命令检查UI夜间模式状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "secure", "ui_night_mode"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                night_mode_status = result.stdout.strip()
                log.info(f"UI夜间模式状态: (值: {night_mode_status})")

                # ui_night_mode值说明:
                # 0 = 关闭夜间模式 (浅色主题)
                # 1 = 开启夜间模式 (深色主题)
                # 2 = 自动模式 (跟随系统设置)
                # 空值或null = 未设置，通常默认为浅色主题

                if night_mode_status in ["0", "", "null"]:
                    is_light = True
                    theme_desc = "浅色主题"
                elif night_mode_status == "1":
                    # 设置为深色主题，但需要验证实际状态
                    actual_status = self._check_actual_theme_status()
                    if actual_status is not None:
                        is_light = actual_status
                        theme_desc = f"深色主题设置 ({'浅色' if is_light else '深色'}主题实际显示)"
                    else:
                        is_light = False
                        theme_desc = "深色主题"
                elif night_mode_status == "2":
                    # 自动模式需要进一步检查当前实际状态
                    is_light = self._check_auto_theme_current_status()
                    theme_desc = f"自动模式 ({'浅色' if is_light else '深色'}主题)"
                else:
                    # 未知值，默认认为是浅色主题
                    is_light = True
                    theme_desc = f"未知模式({night_mode_status})，默认浅色主题"

                log.info(f"系统主题状态: {theme_desc} (值: {night_mode_status})")
                return is_light
            else:
                log.error(f"获取系统主题状态失败: {result.stderr}")
                return True  # 默认返回浅色主题

        except Exception as e:
            log.error(f"检查系统主题状态失败: {e}")
            return True  # 默认返回浅色主题

    def _check_auto_theme_current_status(self) -> bool:
        """
        检查自动模式下的当前主题状态

        Returns:
            bool: 当前是否为浅色主题
        """
        try:
            log.info("检查自动模式下的当前主题状态")

            # 方法1: 通过UiModeManager检查当前夜间模式状态
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "uimode"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                uimode_output = result.stdout

                # 查找当前夜间模式状态
                import re
                # 查找mNightMode和mComputedNightMode
                night_mode_match = re.search(r'mNightMode=(\w+)', uimode_output)
                computed_night_match = re.search(r'mComputedNightMode=(\w+)', uimode_output)

                if computed_night_match:
                    computed_night = computed_night_match.group(1).lower()
                    log.info(f"计算出的夜间模式: {computed_night}")

                    if computed_night == "true":
                        log.info("自动模式当前状态: 深色主题 (通过mComputedNightMode检测)")
                        return False
                    elif computed_night == "false":
                        log.info("自动模式当前状态: 浅色主题 (通过mComputedNightMode检测)")
                        return True

                if night_mode_match:
                    night_mode = night_mode_match.group(1).lower()
                    log.info(f"当前夜间模式设置: {night_mode}")

                    if night_mode in ["no", "false", "0"]:
                        log.info("自动模式当前状态: 浅色主题")
                        return True
                    elif night_mode in ["yes", "true", "1"]:
                        log.info("自动模式当前状态: 深色主题")
                        return False
                    elif night_mode == "2":
                        # 如果mNightMode是2，检查是否有(yes)或(no)标记
                        night_mode_full_match = re.search(r'mNightMode=2\s*\((\w+)\)', uimode_output)
                        if night_mode_full_match:
                            night_status = night_mode_full_match.group(1).lower()
                            log.info(f"自动模式夜间状态: {night_status}")
                            if night_status == "yes":
                                log.info("自动模式当前状态: 深色主题 (通过mNightMode(yes)检测)")
                                return False
                            elif night_status == "no":
                                log.info("自动模式当前状态: 浅色主题 (通过mNightMode(no)检测)")
                                return True

                # 备用检查：查找Configuration信息
                config_match = re.search(r'Configuration.*?uiMode.*?0x(\w+)', uimode_output)
                if config_match:
                    ui_mode_hex = config_match.group(1)
                    try:
                        ui_mode_int = int(ui_mode_hex, 16)
                        # UI_MODE_NIGHT_MASK = 0x30
                        # UI_MODE_NIGHT_NO = 0x10 (浅色主题)
                        # UI_MODE_NIGHT_YES = 0x20 (深色主题)
                        night_mode_bits = ui_mode_int & 0x30
                        if night_mode_bits == 0x10:
                            log.info("自动模式当前状态: 浅色主题 (通过Configuration检测)")
                            return True
                        elif night_mode_bits == 0x20:
                            log.info("自动模式当前状态: 深色主题 (通过Configuration检测)")
                            return False
                    except ValueError:
                        pass

            # 方法2: 通过系统属性检查
            result = subprocess.run(
                ["adb", "shell", "getprop", "ro.config.ui_night_mode"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                night_mode_prop = result.stdout.strip()
                if night_mode_prop:
                    log.info(f"系统属性夜间模式: {night_mode_prop}")
                    if night_mode_prop in ["0", "false", "no"]:
                        return True
                    elif night_mode_prop in ["1", "true", "yes"]:
                        return False

            # 方法3: 通过Activity Manager检查当前Configuration
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "configuration"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                config_output = result.stdout
                # 查找当前Configuration中的UI模式
                config_match = re.search(r'uiMode=0x(\w+)', config_output)
                if config_match:
                    ui_mode_hex = config_match.group(1)
                    try:
                        ui_mode_int = int(ui_mode_hex, 16)
                        night_mode_bits = ui_mode_int & 0x30
                        if night_mode_bits == 0x10:
                            log.info("自动模式当前状态: 浅色主题 (通过Activity Configuration检测)")
                            return True
                        elif night_mode_bits == 0x20:
                            log.info("自动模式当前状态: 深色主题 (通过Activity Configuration检测)")
                            return False
                    except ValueError:
                        pass

            log.info("自动模式当前状态: 无法确定，默认浅色主题")
            return True  # 默认返回浅色主题

        except Exception as e:
            log.warning(f"检查自动模式当前状态失败: {e}")
            return True  # 默认返回浅色主题

    def _check_actual_theme_status(self) -> bool:
        """
        检查实际的主题显示状态（不依赖设置值）

        Returns:
            bool: True表示浅色主题，False表示深色主题，None表示无法确定
        """
        try:
            log.info("检查实际主题显示状态")

            # 方法1: 通过UiModeManager检查mComputedNightMode
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "uimode"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                uimode_output = result.stdout

                import re
                # 检查计算出的夜间模式
                computed_night_match = re.search(r'mComputedNightMode=(\w+)', uimode_output)
                if computed_night_match:
                    computed_night = computed_night_match.group(1).lower()
                    log.info(f"实际计算的夜间模式: {computed_night}")

                    if computed_night == "false":
                        log.info("实际主题状态: 浅色主题 (通过mComputedNightMode检测)")
                        return True
                    elif computed_night == "true":
                        log.info("实际主题状态: 深色主题 (通过mComputedNightMode检测)")
                        return False

                # 检查当前UI模式
                ui_mode_match = re.search(r'mCurUiMode=0x(\w+)', uimode_output)
                if ui_mode_match:
                    ui_mode_hex = ui_mode_match.group(1)
                    try:
                        ui_mode_int = int(ui_mode_hex, 16)
                        night_mode_bits = ui_mode_int & 0x30
                        if night_mode_bits == 0x10:
                            log.info("实际主题状态: 浅色主题 (通过mCurUiMode检测)")
                            return True
                        elif night_mode_bits == 0x20:
                            log.info("实际主题状态: 深色主题 (通过mCurUiMode检测)")
                            return False
                    except ValueError:
                        pass

            # 方法2: 通过window displays检查状态栏外观
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "displays"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                window_output = result.stdout

                # 检查状态栏外观
                if "LIGHT_STATUS_BARS" in window_output:
                    log.info("实际主题状态: 浅色主题 (通过状态栏外观检测)")
                    return True
                elif "DARK_STATUS_BARS" in window_output:
                    log.info("实际主题状态: 深色主题 (通过状态栏外观检测)")
                    return False

            log.info("无法确定实际主题状态")
            return None

        except Exception as e:
            log.warning(f"检查实际主题状态失败: {e}")
            return None

    def get_light_theme_detailed_status(self) -> dict:
        """
        获取系统主题详细状态信息

        Returns:
            dict: 系统主题详细状态信息
        """
        try:
            log.info("获取系统主题详细状态")

            status_info = {
                'is_light_theme': True,
                'ui_night_mode': 0,
                'ui_night_mode_description': '浅色主题',
                'theme_mode': 'LIGHT',
                'auto_mode_enabled': False,
                'auto_mode_current_status': None,
                'detection_method': None,
                'system_properties': {},
                'configuration_info': {}
            }

            # 检查基本状态
            status_info['is_light_theme'] = self.check_light_theme_status()

            # 获取UI夜间模式详细信息
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "secure", "ui_night_mode"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                night_mode_status = result.stdout.strip()
                try:
                    mode_int = int(night_mode_status) if night_mode_status else 0
                    status_info['ui_night_mode'] = mode_int
                    status_info['detection_method'] = 'SETTINGS_SECURE'

                    mode_descriptions = {
                        0: '浅色主题 (关闭夜间模式)',
                        1: '深色主题 (开启夜间模式)',
                        2: '自动模式 (跟随系统设置)'
                    }
                    status_info['ui_night_mode_description'] = mode_descriptions.get(mode_int, f'未知模式({mode_int})')

                    if mode_int == 0:
                        status_info['theme_mode'] = 'LIGHT'
                    elif mode_int == 1:
                        status_info['theme_mode'] = 'DARK'
                    elif mode_int == 2:
                        status_info['theme_mode'] = 'AUTO'
                        status_info['auto_mode_enabled'] = True
                        # 获取自动模式下的当前状态
                        auto_status = self._check_auto_theme_current_status()
                        status_info['auto_mode_current_status'] = 'LIGHT' if auto_status else 'DARK'

                except ValueError:
                    status_info['ui_night_mode_description'] = f'无效值({night_mode_status})'

            # 获取UiModeManager详细信息
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "uimode"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0:
                    uimode_output = result.stdout
                    if not status_info['detection_method']:
                        status_info['detection_method'] = 'DUMPSYS_UIMODE'

                    # 解析UiMode信息
                    import re

                    # 查找当前夜间模式状态
                    night_mode_match = re.search(r'mNightMode=(\w+)', uimode_output)
                    if night_mode_match:
                        night_mode = night_mode_match.group(1)
                        status_info['configuration_info']['night_mode'] = night_mode

                    # 查找Configuration信息
                    config_match = re.search(r'Configuration.*?uiMode.*?0x(\w+)', uimode_output)
                    if config_match:
                        ui_mode_hex = config_match.group(1)
                        try:
                            ui_mode_int = int(ui_mode_hex, 16)
                            status_info['configuration_info']['ui_mode_hex'] = ui_mode_hex
                            status_info['configuration_info']['ui_mode_int'] = ui_mode_int

                            # 解析UI模式位
                            night_mode_bits = ui_mode_int & 0x30
                            if night_mode_bits == 0x10:
                                status_info['configuration_info']['night_mode_bits'] = 'LIGHT (0x10)'
                            elif night_mode_bits == 0x20:
                                status_info['configuration_info']['night_mode_bits'] = 'DARK (0x20)'
                            else:
                                status_info['configuration_info']['night_mode_bits'] = f'UNKNOWN (0x{night_mode_bits:02x})'
                        except ValueError:
                            pass

                    # 查找其他相关信息
                    car_mode_match = re.search(r'mCarModeEnabled=(\w+)', uimode_output)
                    if car_mode_match:
                        status_info['configuration_info']['car_mode_enabled'] = car_mode_match.group(1)

                    dock_state_match = re.search(r'mDockState=(\w+)', uimode_output)
                    if dock_state_match:
                        status_info['configuration_info']['dock_state'] = dock_state_match.group(1)

            except subprocess.TimeoutExpired:
                log.warning("dumpsys uimode命令超时")
            except Exception as e:
                log.warning(f"dumpsys uimode失败: {e}")

            # 获取相关系统属性
            theme_properties = [
                "ro.config.ui_night_mode",
                "persist.vendor.theme.mode",
                "ro.vendor.theme.default"
            ]

            for prop in theme_properties:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        prop_value = result.stdout.strip()
                        if prop_value and prop_value != "null":
                            status_info['system_properties'][prop] = prop_value
                            log.info(f"系统属性 {prop}: {prop_value}")
                except:
                    continue

            # 如果没有检测方法，设置为备用方法
            if not status_info['detection_method']:
                status_info['detection_method'] = 'SYSTEM_PROPERTIES_FALLBACK'

            return status_info

        except Exception as e:
            log.error(f"获取系统主题详细状态失败: {e}")
            return {
                'is_light_theme': True,
                'ui_night_mode': 0,
                'ui_night_mode_description': '错误',
                'theme_mode': 'UNKNOWN',
                'auto_mode_enabled': False,
                'auto_mode_current_status': None,
                'detection_method': 'ERROR',
                'system_properties': {},
                'configuration_info': {}
            }

    def check_location_status(self) -> bool:
        """
        检查位置服务状态

        Returns:
            bool: 位置服务是否已开启
        """
        try:
            log.info("检查位置服务状态")

            # 方法1: 通过ADB命令检查位置服务状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "secure", "location_mode"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                location_mode = result.stdout.strip()
                log.info(f"位置模式值: {location_mode}")
                # location_mode: 0=关闭, 1=仅设备, 2=省电模式, 3=高精度模式
                is_on = location_mode != "0" and location_mode != ""
                mode_desc = {
                    "0": "关闭",
                    "1": "仅设备",
                    "2": "省电模式",
                    "3": "高精度模式"
                }.get(location_mode, f"未知模式({location_mode})")
                log.info(f"位置服务状态: {'开启' if is_on else '关闭'} ({mode_desc})")
                return is_on
            else:
                log.warning(f"方法1获取位置服务状态失败: {result.stderr}")

            # 方法2: 通过providers_allowed检查位置服务
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "secure", "location_providers_allowed"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                providers = result.stdout.strip()
                log.info(f"位置提供者: {providers}")
                # 如果有任何位置提供者被允许，则认为位置服务开启
                is_on = providers != "" and providers != "null"
                log.info(f"位置服务状态: {'开启' if is_on else '关闭'} (通过providers检测)")
                return is_on
            else:
                log.warning(f"方法2获取位置服务状态失败: {result.stderr}")

            # 方法3: 通过LocationManager dumpsys检查
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "location"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                location_output = result.stdout
                # 检查位置服务是否启用
                if "Location is enabled" in location_output or "mLocationEnabled=true" in location_output:
                    log.info("位置服务状态: 开启 (通过dumpsys location检测)")
                    return True
                elif "Location is disabled" in location_output or "mLocationEnabled=false" in location_output:
                    log.info("位置服务状态: 关闭 (通过dumpsys location检测)")
                    return False
                else:
                    # 检查是否有活跃的位置提供者
                    if "gps" in location_output.lower() or "network" in location_output.lower():
                        log.info("位置服务状态: 开启 (检测到位置提供者)")
                        return True

            # 方法4: 通过系统属性检查位置服务
            location_properties = [
                "ro.config.location_enabled",
                "persist.vendor.location.enabled"
            ]

            for prop in location_properties:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        location_prop = result.stdout.strip()
                        if location_prop == "1" or location_prop.lower() == "true":
                            log.info(f"位置服务状态: 开启 (通过系统属性 {prop} 检测)")
                            return True
                        elif location_prop == "0" or location_prop.lower() == "false":
                            log.info(f"位置服务状态: 关闭 (通过系统属性 {prop} 检测)")
                            return False
                except:
                    continue

            log.info("位置服务状态: 关闭 (所有检测方法均未发现开启状态)")
            return False

        except Exception as e:
            log.error(f"检查位置服务状态失败: {e}")
            return False

    def get_location_detailed_status(self) -> dict:
        """
        获取位置服务详细状态信息

        Returns:
            dict: 位置服务详细状态信息
        """
        try:
            log.info("获取位置服务详细状态")

            status_info = {
                'location_enabled': False,
                'location_mode': 0,
                'location_mode_description': '关闭',
                'providers_allowed': [],
                'active_providers': [],
                'gps_enabled': False,
                'network_location_enabled': False,
                'passive_location_enabled': False,
                'detection_method': None,
                'last_known_location': None,
                'location_accuracy': None,
                'mock_location_enabled': False
            }

            # 检查基本状态
            status_info['location_enabled'] = self.check_location_status()

            # 获取位置模式详细信息
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "secure", "location_mode"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                location_mode = result.stdout.strip()
                try:
                    mode_int = int(location_mode) if location_mode else 0
                    status_info['location_mode'] = mode_int
                    status_info['detection_method'] = 'SETTINGS_SECURE'

                    mode_descriptions = {
                        0: '关闭',
                        1: '仅设备(GPS)',
                        2: '省电模式(网络)',
                        3: '高精度模式(GPS+网络)'
                    }
                    status_info['location_mode_description'] = mode_descriptions.get(mode_int, f'未知模式({mode_int})')
                except ValueError:
                    pass

            # 获取位置提供者信息
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "secure", "location_providers_allowed"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                providers = result.stdout.strip()
                if providers and providers != "null":
                    status_info['providers_allowed'] = providers.split(',')
                    log.info(f"允许的位置提供者: {status_info['providers_allowed']}")

                    # 检查具体的提供者类型
                    status_info['gps_enabled'] = 'gps' in providers.lower()
                    status_info['network_location_enabled'] = 'network' in providers.lower()
                    status_info['passive_location_enabled'] = 'passive' in providers.lower()

            # 通过dumpsys location获取详细信息
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "location"],
                    capture_output=True,
                    text=True,
                    timeout=15
                )

                if result.returncode == 0:
                    location_output = result.stdout
                    if not status_info['detection_method']:
                        status_info['detection_method'] = 'DUMPSYS_LOCATION'

                    # 解析活跃的位置提供者
                    import re

                    # 查找活跃的提供者
                    provider_patterns = [
                        r'Provider\s+(\w+).*?enabled=true',
                        r'(\w+)\s+provider.*?enabled',
                        r'Active.*?(\w+).*?provider'
                    ]

                    for pattern in provider_patterns:
                        matches = re.findall(pattern, location_output, re.IGNORECASE)
                        if matches:
                            status_info['active_providers'].extend(matches)

                    # 去重
                    status_info['active_providers'] = list(set(status_info['active_providers']))

                    # 查找最后已知位置信息
                    location_matches = re.findall(r'last known location.*?lat.*?([-\d.]+).*?lon.*?([-\d.]+)', location_output, re.IGNORECASE)
                    if location_matches:
                        lat, lon = location_matches[0]
                        status_info['last_known_location'] = {'latitude': lat, 'longitude': lon}

                    # 查找位置精度信息
                    accuracy_matches = re.findall(r'accuracy.*?(\d+\.?\d*)', location_output, re.IGNORECASE)
                    if accuracy_matches:
                        status_info['location_accuracy'] = f"{accuracy_matches[0]}m"

                    # 检查模拟位置
                    if 'mock' in location_output.lower() or 'test' in location_output.lower():
                        mock_matches = re.findall(r'mock.*?enabled.*?(true|false)', location_output, re.IGNORECASE)
                        if mock_matches:
                            status_info['mock_location_enabled'] = mock_matches[0].lower() == 'true'

            except subprocess.TimeoutExpired:
                log.warning("dumpsys location命令超时")
            except Exception as e:
                log.warning(f"dumpsys location失败: {e}")

            # 检查模拟位置应用设置
            try:
                result = subprocess.run(
                    ["adb", "shell", "settings", "get", "secure", "mock_location"],
                    capture_output=True,
                    text=True,
                    timeout=3
                )

                if result.returncode == 0:
                    mock_setting = result.stdout.strip()
                    if mock_setting == "1":
                        status_info['mock_location_enabled'] = True
                        log.info("检测到模拟位置已启用")

            except:
                pass

            # 如果dumpsys失败，尝试通过其他方法获取基本信息
            if not status_info['detection_method']:
                status_info['detection_method'] = 'SETTINGS_FALLBACK'

            return status_info

        except Exception as e:
            log.error(f"获取位置服务详细状态失败: {e}")
            return {
                'location_enabled': False,
                'location_mode': 0,
                'location_mode_description': '错误',
                'providers_allowed': [],
                'active_providers': [],
                'gps_enabled': False,
                'network_location_enabled': False,
                'passive_location_enabled': False,
                'detection_method': 'ERROR',
                'last_known_location': None,
                'location_accuracy': None,
                'mock_location_enabled': False
            }

    def check_mobile_data_status(self) -> bool:
        """
        检查移动数据状态 (支持双卡)

        Returns:
            bool: 移动数据是否已开启且可用
        """
        try:
            log.info("检查移动数据状态 (双卡支持)")

            # 1. 检查移动数据开关状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "mobile_data"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode != 0:
                log.error(f"获取移动数据设置失败: {result.stderr}")
                return False

            mobile_data_setting = result.stdout.strip()
            log.info(f"移动数据设置: (值: {mobile_data_setting})")

            if mobile_data_setting != "1":
                log.info("移动数据状态: 关闭 (设置未开启)")
                return False

            # 2. 检查双卡SIM卡状态
            sim_available = False

            # 检查主SIM卡状态
            sim_result = subprocess.run(
                ["adb", "shell", "getprop", "gsm.sim.state"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if sim_result.returncode == 0:
                sim_state = sim_result.stdout.strip()
                log.info(f"双卡SIM状态: {sim_state}")

                # 解析双卡状态 (格式: "LOADED,ABSENT" 或 "READY,READY")
                sim_states = sim_state.split(',')
                for i, state in enumerate(sim_states):
                    state = state.strip()
                    if state in ["READY", "LOADED"] and state != "ABSENT":
                        sim_available = True
                        log.info(f"卡槽{i+1} SIM卡可用: {state}")
                    elif state == "ABSENT":
                        log.info(f"卡槽{i+1} SIM卡未插入")
                    else:
                        log.info(f"卡槽{i+1} SIM卡状态: {state}")

            if not sim_available:
                log.info("移动数据状态: 不可用 (所有SIM卡槽均无可用卡)")
                return False

            # 3. 检查telephony.registry中的用户移动数据状态
            telephony_result = subprocess.run(
                ["adb", "shell", "dumpsys", "telephony.registry"],
                capture_output=True,
                text=True,
                timeout=15,
                encoding='utf-8',
                errors='ignore'
            )

            if telephony_result.returncode == 0 and telephony_result.stdout:
                telephony_output = telephony_result.stdout

                # 检查每个Phone ID的用户移动数据状态
                import re
                user_data_states = re.findall(r'mUserMobileDataState=(\w+)', telephony_output)
                data_enabled_states = re.findall(r'mIsDataEnabled=(\w+)', telephony_output)

                log.info(f"用户移动数据状态: {user_data_states}")
                log.info(f"数据启用状态: {data_enabled_states}")

                # 如果任一卡槽的用户移动数据状态为true，则认为移动数据可用
                if 'true' in user_data_states or 'true' in data_enabled_states:
                    log.info("移动数据状态: 开启 (telephony检测)")
                    return True
                else:
                    log.info("移动数据状态: 关闭 (用户未启用)")
                    return False

            # 4. 备用检查：connectivity状态
            connectivity_result = subprocess.run(
                ["adb", "shell", "dumpsys", "connectivity"],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )

            if connectivity_result.returncode == 0 and connectivity_result.stdout:
                connectivity_output = connectivity_result.stdout
                if "MOBILE" in connectivity_output and "CONNECTED" in connectivity_output:
                    log.info("移动数据状态: 开启且已连接 (connectivity检测)")
                    return True

            log.info("移动数据状态: 关闭 (综合判断)")
            return False

        except Exception as e:
            log.error(f"检查移动数据状态失败: {e}")
            return False

    def check_wifi_status(self) -> bool:
        """
        检查WiFi状态

        Returns:
            bool: WiFi是否已开启
        """
        try:
            log.info("检查WiFi状态")

            # 方法1: 通过ADB命令检查WiFi状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "wifi_on"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                wifi_status = self._safe_strip(result.stdout)
                is_on = wifi_status == "1"
                log.info(f"WiFi状态: {'开启' if is_on else '关闭'} (值: {wifi_status})")
                return is_on
            else:
                stderr_msg = self._safe_strip(result.stderr)
                log.warning(f"方法1获取WiFi状态失败: {stderr_msg}")

            # 方法2: 通过dumpsys wifi检查WiFi状态
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "wifi"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                wifi_output = result.stdout
                # 检查WiFi是否启用
                if "Wi-Fi is enabled" in wifi_output or "mWifiEnabled: true" in wifi_output:
                    log.info("WiFi状态: 开启 (通过dumpsys检测)")
                    return True
                elif "Wi-Fi is disabled" in wifi_output or "mWifiEnabled: false" in wifi_output:
                    log.info("WiFi状态: 关闭 (通过dumpsys检测)")
                    return False
                else:
                    log.warning("无法从dumpsys输出中确定WiFi状态")

            # 方法3: 通过网络连接状态检查
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "connectivity"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                connectivity_output = result.stdout
                if "WIFI" in connectivity_output and "CONNECTED" in connectivity_output:
                    log.info("WiFi状态: 开启且已连接 (通过connectivity检测)")
                    return True

            log.warning("所有方法都无法确定WiFi状态，默认返回False")
            return False

        except Exception as e:
            log.error(f"检查WiFi状态失败: {e}")
            return False

    def check_flashlight_status(self) -> bool:
        """
        检查手电筒状态 - 优化版本

        Returns:
            bool: 手电筒是否已开启
        """
        try:
            log.info("检查手电筒状态")

            # 方法1: 优先检查LED硬件状态（最直接准确的方法）
            led_paths = [
                "/sys/class/leds/torch/brightness",
                "/sys/class/leds/flashlight/brightness",
                "/sys/class/leds/led:torch_0/brightness",
                "/sys/class/leds/led:torch_1/brightness",
                "/sys/class/leds/white:flash/brightness",
                "/sys/class/leds/torch-light0/brightness",
                "/sys/class/leds/torch-light1/brightness"
            ]

            for led_path in led_paths:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "cat", led_path],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        brightness = result.stdout.strip()
                        try:
                            brightness_value = int(brightness)
                            if brightness_value > 0:
                                log.info(f"手电筒状态: 开启 (LED路径 {led_path} 亮度值: {brightness_value})")
                                return True
                            else:
                                log.info(f"手电筒状态: 关闭 (LED路径 {led_path} 亮度值: {brightness_value})")
                                return False
                        except ValueError:
                            continue
                except:
                    continue

            # 方法2: 检查快捷设置状态
            try:
                result = subprocess.run(
                    ["adb", "shell", "settings", "get", "secure", "flashlight_enabled"],
                    capture_output=True,
                    text=True,
                    timeout=3
                )

                if result.returncode == 0:
                    flashlight_setting = result.stdout.strip()
                    if flashlight_setting == "1":
                        log.info("手电筒状态: 开启 (通过快捷设置检测)")
                        return True
                    elif flashlight_setting == "0":
                        log.info("手电筒状态: 关闭 (通过快捷设置检测)")
                        return False
            except:
                pass

            # 方法3: 通过dumpsys media.camera检查手电筒状态（作为参考）
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "media.camera"],
                capture_output=True,
                text=True,
                timeout=10
            )

            torch_operation_lines = []
            if result.returncode == 0:
                camera_output = result.stdout

                # 解析最近的torch日志来判断当前状态
                if camera_output:
                    lines = camera_output.split('\n')
                    # 查找真正的torch操作日志（包含时间戳和turned on/off的行）
                    torch_operation_lines = [
                        line.strip() for line in lines
                        if 'torch for camera' in line.lower() and (
                                    'turned on' in line.lower() or 'turned off' in line.lower())
                    ]

                    if torch_operation_lines:
                        # 获取最近的torch操作（第一行，因为日志是按时间倒序排列的）
                        latest_torch_operation = torch_operation_lines[0]

                        if 'turned off' in latest_torch_operation.lower():
                            log.info(f"手电筒状态: 关闭 (通过media.camera检测到最近关闭: {latest_torch_operation})")
                            return False
                        elif 'turned on' in latest_torch_operation.lower():
                            # 如果日志显示开启，但前面的硬件检测都没有确认，可能是过期日志
                            log.warning(f"日志显示开启但硬件检测未确认: {latest_torch_operation}")
                            log.info("手电筒状态: 关闭 (综合判断：日志可能过期)")
                            return False
            else:
                log.warning(f"获取media.camera日志失败: {result.stderr}")

            # 方法4: 通过系统属性检查手电筒状态
            torch_properties = [
                "sys.camera.torch",
                "persist.vendor.camera.torch",
                "vendor.camera.torch.enable"
            ]

            for prop in torch_properties:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        torch_prop = result.stdout.strip()
                        if torch_prop == "1" or torch_prop.lower() == "true":
                            log.info(f"手电筒状态: 开启 (通过系统属性 {prop} 检测)")
                            return True
                        elif torch_prop == "0" or torch_prop.lower() == "false":
                            log.info(f"手电筒状态: 关闭 (通过系统属性 {prop} 检测)")
                            return False
                except:
                    continue

            log.info("手电筒状态: 关闭 (所有检测方法均未发现开启状态)")
            return False

        except Exception as e:
            log.error(f"检查手电筒状态失败: {e}")
            return False

    def get_flashlight_detailed_status(self) -> dict:
        """
        获取手电筒详细状态信息 - 优化版本

        Returns:
            dict: 手电筒详细状态信息
        """
        try:
            log.info("获取手电筒详细状态")

            status_info = {
                'flashlight_enabled': False,
                'brightness_level': 0,
                'torch_mode': 'OFF',
                'camera_in_use': False,
                'detection_method': None,
                'torch_logs': [],
                'led_path_found': None
            }

            # 检查基本状态
            status_info['flashlight_enabled'] = self.check_flashlight_status()

            # 获取详细的相机服务信息
            media_camera_result = subprocess.run(
                ["adb", "shell", "dumpsys", "media.camera"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if media_camera_result.returncode == 0:
                camera_output = media_camera_result.stdout
                if camera_output:
                    lines = camera_output.split('\n')
                    # 查找真正的torch操作日志
                    torch_operation_lines = [
                        line.strip() for line in lines
                        if 'torch for camera' in line.lower() and (
                                    'turned on' in line.lower() or 'turned off' in line.lower())
                    ]
                    status_info['torch_logs'] = torch_operation_lines[:5]  # 保存最近5条torch操作日志（前5条，因为是倒序）

                    if torch_operation_lines:
                        latest_torch_operation = torch_operation_lines[0]  # 第一条是最新的
                        if 'turned on' in latest_torch_operation.lower():
                            status_info['torch_mode'] = 'ON'
                            status_info['detection_method'] = 'MEDIA_CAMERA_LOG'
                        elif 'turned off' in latest_torch_operation.lower():
                            status_info['torch_mode'] = 'OFF'
                            status_info['detection_method'] = 'MEDIA_CAMERA_LOG'

            # 尝试多个LED路径获取亮度信息
            led_paths = [
                "/sys/class/leds/torch/brightness",
                "/sys/class/leds/flashlight/brightness",
                "/sys/class/leds/led:torch_0/brightness",
                "/sys/class/leds/led:torch_1/brightness",
                "/sys/class/leds/white:flash/brightness",
                "/sys/class/leds/torch-light0/brightness",
                "/sys/class/leds/torch-light1/brightness"
            ]

            for led_path in led_paths:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "cat", led_path],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        brightness = result.stdout.strip()
                        try:
                            brightness_value = int(brightness)
                            status_info['brightness_level'] = brightness_value
                            status_info['led_path_found'] = led_path
                            if not status_info['detection_method']:
                                status_info['detection_method'] = 'LED_BRIGHTNESS'
                            log.info(f"LED亮度级别: {brightness_value} (路径: {led_path})")

                            # 尝试获取最大亮度
                            max_path = led_path.replace('/brightness', '/max_brightness')
                            max_result = subprocess.run(
                                ["adb", "shell", "cat", max_path],
                                capture_output=True,
                                text=True,
                                timeout=3
                            )

                            if max_result.returncode == 0:
                                try:
                                    max_brightness = int(max_result.stdout.strip())
                                    status_info['max_brightness'] = max_brightness
                                    if brightness_value > 0:
                                        status_info['brightness_percentage'] = (brightness_value / max_brightness) * 100
                                        log.info(f"亮度百分比: {status_info['brightness_percentage']:.1f}%")
                                except ValueError:
                                    pass
                            break  # 找到有效路径就退出
                        except ValueError:
                            continue
                except:
                    continue

            # 检查相机服务状态（使用标准dumpsys camera作为备用）
            camera_result = subprocess.run(
                ["adb", "shell", "dumpsys", "camera"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if camera_result.returncode == 0 and camera_result.stdout.strip():
                camera_output = camera_result.stdout
                if "Camera in use" in camera_output or "camera_in_use: true" in camera_output:
                    status_info['camera_in_use'] = True
                    log.info("相机正在使用中")

                # 解析torch模式（如果之前没有检测到）
                if status_info['torch_mode'] == 'OFF':
                    if "torch_mode_on" in camera_output.lower() or "torch: on" in camera_output.lower():
                        status_info['torch_mode'] = 'ON'
                    elif "torch_mode_off" in camera_output.lower() or "torch: off" in camera_output.lower():
                        status_info['torch_mode'] = 'OFF'

            return status_info

        except Exception as e:
            log.error(f"获取手电筒详细状态失败: {e}")
            return {
                'flashlight_enabled': False,
                'brightness_level': 0,
                'torch_mode': 'UNKNOWN',
                'camera_in_use': False,
                'detection_method': 'ERROR',
                'torch_logs': [],
                'led_path_found': None
            }

    def check_wifi_connection_status(self) -> dict:
        """
        检查WiFi连接详细状态

        Returns:
            dict: WiFi连接状态信息
        """
        try:
            log.info("检查WiFi连接详细状态")

            status_info = {
                'wifi_enabled': False,
                'connected': False,
                'ssid': None,
                'signal_strength': None,
                'ip_address': None
            }

            # 检查WiFi是否启用
            status_info['wifi_enabled'] = self.check_wifi_status()

            if not status_info['wifi_enabled']:
                log.info("WiFi未启用")
                return status_info

            # 获取WiFi连接信息
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "wifi"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                wifi_output = result.stdout

                # 解析SSID
                import re
                ssid_match = re.search(r'SSID: "([^"]+)"', wifi_output)
                if ssid_match:
                    status_info['ssid'] = ssid_match.group(1)
                    status_info['connected'] = True
                    log.info(f"已连接到WiFi: {status_info['ssid']}")

                # 解析信号强度
                signal_match = re.search(r'RSSI: (-?\d+)', wifi_output)
                if signal_match:
                    status_info['signal_strength'] = int(signal_match.group(1))
                    log.info(f"信号强度: {status_info['signal_strength']} dBm")

            # 获取IP地址
            if status_info['connected']:
                ip_result = subprocess.run(
                    ["adb", "shell", "ip", "route", "get", "*******"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if ip_result.returncode == 0:
                    ip_output = ip_result.stdout
                    ip_match = re.search(r'src (\d+\.\d+\.\d+\.\d+)', ip_output)
                    if ip_match:
                        status_info['ip_address'] = ip_match.group(1)
                        log.info(f"IP地址: {status_info['ip_address']}")

            return status_info

        except Exception as e:
            log.error(f"检查WiFi连接状态失败: {e}")
            return {
                'wifi_enabled': False,
                'connected': False,
                'ssid': None,
                'signal_strength': None,
                'ip_address': None
            }

    def check_mobile_data_connection_status(self) -> dict:
        """
        检查移动数据连接详细状态

        Returns:
            dict: 移动数据连接状态信息
        """
        try:
            log.info("检查移动数据连接详细状态")

            status_info = {
                'mobile_data_enabled': False,
                'mobile_data_always_on': False,
                'connected': False,
                'network_type': None,
                'operator_name': None,
                'signal_strength': None,
                'data_usage_enabled': True,
                'sim_state': None,
                'sim_available': False,
                'dual_sim_info': {
                    'sim1': {'state': None, 'available': False, 'operator': None},
                    'sim2': {'state': None, 'available': False, 'operator': None}
                },
                'active_data_sim': None
            }

            # 检查移动数据是否启用
            status_info['mobile_data_enabled'] = self.check_mobile_data_status()

            # 检查双卡SIM卡状态
            sim_result = subprocess.run(
                ["adb", "shell", "getprop", "gsm.sim.state"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if sim_result.returncode == 0:
                sim_state = sim_result.stdout.strip()
                status_info['sim_state'] = sim_state

                # 解析双卡状态
                sim_states = sim_state.split(',')
                for i, state in enumerate(sim_states):
                    state = state.strip()
                    sim_key = f'sim{i+1}'
                    status_info['dual_sim_info'][sim_key]['state'] = state

                    if state in ["READY", "LOADED"] and state != "ABSENT":
                        status_info['dual_sim_info'][sim_key]['available'] = True
                        status_info['sim_available'] = True  # 至少有一张卡可用
                        log.info(f"卡槽{i+1} SIM卡状态: {state} (可用)")
                    else:
                        status_info['dual_sim_info'][sim_key]['available'] = False
                        log.info(f"卡槽{i+1} SIM卡状态: {state} (不可用)")

            # 获取运营商信息
            operator_props = [
                ("gsm.sim.operator.alpha", "sim1"),
                ("gsm.sim1.operator.alpha", "sim1"),
                ("gsm.sim2.operator.alpha", "sim2")
            ]

            for prop, sim_key in operator_props:
                try:
                    op_result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )
                    if op_result.returncode == 0 and op_result.stdout.strip():
                        operator = op_result.stdout.strip()
                        if operator and operator != "null":
                            status_info['dual_sim_info'][sim_key]['operator'] = operator
                            if not status_info['operator_name']:  # 设置主运营商
                                status_info['operator_name'] = operator
                            log.info(f"{sim_key} 运营商: {operator}")
                except:
                    continue

            # 检查移动数据始终开启设置
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "mobile_data_always_on"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                always_on_status = result.stdout.strip()
                status_info['mobile_data_always_on'] = always_on_status == "1"
                log.info(f"移动数据始终开启: {'是' if status_info['mobile_data_always_on'] else '否'}")

            # 检查当前活跃的数据SIM卡
            try:
                telephony_result = subprocess.run(
                    ["adb", "shell", "dumpsys", "telephony.registry"],
                    capture_output=True,
                    text=True,
                    timeout=15,
                    encoding='utf-8',
                    errors='ignore'
                )

                if telephony_result.returncode == 0 and telephony_result.stdout:
                    telephony_output = telephony_result.stdout

                    # 查找活跃数据订阅ID
                    import re
                    active_data_match = re.search(r'mActiveDataSubId=(\d+)', telephony_output)
                    if active_data_match:
                        active_sub_id = active_data_match.group(1)
                        status_info['active_data_sim'] = f"SubId_{active_sub_id}"
                        log.info(f"当前活跃数据SIM: SubId {active_sub_id}")

                    # 检查每个Phone ID的状态
                    phone_sections = re.split(r'Phone Id=(\d+)', telephony_output)
                    for i in range(1, len(phone_sections), 2):
                        phone_id = phone_sections[i]
                        phone_data = phone_sections[i+1] if i+1 < len(phone_sections) else ""

                        # 提取运营商信息
                        operator_match = re.search(r'mOperatorAlphaLong=([^,\n]+)', phone_data)
                        if operator_match:
                            operator = operator_match.group(1).strip()
                            if operator and operator != "null":
                                sim_key = f'sim{int(phone_id)+1}'
                                if sim_key in status_info['dual_sim_info']:
                                    status_info['dual_sim_info'][sim_key]['operator'] = operator
                                    log.info(f"Phone ID {phone_id} ({sim_key}) 运营商: {operator}")
            except Exception as e:
                log.warning(f"获取telephony详细信息失败: {e}")

            # 获取网络连接信息
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "telephony.registry"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    encoding='utf-8',
                    errors='ignore'
                )

                if result.returncode == 0 and result.stdout:
                    telephony_output = result.stdout

                    # 解析网络类型
                    import re
                    network_match = re.search(r'mDataConnectionNetworkType=(\d+)', telephony_output)
                    if network_match:
                        network_type_code = int(network_match.group(1))
                        network_types = {
                            0: 'UNKNOWN',
                            1: 'GPRS',
                            2: 'EDGE',
                            3: 'UMTS',
                            4: 'CDMA',
                            5: 'EVDO_0',
                            6: 'EVDO_A',
                            7: '1xRTT',
                            8: 'HSDPA',
                            9: 'HSUPA',
                            10: 'HSPA',
                            11: 'IDEN',
                            12: 'EVDO_B',
                            13: 'LTE',
                            14: 'EHRPD',
                            15: 'HSPAP',
                            16: 'GSM',
                            17: 'TD_SCDMA',
                            18: 'IWLAN',
                            19: 'LTE_CA',
                            20: 'NR'  # 5G
                        }
                        status_info['network_type'] = network_types.get(network_type_code, f'UNKNOWN({network_type_code})')
                        log.info(f"网络类型: {status_info['network_type']}")

                    # 解析运营商名称
                    operator_match = re.search(r'mServiceState.*?mOperatorAlphaLong=([^,\s]+)', telephony_output)
                    if operator_match:
                        status_info['operator_name'] = operator_match.group(1).strip('"')
                        log.info(f"运营商: {status_info['operator_name']}")

                    # 解析信号强度
                    signal_match = re.search(r'mSignalStrength.*?mGsmSignalStrength=(\d+)', telephony_output)
                    if signal_match:
                        signal_strength = int(signal_match.group(1))
                        # GSM信号强度转换为dBm (0-31 -> -113 to -51 dBm)
                        if 0 <= signal_strength <= 31:
                            signal_dbm = -113 + (signal_strength * 2)
                            status_info['signal_strength'] = signal_dbm
                            log.info(f"信号强度: {signal_dbm} dBm")
            except Exception as e:
                log.warning(f"获取telephony.registry信息失败: {e}")

            # 检查数据连接状态
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "connectivity"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    encoding='utf-8',
                    errors='ignore'
                )

                if result.returncode == 0 and result.stdout:
                    connectivity_output = result.stdout
                    if "MOBILE" in connectivity_output and "CONNECTED" in connectivity_output:
                        status_info['connected'] = True
                        log.info("移动数据: 已连接")
                    else:
                        log.info("移动数据: 未连接")
            except Exception as e:
                log.warning(f"获取connectivity信息失败: {e}")

            return status_info

        except Exception as e:
            log.error(f"检查移动数据连接状态失败: {e}")
            return {
                'mobile_data_enabled': False,
                'mobile_data_always_on': False,
                'connected': False,
                'network_type': None,
                'operator_name': None,
                'signal_strength': None,
                'data_usage_enabled': True,
                'sim_state': None,
                'sim_available': False,
                'dual_sim_info': {
                    'sim1': {'state': None, 'available': False, 'operator': None},
                    'sim2': {'state': None, 'available': False, 'operator': None}
                },
                'active_data_sim': None
            }

    def ensure_ella_process(self) -> bool:
        """
        确保当前进程是Ella应用

        Returns:
            bool: 当前是否在Ella进程
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False

            log.info("检查当前进程是否是Ella...")

            # 获取当前前台应用信息
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            current_activity = current_app.get('activity', '')

            log.info(f"当前应用: {current_package}")
            log.info(f"当前Activity: {current_activity}")

            # 检查是否是Ella应用
            ella_packages = [
                "com.transsion.aivoiceassistant",
                "com.transsion.ella"
            ]

            if current_package in ella_packages:
                log.info("✅ 当前在Ella应用进程")
                return True
            else:
                log.warning(f"❌ 当前不在Ella应用进程，当前包名: {current_package}")
                return False

        except Exception as e:
            log.error(f"检查Ella进程失败: {e}")
            return False

    def get_screen_brightness(self) -> int:
        """
        获取系统屏幕亮度值

        Returns:
            int: 屏幕亮度值 (0-255)，-1表示获取失败
        """
        try:
            log.info("获取系统屏幕亮度")

            # 方法1: 通过ADB命令获取屏幕亮度设置
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "system", "screen_brightness"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                brightness_str = result.stdout.strip()
                log.info(f"屏幕亮度设置值: {brightness_str}")
                try:
                    brightness_value = int(brightness_str)
                    if 0 <= brightness_value <= 255:
                        log.info(f"屏幕亮度: {brightness_value}/255 ({brightness_value/255*100:.1f}%)")
                        return brightness_value
                    else:
                        log.warning(f"亮度值超出范围: {brightness_value}")
                except ValueError:
                    log.warning(f"无法解析亮度值: {brightness_str}")
            else:
                log.warning(f"方法1获取屏幕亮度失败: {result.stderr}")

            # 方法2: 通过系统文件获取屏幕亮度
            brightness_paths = [
                "/sys/class/backlight/panel0-backlight/brightness",
                "/sys/class/backlight/backlight/brightness",
                "/sys/class/leds/lcd-backlight/brightness",
                "/sys/class/backlight/panel/brightness",
                "/sys/devices/platform/backlight/backlight/brightness"
            ]

            for brightness_path in brightness_paths:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "cat", brightness_path],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        brightness_str = result.stdout.strip()
                        try:
                            brightness_value = int(brightness_str)
                            log.info(f"屏幕亮度: {brightness_value} (通过系统文件 {brightness_path} 检测)")

                            # 尝试获取最大亮度来计算百分比
                            max_path = brightness_path.replace('/brightness', '/max_brightness')
                            max_result = subprocess.run(
                                ["adb", "shell", "cat", max_path],
                                capture_output=True,
                                text=True,
                                timeout=3
                            )

                            if max_result.returncode == 0:
                                try:
                                    max_brightness = int(max_result.stdout.strip())
                                    percentage = (brightness_value / max_brightness) * 100
                                    log.info(f"亮度百分比: {percentage:.1f}% (最大值: {max_brightness})")
                                    # 将硬件亮度值转换为标准0-255范围
                                    normalized_brightness = int((brightness_value / max_brightness) * 255)
                                    return normalized_brightness
                                except ValueError:
                                    pass

                            return brightness_value
                        except ValueError:
                            continue
                except:
                    continue

            log.warning("所有方法都无法获取屏幕亮度")
            return -1

        except Exception as e:
            log.error(f"获取屏幕亮度失败: {e}")
            return -1

    def get_system_volume(self, volume_type: str = "music") -> int:
        """
        获取系统音量值

        Args:
            volume_type (str): 音量类型，可选值：
                - "music": 媒体音量 (默认)
                - "ring": 铃声音量
                - "alarm": 闹钟音量
                - "notification": 通知音量
                - "system": 系统音量
                - "voice_call": 通话音量

        Returns:
            int: 音量值 (0-15或0-100，取决于系统)，-1表示获取失败
        """
        try:
            log.info(f"获取系统音量 - 类型: {volume_type}")

            # 方法1: 优先通过 service call 获取实时音量
            stream_type_map = {
                "music": 3,        # STREAM_MUSIC
                "ring": 2,         # STREAM_RING
                "alarm": 4,        # STREAM_ALARM
                "notification": 5, # STREAM_NOTIFICATION
                "system": 1,       # STREAM_SYSTEM
                "voice_call": 0    # STREAM_VOICE_CALL
            }

            stream_id = stream_type_map.get(volume_type, 3)

            try:
                # 使用 service call audio 25 获取当前音量
                result = subprocess.run(
                    ["adb", "shell", "service", "call", "audio", "25", "i32", str(stream_id)],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if result.returncode == 0:
                    output = result.stdout
                    log.info(f"service call audio 结果获取成功")

                    # 优化的 service call 结果解析
                    import re

                    # 方法1: 查找特定位置的音量值
                    # 从分析结果看，音量值可能在特定的数据结构中
                    lines = output.split('\n')
                    for line in lines:
                        if '0x' in line:
                            # 查找包含小数值的行
                            hex_values = re.findall(r'0x([0-9a-f]{8})', line)
                            for hex_val in hex_values:
                                try:
                                    dec_val = int(hex_val, 16)
                                    # 重点关注0-15范围的值（Android音量通常是0-15）
                                    if 1 <= dec_val <= 15:
                                        log.info(f"{volume_type}音量: {dec_val} (通过service call特定位置检测)")
                                        return dec_val
                                except ValueError:
                                    continue

                    # 方法2: 如果没找到1-15的值，查找0值或其他可能值
                    hex_matches = re.findall(r'0x([0-9a-f]{8})', output)
                    if hex_matches:
                        # 查找最后几个可能的音量值
                        for hex_val in hex_matches[-10:]:  # 查看最后10个值
                            try:
                                dec_val = int(hex_val, 16)
                                if 0 <= dec_val <= 15:
                                    log.info(f"{volume_type}音量: {dec_val} (通过service call后段检测)")
                                    return dec_val
                            except ValueError:
                                continue
            except Exception as e:
                log.warning(f"service call方法失败: {e}")

            # 方法2: 通过 dumpsys audio 直接查找 streamVolume
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "audio"],
                    capture_output=True,
                    text=True,
                    timeout=8
                )

                if result.returncode == 0:
                    audio_output = result.stdout

                    # 查找对应流的 streamVolume
                    stream_names = {
                        "music": "STREAM_MUSIC",
                        "ring": "STREAM_RING",
                        "alarm": "STREAM_ALARM",
                        "notification": "STREAM_NOTIFICATION",
                        "system": "STREAM_SYSTEM",
                        "voice_call": "STREAM_VOICE_CALL"
                    }

                    stream_name = stream_names.get(volume_type, "STREAM_MUSIC")

                    # 查找特定流的 streamVolume
                    import re
                    pattern = rf'{stream_name}:\s*\n.*?streamVolume:(\d+)'
                    match = re.search(pattern, audio_output, re.IGNORECASE | re.DOTALL)

                    if match:
                        volume_value = int(match.group(1))
                        log.info(f"{volume_type}音量: {volume_value} (通过dumpsys streamVolume检测)")
                        return volume_value

                    # 备用：查找所有 streamVolume，按流顺序匹配
                    stream_volumes = re.findall(r'streamVolume:(\d+)', audio_output)
                    if stream_volumes:
                        # 根据流ID获取对应的音量（MUSIC通常是索引6）
                        stream_indices = {
                            "voice_call": 0,
                            "system": 1,
                            "ring": 2,
                            "music": 6,  # 通常STREAM_MUSIC在第6个位置
                            "alarm": 4,
                            "notification": 5
                        }

                        stream_index = stream_indices.get(volume_type, 6)
                        if stream_index < len(stream_volumes):
                            volume_value = int(stream_volumes[stream_index])
                            log.info(f"{volume_type}音量: {volume_value} (通过dumpsys索引{stream_index}检测)")
                            return volume_value

            except Exception as e:
                log.warning(f"dumpsys audio方法失败: {e}")

            # 方法3: 通过ADB命令获取音量设置（备用方法）
            volume_settings_map = {
                "music": "volume_music",
                "ring": "volume_ring",
                "alarm": "volume_alarm",
                "notification": "volume_notification",
                "system": "volume_system",
                "voice_call": "volume_voice"
            }

            setting_key = volume_settings_map.get(volume_type, "volume_music")

            result = subprocess.run(
                ["adb", "shell", "settings", "get", "system", setting_key],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                volume_str = result.stdout.strip()
                log.info(f"{volume_type}音量设置值: {volume_str}")
                try:
                    volume_value = int(volume_str)
                    if 0 <= volume_value <= 100:  # 大部分系统音量范围是0-15，但有些可能到100
                        log.info(f"{volume_type}音量: {volume_value} (通过settings检测)")
                        return volume_value
                    else:
                        log.warning(f"音量值超出预期范围: {volume_value}")
                        return volume_value  # 仍然返回，可能是不同的音量范围
                except ValueError:
                    log.warning(f"无法解析音量值: {volume_str}")
            else:
                log.warning(f"方法2获取{volume_type}音量失败: {result.stderr}")

            # 方法3: 通过AudioManager dumpsys获取音量信息（优化解析）
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "audio"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                audio_output = result.stdout

                # 解析音量流信息
                import re

                # 查找对应的音频流类型
                stream_type_map = {
                    "music": ["STREAM_MUSIC", "Music"],
                    "ring": ["STREAM_RING", "Ring"],
                    "alarm": ["STREAM_ALARM", "Alarm"],
                    "notification": ["STREAM_NOTIFICATION", "Notification"],
                    "system": ["STREAM_SYSTEM", "System"],
                    "voice_call": ["STREAM_VOICE_CALL", "Voice"]
                }

                stream_names = stream_type_map.get(volume_type, ["STREAM_MUSIC"])

                for stream_name in stream_names:
                    # 优化的音量信息查找模式
                    patterns = [
                        rf'{stream_name}:\s*\n.*?streamVolume:(\d+)',  # 新增：查找streamVolume
                        rf'- {stream_name}:\s*\n.*?(\d+)',
                        rf'{stream_name}.*?volume.*?(\d+)',
                        rf'{stream_name}.*?current.*?(\d+)',
                        rf'{stream_name}.*?level.*?(\d+)',
                        rf'streamVolume:(\d+).*?{stream_name}',  # 反向查找
                    ]

                    for pattern in patterns:
                        matches = re.findall(pattern, audio_output, re.IGNORECASE | re.DOTALL)
                        if matches:
                            try:
                                volume_value = int(matches[0])
                                log.info(f"{volume_type}音量: {volume_value} (通过dumpsys audio检测 - {stream_name})")
                                return volume_value
                            except ValueError:
                                continue

                # 如果没找到特定流，尝试查找所有streamVolume值
                stream_volumes = re.findall(r'streamVolume:(\d+)', audio_output)
                if stream_volumes and volume_type == "music":
                    # 对于音乐流，通常是第一个或最大的值
                    try:
                        volume_value = int(stream_volumes[0])
                        log.info(f"{volume_type}音量: {volume_value} (通过streamVolume通用检测)")
                        return volume_value
                    except ValueError:
                        pass

            # 方法3: 通过媒体音量控制获取当前音量
            if volume_type == "music":
                try:
                    # 使用media session获取当前播放音量
                    result = subprocess.run(
                        ["adb", "shell", "dumpsys", "media_session"],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )

                    if result.returncode == 0:
                        media_output = result.stdout
                        # 查找音量信息
                        volume_matches = re.findall(r'volume.*?(\d+)', media_output, re.IGNORECASE)
                        if volume_matches:
                            try:
                                volume_value = int(volume_matches[0])
                                log.info(f"媒体音量: {volume_value} (通过media_session检测)")
                                return volume_value
                            except ValueError:
                                pass
                except:
                    pass

            # 方法4: 通过系统属性获取音量信息
            volume_properties = [
                f"persist.vendor.audio.{volume_type}.volume",
                f"ro.config.{volume_type}_volume",
                "persist.vendor.audio.volume",
                "ro.config.media_vol_default"
            ]

            for prop in volume_properties:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        volume_prop = result.stdout.strip()
                        if volume_prop and volume_prop != "":
                            try:
                                volume_value = int(volume_prop)
                                log.info(f"{volume_type}音量: {volume_value} (通过系统属性 {prop} 检测)")
                                return volume_value
                            except ValueError:
                                continue
                except:
                    continue

            # 方法5: 通过ALSA音频设备获取音量（如果可用）
            alsa_paths = [
                "/proc/asound/card0/codec#0",
                "/sys/class/sound/controlC0/volume",
                "/dev/snd/controlC0"
            ]

            for alsa_path in alsa_paths:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "cat", alsa_path],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        alsa_output = result.stdout
                        # 查找音量相关信息
                        volume_matches = re.findall(r'volume.*?(\d+)', alsa_output, re.IGNORECASE)
                        if volume_matches:
                            try:
                                volume_value = int(volume_matches[0])
                                log.info(f"{volume_type}音量: {volume_value} (通过ALSA设备 {alsa_path} 检测)")
                                return volume_value
                            except ValueError:
                                continue
                except:
                    continue

            log.warning(f"所有方法都无法获取{volume_type}音量")
            return -1

        except Exception as e:
            log.error(f"获取{volume_type}音量失败: {e}")
            return -1
    def get_media_volume(self, volume_type: str = "music") -> int:
        """
        获取系统音量值

        Args:
            volume_type (str): 音量类型，可选值：
                - "music": 媒体音量 (默认)
                - "ring": 铃声音量
                - "alarm": 闹钟音量
                - "notification": 通知音量
                - "system": 系统音量
                - "voice_call": 通话音量

        Returns:
            int: 音量值 (0-15或0-100，取决于系统)，-1表示获取失败
        """
        try:
            log.info(f"获取系统音量 - 类型: {volume_type}")

            # 方法1: 优先通过 service call 获取实时音量
            stream_type_map = {
                "music": 3,        # STREAM_MUSIC
                "ring": 2,         # STREAM_RING
                "alarm": 4,        # STREAM_ALARM
                "notification": 5, # STREAM_NOTIFICATION
                "system": 1,       # STREAM_SYSTEM
                "voice_call": 0    # STREAM_VOICE_CALL
            }

            stream_id = stream_type_map.get(volume_type, 3)

            try:
                # 使用 service call audio 25 获取当前音量
                result = subprocess.run(
                    ["adb", "shell", "service", "call", "audio", "25", "i32", str(stream_id)],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if result.returncode == 0:
                    output = result.stdout
                    log.info(f"service call audio 结果获取成功")

                    # 优化的 service call 结果解析
                    import re

                    # 方法1: 查找特定位置的音量值
                    # 从分析结果看，音量值可能在特定的数据结构中
                    lines = output.split('\n')
                    for line in lines:
                        if '0x' in line:
                            # 查找包含小数值的行
                            hex_values = re.findall(r'0x([0-9a-f]{8})', line)
                            for hex_val in hex_values:
                                try:
                                    dec_val = int(hex_val, 16)
                                    # 重点关注0-15范围的值（Android音量通常是0-15）
                                    if 1 <= dec_val <= 15:
                                        log.info(f"{volume_type}音量: {dec_val} (通过service call特定位置检测)")
                                        return dec_val
                                except ValueError:
                                    continue

                    # 方法2: 如果没找到1-15的值，查找0值或其他可能值
                    hex_matches = re.findall(r'0x([0-9a-f]{8})', output)
                    if hex_matches:
                        # 查找最后几个可能的音量值
                        for hex_val in hex_matches[-10:]:  # 查看最后10个值
                            try:
                                dec_val = int(hex_val, 16)
                                if 0 <= dec_val <= 15:
                                    log.info(f"{volume_type}音量: {dec_val} (通过service call后段检测)")
                                    return dec_val
                            except ValueError:
                                continue
            except Exception as e:
                log.warning(f"service call方法失败: {e}")

            # 方法2: 通过 dumpsys audio 直接查找 streamVolume
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "audio"],
                    capture_output=True,
                    text=True,
                    timeout=8
                )

                if result.returncode == 0:
                    audio_output = result.stdout

                    # 查找对应流的 streamVolume
                    stream_names = {
                        "music": "STREAM_MUSIC",
                        "ring": "STREAM_RING",
                        "alarm": "STREAM_ALARM",
                        "notification": "STREAM_NOTIFICATION",
                        "system": "STREAM_SYSTEM",
                        "voice_call": "STREAM_VOICE_CALL"
                    }

                    stream_name = stream_names.get(volume_type, "STREAM_MUSIC")

                    # 查找特定流的 streamVolume
                    import re
                    pattern = rf'{stream_name}:\s*\n.*?streamVolume:(\d+)'
                    match = re.search(pattern, audio_output, re.IGNORECASE | re.DOTALL)

                    if match:
                        volume_value = int(match.group(1))
                        log.info(f"{volume_type}音量: {volume_value} (通过dumpsys streamVolume检测)")
                        return volume_value

                    # 备用：查找所有 streamVolume，按流顺序匹配
                    stream_volumes = re.findall(r'streamVolume:(\d+)', audio_output)
                    if stream_volumes:
                        # 根据流ID获取对应的音量（MUSIC通常是索引6）
                        stream_indices = {
                            "voice_call": 0,
                            "system": 1,
                            "ring": 2,
                            "music": 6,  # 通常STREAM_MUSIC在第6个位置
                            "alarm": 4,
                            "notification": 5
                        }

                        stream_index = stream_indices.get(volume_type, 6)
                        if stream_index < len(stream_volumes):
                            volume_value = int(stream_volumes[stream_index])
                            log.info(f"{volume_type}音量: {volume_value} (通过dumpsys索引{stream_index}检测)")
                            return volume_value

            except Exception as e:
                log.warning(f"dumpsys audio方法失败: {e}")

            # 方法3: 通过ADB命令获取音量设置（备用方法）
            volume_settings_map = {
                "music": "volume_music",
                "ring": "volume_ring",
                "alarm": "volume_alarm",
                "notification": "volume_notification",
                "system": "volume_system",
                "voice_call": "volume_voice"
            }

            setting_key = volume_settings_map.get(volume_type, "volume_music")

            result = subprocess.run(
                ["adb", "shell", "settings", "get", "system", setting_key],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                volume_str = result.stdout.strip()
                log.info(f"{volume_type}音量设置值: {volume_str}")
                try:
                    volume_value = int(volume_str)
                    if 0 <= volume_value <= 100:  # 大部分系统音量范围是0-15，但有些可能到100
                        log.info(f"{volume_type}音量: {volume_value} (通过settings检测)")
                        return volume_value
                    else:
                        log.warning(f"音量值超出预期范围: {volume_value}")
                        return volume_value  # 仍然返回，可能是不同的音量范围
                except ValueError:
                    log.warning(f"无法解析音量值: {volume_str}")
            else:
                log.warning(f"方法2获取{volume_type}音量失败: {result.stderr}")

            # 方法3: 通过AudioManager dumpsys获取音量信息（优化解析）
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "audio"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                audio_output = result.stdout

                # 解析音量流信息
                import re

                # 查找对应的音频流类型
                stream_type_map = {
                    "music": ["STREAM_MUSIC", "Music"],
                    "ring": ["STREAM_RING", "Ring"],
                    "alarm": ["STREAM_ALARM", "Alarm"],
                    "notification": ["STREAM_NOTIFICATION", "Notification"],
                    "system": ["STREAM_SYSTEM", "System"],
                    "voice_call": ["STREAM_VOICE_CALL", "Voice"]
                }

                stream_names = stream_type_map.get(volume_type, ["STREAM_MUSIC"])

                for stream_name in stream_names:
                    # 优化的音量信息查找模式
                    patterns = [
                        rf'{stream_name}:\s*\n.*?streamVolume:(\d+)',  # 新增：查找streamVolume
                        rf'- {stream_name}:\s*\n.*?(\d+)',
                        rf'{stream_name}.*?volume.*?(\d+)',
                        rf'{stream_name}.*?current.*?(\d+)',
                        rf'{stream_name}.*?level.*?(\d+)',
                        rf'streamVolume:(\d+).*?{stream_name}',  # 反向查找
                    ]

                    for pattern in patterns:
                        matches = re.findall(pattern, audio_output, re.IGNORECASE | re.DOTALL)
                        if matches:
                            try:
                                volume_value = int(matches[0])
                                log.info(f"{volume_type}音量: {volume_value} (通过dumpsys audio检测 - {stream_name})")
                                return volume_value
                            except ValueError:
                                continue

                # 如果没找到特定流，尝试查找所有streamVolume值
                stream_volumes = re.findall(r'streamVolume:(\d+)', audio_output)
                if stream_volumes and volume_type == "music":
                    # 对于音乐流，通常是第一个或最大的值
                    try:
                        volume_value = int(stream_volumes[0])
                        log.info(f"{volume_type}音量: {volume_value} (通过streamVolume通用检测)")
                        return volume_value
                    except ValueError:
                        pass

            # 方法3: 通过媒体音量控制获取当前音量
            if volume_type == "music":
                try:
                    # 使用media session获取当前播放音量
                    result = subprocess.run(
                        ["adb", "shell", "dumpsys", "media_session"],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )

                    if result.returncode == 0:
                        media_output = result.stdout
                        # 查找音量信息
                        volume_matches = re.findall(r'volume.*?(\d+)', media_output, re.IGNORECASE)
                        if volume_matches:
                            try:
                                volume_value = int(volume_matches[0])
                                log.info(f"媒体音量: {volume_value} (通过media_session检测)")
                                return volume_value
                            except ValueError:
                                pass
                except:
                    pass

            # 方法4: 通过系统属性获取音量信息
            volume_properties = [
                f"persist.vendor.audio.{volume_type}.volume",
                f"ro.config.{volume_type}_volume",
                "persist.vendor.audio.volume",
                "ro.config.media_vol_default"
            ]

            for prop in volume_properties:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        volume_prop = result.stdout.strip()
                        if volume_prop and volume_prop != "":
                            try:
                                volume_value = int(volume_prop)
                                log.info(f"{volume_type}音量: {volume_value} (通过系统属性 {prop} 检测)")
                                return volume_value
                            except ValueError:
                                continue
                except:
                    continue

            # 方法5: 通过ALSA音频设备获取音量（如果可用）
            alsa_paths = [
                "/proc/asound/card0/codec#0",
                "/sys/class/sound/controlC0/volume",
                "/dev/snd/controlC0"
            ]

            for alsa_path in alsa_paths:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "cat", alsa_path],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        alsa_output = result.stdout
                        # 查找音量相关信息
                        volume_matches = re.findall(r'volume.*?(\d+)', alsa_output, re.IGNORECASE)
                        if volume_matches:
                            try:
                                volume_value = int(volume_matches[0])
                                log.info(f"{volume_type}音量: {volume_value} (通过ALSA设备 {alsa_path} 检测)")
                                return volume_value
                            except ValueError:
                                continue
                except:
                    continue

            log.warning(f"所有方法都无法获取{volume_type}音量")
            return -1

        except Exception as e:
            log.error(f"获取{volume_type}音量失败: {e}")
            return -1

    def get_alarm_volume(self) -> int:
        """
        优化版闹钟音量获取 - 基于get_system_volume_detailed_status逻辑

        Returns:
            int: 闹钟音量值 (0-15)，-1表示获取失败
        """
        try:
            log.info("获取闹钟音量")

            # 优先使用已优化的通用音量获取方法
            alarm_volume = self.get_system_volume("alarm")
            if alarm_volume != -1:
                log.info(f"闹钟音量: {alarm_volume}")
                return alarm_volume

            # 备用方法：直接从详细状态中获取
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status and volume_status.get('alarm_volume', -1) != -1:
                    alarm_vol = volume_status['alarm_volume']
                    log.info(f"闹钟音量: {alarm_vol} (通过详细状态获取)")
                    return alarm_vol
            except Exception as e:
                log.warning(f"通过详细状态获取闹钟音量失败: {e}")

            # 最后备用：settings方法
            try:
                result = subprocess.run(
                    ["adb", "shell", "settings", "get", "system", "volume_alarm"],
                    capture_output=True,
                    text=True,
                    timeout=3
                )

                if result.returncode == 0:
                    volume_str = result.stdout.strip()
                    if volume_str and volume_str != "null":
                        volume_value = int(volume_str)
                        log.info(f"闹钟音量: {volume_value} (通过settings备用方法)")
                        return volume_value
            except Exception as e:
                log.warning(f"settings备用方法获取闹钟音量失败: {e}")

            log.warning("所有方法都无法获取闹钟音量")
            return -1

        except Exception as e:
            log.error(f"获取闹钟音量失败: {e}")
            return -1

    def get_notification_volume(self) -> int:
        """
        获取通知音量值 - 基于get_alarm_volume逻辑优化

        Returns:
            int: 通知音量值 (0-15)，-1表示获取失败
        """
        try:
            log.info("获取通知音量")

            # 优先使用已优化的通用音量获取方法
            notification_volume = self.get_system_volume("notification")
            if notification_volume != -1:
                log.info(f"通知音量: {notification_volume}")
                return notification_volume

            # 备用方法：直接从详细状态中获取
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status and volume_status.get('notification_volume', -1) != -1:
                    notif_vol = volume_status['notification_volume']
                    log.info(f"通知音量: {notif_vol} (通过详细状态获取)")
                    return notif_vol
            except Exception as e:
                log.warning(f"通过详细状态获取通知音量失败: {e}")

            # 最后备用：settings方法
            notification_setting = self._get_setting("system", "volume_notification")
            if notification_setting:
                try:
                    volume_value = int(notification_setting)
                    log.info(f"通知音量: {volume_value} (通过settings备用方法)")
                    return volume_value
                except ValueError:
                    log.warning(f"无法解析通知音量值: {notification_setting}")

            log.warning("所有方法都无法获取通知音量")
            return -1

        except Exception as e:
            log.error(f"获取通知音量失败: {e}")
            return -1

    def get_notification_volume_percentage(self) -> int:
        """
        获取通知音量百分比 - 基于闹钟音量百分比逻辑

        Returns:
            int: 通知音量百分比 (0-100)，-1表示获取失败
        """
        try:
            # 优先从详细状态中获取已计算好的百分比
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status and 'volume_percentages' in volume_status:
                    notification_percentage = volume_status['volume_percentages'].get('notification')
                    if notification_percentage is not None:
                        percentage = int(notification_percentage)
                        log.info(f"通知音量百分比: {percentage}% (通过详细状态获取)")
                        return percentage
            except Exception as e:
                log.warning(f"通过详细状态获取通知音量百分比失败: {e}")

            # 备用方法：通过音量值计算
            notification_volume = self.get_notification_volume()
            if notification_volume == -1:
                return -1

            # 获取最大音量值（优先从详细状态获取，否则使用默认15）
            max_volume = 15
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status and 'max_volumes' in volume_status:
                    max_volume = volume_status['max_volumes'].get('notification', 15)
            except:
                pass

            percentage = int((notification_volume / max_volume) * 100)
            log.info(f"通知音量百分比: {percentage}% (计算得出: {notification_volume}/{max_volume})")
            return percentage

        except Exception as e:
            log.error(f"获取通知音量百分比失败: {e}")
            return -1

    def get_notification_volume_info(self) -> dict:
        """
        获取通知音量完整信息 - 基于闹钟音量完整信息逻辑

        Returns:
            dict: 通知音量完整信息
            {
                'volume': int,           # 音量值 (0-15)
                'percentage': int,       # 百分比 (0-100)
                'max_volume': int,       # 最大音量值
                'is_muted': bool,        # 是否静音
                'detection_method': str, # 检测方法
                'success': bool          # 是否成功获取
            }
        """
        try:
            log.info("获取通知音量完整信息")

            # 初始化返回结果
            notification_info = {
                'volume': -1,
                'percentage': -1,
                'max_volume': 15,
                'is_muted': False,
                'detection_method': 'UNKNOWN',
                'success': False
            }

            # 优先从详细状态中获取完整信息
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status:
                    # 获取音量值
                    notification_volume = volume_status.get('notification_volume', -1)
                    if notification_volume != -1:
                        notification_info['volume'] = notification_volume
                        notification_info['success'] = True
                        notification_info['detection_method'] = volume_status.get('detection_method', 'DETAILED_STATUS')

                    # 获取百分比
                    if 'volume_percentages' in volume_status:
                        notification_percentage = volume_status['volume_percentages'].get('notification')
                        if notification_percentage is not None:
                            notification_info['percentage'] = int(notification_percentage)

                    # 获取最大音量
                    if 'max_volumes' in volume_status:
                        max_vol = volume_status['max_volumes'].get('notification', 15)
                        notification_info['max_volume'] = max_vol

                    # 获取静音状态
                    if 'mute_status' in volume_status:
                        is_muted = volume_status['mute_status'].get('notification', False)
                        notification_info['is_muted'] = is_muted

                    # 如果成功获取了音量值，计算百分比（如果还没有的话）
                    if notification_info['volume'] != -1 and notification_info['percentage'] == -1:
                        notification_info['percentage'] = int((notification_info['volume'] / notification_info['max_volume']) * 100)

                    log.info(f"通知音量完整信息: {notification_info['volume']}/{notification_info['max_volume']} "
                           f"({notification_info['percentage']}%), 静音: {notification_info['is_muted']}")
                    return notification_info

            except Exception as e:
                log.warning(f"通过详细状态获取通知音量完整信息失败: {e}")

            # 备用方法：分别获取各项信息
            notification_volume = self.get_notification_volume()
            if notification_volume != -1:
                notification_info['volume'] = notification_volume
                notification_info['percentage'] = self.get_notification_volume_percentage()
                notification_info['detection_method'] = 'SEPARATE_CALLS'
                notification_info['success'] = True

                log.info(f"通知音量信息 (备用方法): {notification_info['volume']}/{notification_info['max_volume']} "
                       f"({notification_info['percentage']}%)")

            return notification_info

        except Exception as e:
            log.error(f"获取通知音量完整信息失败: {e}")
            return {
                'volume': -1,
                'percentage': -1,
                'max_volume': 15,
                'is_muted': False,
                'detection_method': 'ERROR',
                'success': False
            }

    def get_ring_volume(self) -> int:
        """
        获取铃声音量值 - 基于get_notification_volume逻辑优化

        Returns:
            int: 铃声音量值 (0-15)，-1表示获取失败
        """
        try:
            log.info("获取铃声音量")

            # 优先使用已优化的通用音量获取方法
            ring_volume = self.get_system_volume("ring")
            if ring_volume != -1:
                log.info(f"铃声音量: {ring_volume}")
                return ring_volume

            # 备用方法：直接从详细状态中获取
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status and volume_status.get('ring_volume', -1) != -1:
                    ring_vol = volume_status['ring_volume']
                    log.info(f"铃声音量: {ring_vol} (通过详细状态获取)")
                    return ring_vol
            except Exception as e:
                log.warning(f"通过详细状态获取铃声音量失败: {e}")

            # 最后备用：settings方法
            ring_setting = self._get_setting("system", "volume_ring")
            if ring_setting:
                try:
                    volume_value = int(ring_setting)
                    log.info(f"铃声音量: {volume_value} (通过settings备用方法)")
                    return volume_value
                except ValueError:
                    log.warning(f"无法解析铃声音量值: {ring_setting}")

            log.warning("所有方法都无法获取铃声音量")
            return -1

        except Exception as e:
            log.error(f"获取铃声音量失败: {e}")
            return -1

    def get_ring_volume_percentage(self) -> int:
        """
        获取铃声音量百分比 - 基于通知音量百分比逻辑

        Returns:
            int: 铃声音量百分比 (0-100)，-1表示获取失败
        """
        try:
            # 优先从详细状态中获取已计算好的百分比
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status and 'volume_percentages' in volume_status:
                    ring_percentage = volume_status['volume_percentages'].get('ring')
                    if ring_percentage is not None:
                        percentage = int(ring_percentage)
                        log.info(f"铃声音量百分比: {percentage}% (通过详细状态获取)")
                        return percentage
            except Exception as e:
                log.warning(f"通过详细状态获取铃声音量百分比失败: {e}")

            # 备用方法：通过音量值计算
            ring_volume = self.get_ring_volume()
            if ring_volume == -1:
                return -1

            # 获取最大音量值（优先从详细状态获取，否则使用默认15）
            max_volume = 15
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status and 'max_volumes' in volume_status:
                    max_volume = volume_status['max_volumes'].get('ring', 15)
            except:
                pass

            percentage = int((ring_volume / max_volume) * 100)
            log.info(f"铃声音量百分比: {percentage}% (计算得出: {ring_volume}/{max_volume})")
            return percentage

        except Exception as e:
            log.error(f"获取铃声音量百分比失败: {e}")
            return -1

    def get_ring_volume_info(self) -> dict:
        """
        获取铃声音量完整信息 - 基于通知音量完整信息逻辑

        Returns:
            dict: 铃声音量完整信息
            {
                'volume': int,           # 音量值 (0-15)
                'percentage': int,       # 百分比 (0-100)
                'max_volume': int,       # 最大音量值
                'is_muted': bool,        # 是否静音
                'detection_method': str, # 检测方法
                'success': bool          # 是否成功获取
            }
        """
        try:
            log.info("获取铃声音量完整信息")

            # 初始化返回结果
            ring_info = {
                'volume': -1,
                'percentage': -1,
                'max_volume': 15,
                'is_muted': False,
                'detection_method': 'UNKNOWN',
                'success': False
            }

            # 优先从详细状态中获取完整信息
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status:
                    # 获取音量值
                    ring_volume = volume_status.get('ring_volume', -1)
                    if ring_volume != -1:
                        ring_info['volume'] = ring_volume
                        ring_info['success'] = True
                        ring_info['detection_method'] = volume_status.get('detection_method', 'DETAILED_STATUS')

                    # 获取百分比
                    if 'volume_percentages' in volume_status:
                        ring_percentage = volume_status['volume_percentages'].get('ring')
                        if ring_percentage is not None:
                            ring_info['percentage'] = int(ring_percentage)

                    # 获取最大音量
                    if 'max_volumes' in volume_status:
                        max_vol = volume_status['max_volumes'].get('ring', 15)
                        ring_info['max_volume'] = max_vol

                    # 获取静音状态
                    if 'mute_status' in volume_status:
                        is_muted = volume_status['mute_status'].get('ring', False)
                        ring_info['is_muted'] = is_muted

                    # 如果成功获取了音量值，计算百分比（如果还没有的话）
                    if ring_info['volume'] != -1 and ring_info['percentage'] == -1:
                        ring_info['percentage'] = int((ring_info['volume'] / ring_info['max_volume']) * 100)

                    log.info(f"铃声音量完整信息: {ring_info['volume']}/{ring_info['max_volume']} "
                           f"({ring_info['percentage']}%), 静音: {ring_info['is_muted']}")
                    return ring_info

            except Exception as e:
                log.warning(f"通过详细状态获取铃声音量完整信息失败: {e}")

            # 备用方法：分别获取各项信息
            ring_volume = self.get_ring_volume()
            if ring_volume != -1:
                ring_info['volume'] = ring_volume
                ring_info['percentage'] = self.get_ring_volume_percentage()
                ring_info['detection_method'] = 'SEPARATE_CALLS'
                ring_info['success'] = True

                log.info(f"铃声音量信息 (备用方法): {ring_info['volume']}/{ring_info['max_volume']} "
                       f"({ring_info['percentage']}%)")

            return ring_info

        except Exception as e:
            log.error(f"获取铃声音量完整信息失败: {e}")
            return {
                'volume': -1,
                'percentage': -1,
                'max_volume': 15,
                'is_muted': False,
                'detection_method': 'ERROR',
                'success': False
            }

    def get_alarm_volume_percentage(self) -> int:
        """
        优化版闹钟音量百分比获取 - 基于详细状态逻辑

        Returns:
            int: 闹钟音量百分比 (0-100)，-1表示获取失败
        """
        try:
            # 优先从详细状态中获取已计算好的百分比
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status and 'volume_percentages' in volume_status:
                    alarm_percentage = volume_status['volume_percentages'].get('alarm')
                    if alarm_percentage is not None:
                        percentage = int(alarm_percentage)
                        log.info(f"闹钟音量百分比: {percentage}% (通过详细状态获取)")
                        return percentage
            except Exception as e:
                log.warning(f"通过详细状态获取闹钟音量百分比失败: {e}")

            # 备用方法：通过音量值计算
            alarm_volume = self.get_alarm_volume()
            if alarm_volume == -1:
                return -1

            # 获取最大音量值（优先从详细状态获取，否则使用默认15）
            max_volume = 15
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status and 'max_volumes' in volume_status:
                    max_volume = volume_status['max_volumes'].get('alarm', 15)
            except:
                pass

            percentage = int((alarm_volume / max_volume) * 100)
            log.info(f"闹钟音量百分比: {percentage}% (计算得出: {alarm_volume}/{max_volume})")
            return percentage

        except Exception as e:
            log.error(f"获取闹钟音量百分比失败: {e}")
            return -1

    def get_alarm_volume_info(self) -> dict:
        """
        获取闹钟音量完整信息 - 一次性获取所有相关数据

        Returns:
            dict: 闹钟音量完整信息
            {
                'volume': int,           # 音量值 (0-15)
                'percentage': int,       # 百分比 (0-100)
                'max_volume': int,       # 最大音量值
                'is_muted': bool,        # 是否静音
                'detection_method': str, # 检测方法
                'success': bool          # 是否成功获取
            }
        """
        try:
            log.info("获取闹钟音量完整信息")

            # 初始化返回结果
            alarm_info = {
                'volume': -1,
                'percentage': -1,
                'max_volume': 15,
                'is_muted': False,
                'detection_method': 'UNKNOWN',
                'success': False
            }

            # 优先从详细状态中获取完整信息
            try:
                volume_status = self.get_system_volume_detailed_status()
                if volume_status:
                    # 获取音量值
                    alarm_volume = volume_status.get('alarm_volume', -1)
                    if alarm_volume != -1:
                        alarm_info['volume'] = alarm_volume
                        alarm_info['success'] = True
                        alarm_info['detection_method'] = volume_status.get('detection_method', 'DETAILED_STATUS')

                    # 获取百分比
                    if 'volume_percentages' in volume_status:
                        alarm_percentage = volume_status['volume_percentages'].get('alarm')
                        if alarm_percentage is not None:
                            alarm_info['percentage'] = int(alarm_percentage)

                    # 获取最大音量
                    if 'max_volumes' in volume_status:
                        max_vol = volume_status['max_volumes'].get('alarm', 15)
                        alarm_info['max_volume'] = max_vol

                    # 获取静音状态
                    if 'mute_status' in volume_status:
                        is_muted = volume_status['mute_status'].get('alarm', False)
                        alarm_info['is_muted'] = is_muted

                    # 如果成功获取了音量值，计算百分比（如果还没有的话）
                    if alarm_info['volume'] != -1 and alarm_info['percentage'] == -1:
                        alarm_info['percentage'] = int((alarm_info['volume'] / alarm_info['max_volume']) * 100)

                    log.info(f"闹钟音量完整信息: {alarm_info['volume']}/{alarm_info['max_volume']} "
                           f"({alarm_info['percentage']}%), 静音: {alarm_info['is_muted']}")
                    return alarm_info

            except Exception as e:
                log.warning(f"通过详细状态获取闹钟音量完整信息失败: {e}")

            # 备用方法：分别获取各项信息
            alarm_volume = self.get_alarm_volume()
            if alarm_volume != -1:
                alarm_info['volume'] = alarm_volume
                alarm_info['percentage'] = self.get_alarm_volume_percentage()
                alarm_info['detection_method'] = 'SEPARATE_CALLS'
                alarm_info['success'] = True

                log.info(f"闹钟音量信息 (备用方法): {alarm_info['volume']}/{alarm_info['max_volume']} "
                       f"({alarm_info['percentage']}%)")

            return alarm_info

        except Exception as e:
            log.error(f"获取闹钟音量完整信息失败: {e}")
            return {
                'volume': -1,
                'percentage': -1,
                'max_volume': 15,
                'is_muted': False,
                'detection_method': 'ERROR',
                'success': False
            }

    def get_volume_max_value(self, volume_type: str = "music") -> int:
        """
        获取指定音量类型的最大值

        Args:
            volume_type (str): 音量类型

        Returns:
            int: 最大音量值，-1表示获取失败
        """
        try:
            log.info(f"获取{volume_type}音量最大值")

            # 音量类型映射到流类型
            stream_type_map = {
                "music": "STREAM_MUSIC",
                "ring": "STREAM_RING",
                "alarm": "STREAM_ALARM",
                "notification": "STREAM_NOTIFICATION",
                "system": "STREAM_SYSTEM",
                "voice_call": "STREAM_VOICE_CALL"
            }

            stream_name = stream_type_map.get(volume_type, "STREAM_MUSIC")

            # 方法1: 通过dumpsys audio获取最大音量
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "audio"],
                    capture_output=True,
                    text=True,
                    timeout=8
                )

                if result.returncode == 0:
                    audio_output = result.stdout

                    # 限制处理文本大小
                    if len(audio_output) > 50000:
                        audio_output = audio_output[:50000]

                    # 查找最大音量信息
                    import re

                    # 查找包含流类型和max的行
                    lines = audio_output.split('\n')
                    for line in lines:
                        if stream_name in line and ('max' in line.lower() or 'Max' in line):
                            # 提取数字
                            numbers = re.findall(r'\d+', line)
                            if numbers:
                                # 通常最大值是较大的数字
                                max_candidates = [int(n) for n in numbers if int(n) > 5]
                                if max_candidates:
                                    max_volume = max(max_candidates)
                                    log.info(f"{volume_type}最大音量: {max_volume} (通过dumpsys audio检测)")
                                    return max_volume

                    # 备用方法：查找音量范围模式
                    volume_range_patterns = [
                        rf'{stream_name}.*?(\d+)/(\d+)',  # 格式: 13/15
                        rf'{stream_name}.*?max.*?(\d+)',   # 格式: max 15
                        rf'{stream_name}.*?(\d+).*?max.*?(\d+)'  # 格式: current 13 max 15
                    ]

                    for pattern in volume_range_patterns:
                        matches = re.findall(pattern, audio_output, re.IGNORECASE)
                        if matches:
                            try:
                                if len(matches[0]) == 2:  # 两个数字的情况
                                    current, max_vol = matches[0]
                                    max_volume = int(max_vol)
                                    log.info(f"{volume_type}最大音量: {max_volume} (通过范围模式检测)")
                                    return max_volume
                                else:  # 一个数字的情况
                                    max_volume = int(matches[0])
                                    if max_volume > 10:  # 合理的最大值
                                        log.info(f"{volume_type}最大音量: {max_volume} (通过单值模式检测)")
                                        return max_volume
                            except (ValueError, IndexError):
                                continue

            except subprocess.TimeoutExpired:
                log.warning("dumpsys audio获取最大音量超时")
            except Exception as e:
                log.warning(f"dumpsys audio获取最大音量失败: {e}")

            # 方法2: 通过AudioManager获取最大音量
            try:
                # 使用AudioManager的getStreamMaxVolume方法
                stream_id_map = {
                    "music": "3",      # AudioManager.STREAM_MUSIC
                    "ring": "2",       # AudioManager.STREAM_RING
                    "alarm": "4",      # AudioManager.STREAM_ALARM
                    "notification": "5", # AudioManager.STREAM_NOTIFICATION
                    "system": "1",     # AudioManager.STREAM_SYSTEM
                    "voice_call": "0"  # AudioManager.STREAM_VOICE_CALL
                }

                stream_id = stream_id_map.get(volume_type, "3")

                # 通过shell命令调用AudioManager
                result = subprocess.run(
                    ["adb", "shell", "cmd", "media_session", "volume", "--show", "--stream", stream_id],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if result.returncode == 0:
                    output = result.stdout
                    # 查找最大音量信息
                    max_matches = re.findall(r'max.*?(\d+)', output, re.IGNORECASE)
                    if max_matches:
                        max_volume = int(max_matches[0])
                        log.info(f"{volume_type}最大音量: {max_volume} (通过media_session检测)")
                        return max_volume

            except Exception as e:
                log.warning(f"media_session获取最大音量失败: {e}")

            # 方法3: 根据音量类型返回常见的最大值
            common_max_values = {
                "music": 15,
                "ring": 7,
                "alarm": 7,
                "notification": 7,
                "system": 7,
                "voice_call": 7
            }

            default_max = common_max_values.get(volume_type, 15)
            log.info(f"{volume_type}最大音量: {default_max} (使用默认值)")
            return default_max

        except Exception as e:
            log.error(f"获取{volume_type}最大音量失败: {e}")
            return -1

    def get_volume_with_percentage(self, volume_type: str = "music") -> dict:
        """
        获取音量值及其百分比

        Args:
            volume_type (str): 音量类型

        Returns:
            dict: 包含当前音量、最大音量、百分比的信息
        """
        try:
            log.info(f"获取{volume_type}音量及百分比")

            result = {
                'current_volume': -1,
                'max_volume': -1,
                'percentage': 0.0,
                'is_max': False,
                'detection_method': None
            }

            # 获取当前音量
            current_volume = self.get_system_volume(volume_type)
            result['current_volume'] = current_volume

            if current_volume < 0:
                log.warning(f"无法获取{volume_type}当前音量")
                return result

            # 获取最大音量
            max_volume = self.get_volume_max_value(volume_type)
            result['max_volume'] = max_volume

            if max_volume > 0:
                # 计算百分比
                percentage = (current_volume / max_volume) * 100
                result['percentage'] = round(percentage, 1)
                result['is_max'] = (current_volume == max_volume)
                result['detection_method'] = 'CALCULATED'

                log.info(f"{volume_type}音量详情: {current_volume}/{max_volume} ({percentage:.1f}%) {'[最大]' if result['is_max'] else ''}")
            else:
                log.warning(f"无法获取{volume_type}最大音量，无法计算百分比")

            return result

        except Exception as e:
            log.error(f"获取{volume_type}音量百分比失败: {e}")
            return {
                'current_volume': -1,
                'max_volume': -1,
                'percentage': 0.0,
                'is_max': False,
                'detection_method': 'ERROR'
            }

    def get_system_volume_simple(self) -> dict:
        """
        获取系统音量简化版本 - 快速获取，避免卡死

        Returns:
            dict: 简化的音量信息
        """
        try:
            log.info("获取系统音量 (简化版本)")

            volume_info = {
                'music_volume': -1,
                'ring_volume': -1,
                'alarm_volume': -1,
                'notification_volume': -1,
                'detection_method': 'SETTINGS_SIMPLE',
                'success': False
            }

            # 只使用最简单可靠的方法获取音量
            volume_settings = {
                'music': 'volume_music',
                'ring': 'volume_ring',
                'alarm': 'volume_alarm',
                'notification': 'volume_notification'
            }

            success_count = 0
            for vol_type, setting_key in volume_settings.items():
                try:
                    result = subprocess.run(
                        ["adb", "shell", "settings", "get", "system", setting_key],
                        capture_output=True,
                        text=True,
                        timeout=2  # 很短的超时时间
                    )

                    if result.returncode == 0:
                        volume_str = result.stdout.strip()
                        try:
                            volume_value = int(volume_str)
                            volume_info[f'{vol_type}_volume'] = volume_value
                            success_count += 1
                            log.info(f"{vol_type}音量: {volume_value}")
                        except ValueError:
                            log.warning(f"无法解析{vol_type}音量: {volume_str}")
                except subprocess.TimeoutExpired:
                    log.warning(f"获取{vol_type}音量超时")
                except Exception as e:
                    log.warning(f"获取{vol_type}音量失败: {e}")

            volume_info['success'] = success_count > 0
            return volume_info

        except Exception as e:
            log.error(f"获取系统音量简化版本失败: {e}")
            return {
                'music_volume': -1,
                'ring_volume': -1,
                'alarm_volume': -1,
                'notification_volume': -1,
                'detection_method': 'ERROR',
                'success': False
            }

    def get_system_volume_detailed_status(self) -> dict:
        """
        获取系统音量详细状态信息

        Returns:
            dict: 系统音量详细状态信息
        """
        try:
            log.info("获取系统音量详细状态")

            status_info = {
                'music_volume': -1,
                'ring_volume': -1,
                'alarm_volume': -1,
                'notification_volume': -1,
                'system_volume': -1,
                'voice_call_volume': -1,
                'max_volumes': {},
                'volume_percentages': {},
                'mute_status': {},
                'detection_method': None,
                'audio_mode': None,
                'volume_steps': {},
                'current_audio_focus': None
            }

            # 优化：使用与get_system_volume相同的逻辑获取实时音量
            volume_types = ["music", "ring", "alarm", "notification", "system", "voice_call"]

            log.info("获取实时音量状态...")
            for vol_type in volume_types:
                try:
                    # 使用优化后的get_system_volume方法获取实时音量
                    volume_value = self.get_system_volume(vol_type)
                    status_info[f'{vol_type}_volume'] = volume_value
                    if volume_value > 0:
                        log.info(f"{vol_type}实时音量: {volume_value}")
                    else:
                        log.warning(f"{vol_type}音量获取失败或为0")
                except Exception as e:
                    log.warning(f"获取{vol_type}实时音量异常: {e}")
                    # 备用：尝试settings方法
                    try:
                        setting_key = f"volume_{vol_type}" if vol_type != "voice_call" else "volume_voice"
                        result = subprocess.run(
                            ["adb", "shell", "settings", "get", "system", setting_key],
                            capture_output=True,
                            text=True,
                            timeout=3
                        )
                        if result.returncode == 0:
                            volume_str = result.stdout.strip()
                            volume_value = int(volume_str)
                            status_info[f'{vol_type}_volume'] = volume_value
                            log.info(f"{vol_type}音量 (备用settings): {volume_value}")
                    except:
                        status_info[f'{vol_type}_volume'] = -1

            # 通过dumpsys audio获取详细信息 (优化：缩短超时时间，添加更多错误处理)
            log.info("尝试获取dumpsys audio信息...")
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "audio"],
                    capture_output=True,
                    text=True,
                    timeout=8  # 缩短超时时间从15秒到8秒
                )
            except subprocess.TimeoutExpired:
                log.warning("dumpsys audio命令超时，跳过详细信息获取")
                status_info['detection_method'] = 'SETTINGS_ONLY'
                return status_info
            except Exception as e:
                log.warning(f"dumpsys audio命令失败: {e}")
                status_info['detection_method'] = 'SETTINGS_ONLY'
                return status_info

            if result.returncode == 0:
                audio_output = result.stdout
                status_info['detection_method'] = 'OPTIMIZED_REALTIME_VOLUME'  # 标识使用了优化后的实时检测

                # 简化解析，避免复杂的正则表达式导致卡死
                import re

                # 限制处理的文本长度，避免过大的输出导致正则表达式卡死
                if len(audio_output) > 50000:  # 如果输出超过50KB，只处理前50KB
                    audio_output = audio_output[:50000]
                    log.warning("dumpsys audio输出过大，只处理前50KB")

                try:
                    # 解析音频模式 (简化)
                    if 'Audio mode:' in audio_output:
                        mode_match = re.search(r'Audio mode:\s*(\w+)', audio_output)
                        if mode_match:
                            status_info['audio_mode'] = mode_match.group(1)
                            log.info(f"音频模式: {status_info['audio_mode']}")

                    # 解析音频焦点 (简化)
                    if 'Audio Focus' in audio_output:
                        focus_lines = [line for line in audio_output.split('\n') if 'client=' in line]
                        if focus_lines:
                            # 取第一个匹配的客户端
                            focus_match = re.search(r'client=([^,\s]+)', focus_lines[0])
                            if focus_match:
                                status_info['current_audio_focus'] = focus_match.group(1).strip()
                                log.info(f"当前音频焦点: {status_info['current_audio_focus']}")

                    # 优化：使用与get_system_volume相同的streamVolume解析逻辑
                    stream_keywords = {
                        'music': 'STREAM_MUSIC',
                        'ring': 'STREAM_RING',
                        'alarm': 'STREAM_ALARM',
                        'notification': 'STREAM_NOTIFICATION',
                        'system': 'STREAM_SYSTEM',
                        'voice_call': 'STREAM_VOICE_CALL'
                    }

                    # 查找所有 streamVolume 值
                    stream_volumes = re.findall(r'streamVolume:(\d+)', audio_output)
                    log.info(f"找到 {len(stream_volumes)} 个streamVolume值: {stream_volumes}")

                    for vol_type, stream_name in stream_keywords.items():
                        # 方法1: 查找特定流的 streamVolume
                        pattern = rf'{stream_name}:\s*\n.*?streamVolume:(\d+)'
                        match = re.search(pattern, audio_output, re.IGNORECASE | re.DOTALL)

                        if match:
                            current_vol = int(match.group(1))
                            log.info(f"{vol_type}实时streamVolume: {current_vol}")

                            # 尝试获取最大音量（通常是15）
                            max_vol = 15  # Android默认最大音量

                            # 尝试从dumpsys中找到实际的最大音量
                            max_pattern = rf'{stream_name}.*?max.*?(\d+)'
                            max_match = re.search(max_pattern, audio_output, re.IGNORECASE)
                            if max_match:
                                try:
                                    max_vol = int(max_match.group(1))
                                except ValueError:
                                    pass

                            status_info['max_volumes'][vol_type] = max_vol
                            if max_vol > 0:
                                percentage = (current_vol / max_vol) * 100
                                status_info['volume_percentages'][vol_type] = round(percentage, 1)
                                log.info(f"{vol_type}音量详情: {current_vol}/{max_vol} ({percentage:.1f}%)")

                        # 方法2: 备用索引方法（如果方法1失败）
                        elif stream_volumes:
                            stream_indices = {
                                'voice_call': 0, 'system': 1, 'ring': 2,
                                'music': 6, 'alarm': 4, 'notification': 5
                            }

                            stream_index = stream_indices.get(vol_type, 6)
                            if stream_index < len(stream_volumes):
                                current_vol = int(stream_volumes[stream_index])
                                max_vol = 15
                                status_info['max_volumes'][vol_type] = max_vol
                                percentage = (current_vol / max_vol) * 100
                                status_info['volume_percentages'][vol_type] = round(percentage, 1)
                                log.info(f"{vol_type}音量详情(索引{stream_index}): {current_vol}/{max_vol} ({percentage:.1f}%)")

                except Exception as e:
                    log.warning(f"解析dumpsys audio输出时出错: {e}")
                    # 继续执行，不中断整个方法

                # 简化的静音状态解析
                try:
                    stream_keywords = {
                        'music': 'STREAM_MUSIC',
                        'ring': 'STREAM_RING',
                        'alarm': 'STREAM_ALARM',
                        'notification': 'STREAM_NOTIFICATION',
                        'system': 'STREAM_SYSTEM',
                        'voice_call': 'STREAM_VOICE_CALL'
                    }

                    for vol_type, keyword in stream_keywords.items():
                        if keyword in audio_output:
                            # 查找包含该关键字和muted的行
                            lines = [line for line in audio_output.split('\n')
                                   if keyword in line and 'muted' in line.lower()]
                            for line in lines:
                                if 'true' in line.lower():
                                    status_info['mute_status'][vol_type] = True
                                    log.info(f"{vol_type}静音状态: 静音")
                                    break
                                elif 'false' in line.lower():
                                    status_info['mute_status'][vol_type] = False
                                    log.info(f"{vol_type}静音状态: 未静音")
                                    break
                except Exception as e:
                    log.warning(f"解析静音状态时出错: {e}")

            # 如果dumpsys失败，尝试通过settings获取基本信息
            if not status_info['detection_method']:
                status_info['detection_method'] = 'SETTINGS_FALLBACK'

                # 尝试获取一些基本的音量设置
                basic_settings = [
                    ("volume_music_speaker", "music"),
                    ("volume_ring_speaker", "ring"),
                    ("volume_alarm_speaker", "alarm")
                ]

                for setting_key, vol_type in basic_settings:
                    try:
                        result = subprocess.run(
                            ["adb", "shell", "settings", "get", "system", setting_key],
                            capture_output=True,
                            text=True,
                            timeout=3
                        )

                        if result.returncode == 0 and result.stdout.strip():
                            volume_str = result.stdout.strip()
                            try:
                                volume_value = int(volume_str)
                                if status_info[f'{vol_type}_volume'] == -1:
                                    status_info[f'{vol_type}_volume'] = volume_value
                                    log.info(f"{vol_type}音量 (备用方法): {volume_value}")
                            except ValueError:
                                pass
                    except:
                        continue

            return status_info

        except Exception as e:
            log.error(f"获取系统音量详细状态失败: {e}")
            return {
                'music_volume': -1,
                'ring_volume': -1,
                'alarm_volume': -1,
                'notification_volume': -1,
                'system_volume': -1,
                'voice_call_volume': -1,
                'max_volumes': {},
                'volume_percentages': {},
                'mute_status': {},
                'detection_method': 'ERROR',
                'audio_mode': None,
                'volume_steps': {},
                'current_audio_focus': None
            }

    def check_music_volume_at_max(self) -> bool:
        """
        判断当前音乐实时streamVolume的值是否为最大值

        基于get_system_volume_detailed_status方法获取音量信息，
        比较音乐流的当前音量是否等于最大音量

        Returns:
            bool: True表示音乐音量为最大值，False表示不是最大值
        """
        try:
            log.info("检查音乐音量是否为最大值")

            # 获取详细的音量状态信息
            volume_status = self.get_system_volume_detailed_status()

            # 检查是否成功获取到音量信息
            if not volume_status or volume_status.get('detection_method') == 'ERROR':
                log.warning("无法获取音量状态信息，返回False")
                return False

            # 获取音乐流的当前音量
            current_music_volume = volume_status.get('music_volume', -1)
            if current_music_volume == -1:
                log.warning("无法获取当前音乐音量，返回False")
                return False

            # 方法1: 从max_volumes中获取最大音量
            max_volumes = volume_status.get('max_volumes', {})
            max_music_volume = max_volumes.get('music')

            if max_music_volume is not None and max_music_volume > 0:
                is_max = (current_music_volume == max_music_volume)
                log.info(f"音乐音量状态: {current_music_volume}/{max_music_volume} {'[最大音量]' if is_max else '[非最大音量]'}")
                return is_max

            # 方法2: 如果没有获取到最大音量，尝试单独获取
            try:
                max_volume = self.get_volume_max_value("music")
                if max_volume > 0:
                    is_max = (current_music_volume == max_volume)
                    log.info(f"音乐音量状态(备用方法): {current_music_volume}/{max_volume} {'[最大音量]' if is_max else '[非最大音量]'}")
                    return is_max
            except Exception as e:
                log.warning(f"备用方法获取最大音量失败: {e}")

            # 方法3: 使用Android默认最大音量15进行判断
            default_max_volume = 15
            is_max = (current_music_volume == default_max_volume)
            log.info(f"音乐音量状态(默认最大值): {current_music_volume}/{default_max_volume} {'[最大音量]' if is_max else '[非最大音量]'}")
            return is_max

        except Exception as e:
            log.error(f"检查音乐音量是否为最大值时发生错误: {e}")
            return False

    def check_do_not_disturb_status(self) -> bool:
        """
        检查Do Not Disturb（勿扰模式）状态

        Returns:
            bool: Do Not Disturb是否已开启
        """
        try:
            log.info("检查Do Not Disturb状态")

            # 方法1: 通过ADB命令检查勿扰模式设置
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "zen_mode"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                zen_mode = result.stdout.strip()
                log.info(f"Zen模式值: {zen_mode}")
                # zen_mode: 0=关闭, 1=优先级, 2=完全静音, 3=仅闹钟
                if zen_mode in ["1", "2", "3"]:
                    is_on = True
                    mode_desc = {
                        "1": "优先级模式",
                        "2": "完全静音",
                        "3": "仅闹钟"
                    }.get(zen_mode, "未知模式")
                    log.info(f"Do Not Disturb状态: 开启 ({mode_desc})")
                    return True
                elif zen_mode == "0":
                    log.info("Do Not Disturb状态: 关闭")
                    return False
                else:
                    log.warning(f"未知的zen_mode值: {zen_mode}")
            else:
                log.warning(f"方法1获取Do Not Disturb状态失败: {result.stderr}")

            # 方法2: 通过notification policy检查
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "notification"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                notification_output = result.stdout

                # 检查ZenMode状态
                if "mZenMode=" in notification_output:
                    import re
                    zen_match = re.search(r'mZenMode=(\d+)', notification_output)
                    if zen_match:
                        zen_value = zen_match.group(1)
                        if zen_value in ["1", "2", "3"]:
                            log.info(f"Do Not Disturb状态: 开启 (通过dumpsys检测，模式: {zen_value})")
                            return True
                        elif zen_value == "0":
                            log.info("Do Not Disturb状态: 关闭 (通过dumpsys检测)")
                            return False

                # 检查其他DND相关标识
                if "DND" in notification_output and "enabled" in notification_output.lower():
                    log.info("Do Not Disturb状态: 开启 (通过DND标识检测)")
                    return True

            # 方法3: 通过系统属性检查
            dnd_properties = [
                "persist.vendor.dnd.enable",
                "ro.config.dnd_enabled"
            ]

            for prop in dnd_properties:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "getprop", prop],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        dnd_prop = result.stdout.strip()
                        if dnd_prop == "1" or dnd_prop.lower() == "true":
                            log.info(f"Do Not Disturb状态: 开启 (通过系统属性 {prop} 检测)")
                            return True
                        elif dnd_prop == "0" or dnd_prop.lower() == "false":
                            log.info(f"Do Not Disturb状态: 关闭 (通过系统属性 {prop} 检测)")
                            return False
                except:
                    continue

            log.info("Do Not Disturb状态: 关闭 (所有检测方法均未发现开启状态)")
            return False

        except Exception as e:
            log.error(f"检查Do Not Disturb状态失败: {e}")
            return False

    def get_do_not_disturb_detailed_status(self) -> dict:
        """
        获取Do Not Disturb详细状态信息

        Returns:
            dict: Do Not Disturb详细状态信息
        """
        try:
            log.info("获取Do Not Disturb详细状态")

            status_info = {
                'dnd_enabled': False,
                'zen_mode': 0,
                'zen_mode_description': '关闭',
                'priority_categories': [],
                'allowed_callers': None,
                'allowed_messages': None,
                'detection_method': None,
                'schedule_enabled': False,
                'schedule_info': None
            }

            # 检查基本状态
            status_info['dnd_enabled'] = self.check_do_not_disturb_status()

            # 获取详细的通知管理器信息
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "notification"],
                capture_output=True,
                text=True,
                timeout=15
            )

            if result.returncode == 0:
                notification_output = result.stdout

                # 解析ZenMode详细信息
                import re
                zen_match = re.search(r'mZenMode=(\d+)', notification_output)
                if zen_match:
                    zen_mode = int(zen_match.group(1))
                    status_info['zen_mode'] = zen_mode
                    status_info['detection_method'] = 'DUMPSYS_NOTIFICATION'

                    zen_descriptions = {
                        0: '关闭',
                        1: '优先级模式',
                        2: '完全静音',
                        3: '仅闹钟'
                    }
                    status_info['zen_mode_description'] = zen_descriptions.get(zen_mode, f'未知模式({zen_mode})')

                # 解析优先级类别
                if "mPriorityCategories=" in notification_output:
                    priority_match = re.search(r'mPriorityCategories=(\d+)', notification_output)
                    if priority_match:
                        priority_value = int(priority_match.group(1))
                        categories = []

                        # 解析优先级类别位掩码
                        if priority_value & 1:
                            categories.append('提醒')
                        if priority_value & 2:
                            categories.append('事件')
                        if priority_value & 4:
                            categories.append('消息')
                        if priority_value & 8:
                            categories.append('通话')
                        if priority_value & 16:
                            categories.append('重复来电')

                        status_info['priority_categories'] = categories

                # 解析允许的来电者
                if "mPriorityCallSenders=" in notification_output:
                    caller_match = re.search(r'mPriorityCallSenders=(\d+)', notification_output)
                    if caller_match:
                        caller_value = int(caller_match.group(1))
                        caller_types = {
                            0: '任何人',
                            1: '联系人',
                            2: '收藏联系人',
                            3: '无人'
                        }
                        status_info['allowed_callers'] = caller_types.get(caller_value, f'未知类型({caller_value})')

                # 解析允许的消息发送者
                if "mPriorityMessageSenders=" in notification_output:
                    message_match = re.search(r'mPriorityMessageSenders=(\d+)', notification_output)
                    if message_match:
                        message_value = int(message_match.group(1))
                        message_types = {
                            0: '任何人',
                            1: '联系人',
                            2: '收藏联系人',
                            3: '无人'
                        }
                        status_info['allowed_messages'] = message_types.get(message_value, f'未知类型({message_value})')

                # 检查自动规则/计划
                if "ZenModeConfig" in notification_output:
                    # 查找自动规则信息
                    if "automaticRules" in notification_output:
                        status_info['schedule_enabled'] = True
                        # 可以进一步解析具体的规则信息
                        rule_matches = re.findall(r'rule\[([^\]]+)\]', notification_output)
                        if rule_matches:
                            status_info['schedule_info'] = rule_matches[:3]  # 保存前3个规则

            # 如果dumpsys失败，尝试通过settings获取基本信息
            if not status_info['detection_method']:
                result = subprocess.run(
                    ["adb", "shell", "settings", "get", "global", "zen_mode"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if result.returncode == 0:
                    zen_mode = result.stdout.strip()
                    try:
                        zen_mode_int = int(zen_mode)
                        status_info['zen_mode'] = zen_mode_int
                        status_info['detection_method'] = 'SETTINGS_GLOBAL'

                        zen_descriptions = {
                            0: '关闭',
                            1: '优先级模式',
                            2: '完全静音',
                            3: '仅闹钟'
                        }
                        status_info['zen_mode_description'] = zen_descriptions.get(zen_mode_int, f'未知模式({zen_mode_int})')
                    except ValueError:
                        pass

            return status_info

        except Exception as e:
            log.error(f"获取Do Not Disturb详细状态失败: {e}")
            return {
                'dnd_enabled': False,
                'zen_mode': 0,
                'zen_mode_description': '错误',
                'priority_categories': [],
                'allowed_callers': None,
                'allowed_messages': None,
                'detection_method': 'ERROR',
                'schedule_enabled': False,
                'schedule_info': None
            }

    def get_screen_brightness_detailed_status(self) -> dict:
        """
        获取屏幕亮度详细状态信息

        Returns:
            dict: 屏幕亮度详细状态信息
        """
        try:
            log.info("获取屏幕亮度详细状态")

            status_info = {
                'brightness_value': -1,
                'brightness_percentage': 0.0,
                'max_brightness': 255,
                'auto_brightness_enabled': False,
                'adaptive_brightness_enabled': False,
                'detection_method': None,
                'hardware_brightness': -1,
                'hardware_max_brightness': -1,
                'brightness_path_found': None
            }

            # 获取基本亮度值
            status_info['brightness_value'] = self.get_screen_brightness()

            if status_info['brightness_value'] > 0:
                status_info['brightness_percentage'] = (status_info['brightness_value'] / 255) * 100

            # 检查自动亮度状态
            try:
                auto_result = subprocess.run(
                    ["adb", "shell", "settings", "get", "system", "screen_brightness_mode"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if auto_result.returncode == 0:
                    auto_mode = auto_result.stdout.strip()
                    status_info['auto_brightness_enabled'] = auto_mode == "1"
                    log.info(f"自动亮度: {'开启' if status_info['auto_brightness_enabled'] else '关闭'} (值: {auto_mode})")
                    if not status_info['detection_method']:
                        status_info['detection_method'] = 'SETTINGS_SYSTEM'
            except:
                pass

            # 检查自适应亮度状态（Android 9+）
            try:
                adaptive_result = subprocess.run(
                    ["adb", "shell", "settings", "get", "secure", "screen_brightness_mode"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if adaptive_result.returncode == 0:
                    adaptive_mode = adaptive_result.stdout.strip()
                    status_info['adaptive_brightness_enabled'] = adaptive_mode == "1"
                    log.info(f"自适应亮度: {'开启' if status_info['adaptive_brightness_enabled'] else '关闭'} (值: {adaptive_mode})")
            except:
                pass

            # 获取硬件亮度信息
            brightness_paths = [
                "/sys/class/backlight/panel0-backlight/brightness",
                "/sys/class/backlight/backlight/brightness",
                "/sys/class/leds/lcd-backlight/brightness",
                "/sys/class/backlight/panel/brightness",
                "/sys/devices/platform/backlight/backlight/brightness"
            ]

            for brightness_path in brightness_paths:
                try:
                    result = subprocess.run(
                        ["adb", "shell", "cat", brightness_path],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if result.returncode == 0:
                        brightness_str = result.stdout.strip()
                        try:
                            hardware_brightness = int(brightness_str)
                            status_info['hardware_brightness'] = hardware_brightness
                            status_info['brightness_path_found'] = brightness_path
                            log.info(f"硬件亮度值: {hardware_brightness} (路径: {brightness_path})")

                            # 获取硬件最大亮度
                            max_path = brightness_path.replace('/brightness', '/max_brightness')
                            max_result = subprocess.run(
                                ["adb", "shell", "cat", max_path],
                                capture_output=True,
                                text=True,
                                timeout=3
                            )

                            if max_result.returncode == 0:
                                try:
                                    hardware_max = int(max_result.stdout.strip())
                                    status_info['hardware_max_brightness'] = hardware_max
                                    hardware_percentage = (hardware_brightness / hardware_max) * 100
                                    log.info(f"硬件亮度百分比: {hardware_percentage:.1f}% (最大值: {hardware_max})")
                                    if not status_info['detection_method']:
                                        status_info['detection_method'] = 'HARDWARE_BACKLIGHT'
                                except ValueError:
                                    pass
                            break  # 找到有效路径就退出
                        except ValueError:
                            continue
                except:
                    continue

            # 通过dumpsys display获取更多显示信息
            try:
                display_result = subprocess.run(
                    ["adb", "shell", "dumpsys", "display"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if display_result.returncode == 0:
                    display_output = display_result.stdout

                    # 解析显示亮度信息
                    import re
                    brightness_match = re.search(r'brightness=(\d+\.?\d*)', display_output)
                    if brightness_match:
                        display_brightness = float(brightness_match.group(1))
                        log.info(f"显示服务亮度: {display_brightness}")
                        if status_info['brightness_value'] == -1:
                            # 如果之前没有获取到亮度值，使用显示服务的值
                            status_info['brightness_value'] = int(display_brightness * 255)
                            status_info['brightness_percentage'] = display_brightness * 100
                            status_info['detection_method'] = 'DUMPSYS_DISPLAY'

                    # 检查自动亮度状态
                    if "auto-brightness" in display_output.lower():
                        auto_match = re.search(r'auto-brightness[:\s]*(\w+)', display_output.lower())
                        if auto_match:
                            auto_status = auto_match.group(1)
                            if auto_status in ['true', 'enabled', 'on']:
                                status_info['auto_brightness_enabled'] = True
                            elif auto_status in ['false', 'disabled', 'off']:
                                status_info['auto_brightness_enabled'] = False
            except Exception as e:
                log.warning(f"获取display信息失败: {e}")

            return status_info

        except Exception as e:
            log.error(f"获取屏幕亮度详细状态失败: {e}")
            return {
                'brightness_value': -1,
                'brightness_percentage': 0.0,
                'max_brightness': 255,
                'auto_brightness_enabled': False,
                'adaptive_brightness_enabled': False,
                'detection_method': 'ERROR',
                'hardware_brightness': -1,
                'hardware_max_brightness': -1,
                'brightness_path_found': None
            }

    def check_service_health(self) -> bool:
        """
        检查UIAutomator2服务健康状态

        Returns:
            bool: 服务是否健康
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False

            log.info("检查UIAutomator2服务健康状态")

            # 检查设备信息是否可以正常获取
            device_info = self.driver.device_info

            # 检查关键信息是否存在
            if not device_info or not device_info.get('serial'):
                log.warning("设备信息不完整")
                return False

            # 尝试获取屏幕信息
            window_size = self.driver.window_size()
            if not window_size or len(window_size) != 2:
                log.warning("无法获取屏幕信息")
                return False

            log.info("✅ UIAutomator2服务健康状态良好")
            return True

        except Exception as e:
            log.warning(f"UIAutomator2服务健康检查失败: {e}")
            return False

    def check_nfc_status(self) -> bool:
        """
           通过 'dumpsys nfc' 命令直接查询 NFC 核心服务的状态，
           这是在无法通过 settings 数据库找到开关时最可靠的方法。

           Returns:
               bool: 如果 NFC 已开启返回 True, 关闭返回 False。
               None: 如果命令执行失败或无法判断状态，则返回 None。
           """

        try:
            log.info("检查NFC状态")

            # 方法1: 通过dumpsys命令检查
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "nfc"],
                capture_output=True,
                text=True,
                timeout=10,
                check=False  # 手动检查，避免因找不到服务而直接崩溃
            )

            # 1. 检查命令是否成功执行
            if result.returncode != 0:
                log.error(f"执行 'dumpsys nfc' 命令失败。返回码: {result.returncode}")
                stderr_msg = result.stderr.strip() if result.stderr else "无错误信息"
                log.error(f"错误信息: {stderr_msg}")
                return None

            # 2. 检查输出是否为空
            if not result.stdout:
                log.error("dumpsys nfc 命令输出为空")
                return None

            # 3. 遍历输出，查找关键的状态行
            nfc_state_found = False
            for line in result.stdout.strip().splitlines():
                # 移除行首的空格，方便判断
                clean_line = line.strip()

                # 查找最关键的状态信息，通常以 'mState=' 或 'mAdapterState=' 开头
                if clean_line.startswith("mState=") or clean_line.startswith("mAdapterState="):
                    nfc_state_found = True
                    log.info(f"成功找到状态行: {clean_line}")

                    # 判断状态是 ON 还是 OFF
                    if "on" in clean_line:
                        log.info("状态判断 -> NFC 已开启")
                        return True
                    elif "off" in clean_line:
                        log.info("状态判断 -> NFC 已关闭")
                        return False
                    else:
                        # 找到了状态行，但内容不认识，也视为无法判断
                        log.warning(f"找到了状态行，但无法识别其状态: {clean_line}")
                        return None

            # 4. 如果遍历完所有行都没有找到状态信息
            if not nfc_state_found:
                log.warning("命令成功执行，但在其输出中未找到 NFC 状态行 (mState=...)。")
                log.warning("请检查 NFC 服务是否在设备上正常运行。")
                return None

        except Exception as e:
            log.error(f"检查 NFC 状态时发生未知错误: {e}")
            return None

    def check_auto_brightness_status(self) -> bool:
        try:
            log.info("检查Auto Brightness状态")

            # 方法1: 通过ADB命令检查Auto Brightness状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "system", "screen_brightness_mode"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                auto_brightness_status = result.stdout.strip()
                is_on = auto_brightness_status == "1"
                log.info(f"Auto Brightness状态: {'开启' if is_on else '关闭'} (值: {auto_brightness_status})")
                return is_on
            else:
                log.warning(f"方法1获取Auto Brightness状态失败: {result.stderr}")

            # 方法2：使用 dumpsys 解析Auto Brightness状态
            # 执行 adb 命令
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "display"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                print(f"命令执行失败: {result.stderr}")
                return None

            output = result.stdout

            # 使用正则表达式查找状态
            match = re.search(r"mAutoBrightnessEnabled\s*=\s*(true|false)", output, re.IGNORECASE)

            if match:
                status_str = match.group(1).lower()
                return status_str == "true"

            print("未找到 mAutoBrightnessEnabled 字段")
            return None


        except Exception as e:
            log.error(f"检查Auto Brightness状态时发生未知错误: {e}")
            return None

    def check_auto_rotate_status(self) -> bool:
        try:
            log.info("检查Auto Rotate状态")

            # 方法1: 通过ADB命令检查Auto Rotate状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "system", "accelerometer_rotation"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                auto_brightness_status = result.stdout.strip()
                is_on = auto_brightness_status == "1"
                log.info(f"Auto Rotate状态: {'开启' if is_on else '关闭'} (值: {auto_brightness_status})")
                return is_on
            else:
                log.warning(f"方法1获取Auto Rotate状态失败: {result.stderr}")

            # 方法2：使用 dumpsys 解析Auto Rotate状态
            # 执行 adb 命令
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                print(f"命令执行失败: {result.stderr}")
                return None

            output = result.stdout

            # 更精确的解析逻辑
            # 1. 查找所有包含 mUserRotationMode 的行
            rotation_lines = [line for line in output.splitlines() if "mUserRotationMode" in line]

            # 2. 如果没有找到任何相关行
            if not rotation_lines:
                print("未找到任何 mUserRotationMode 字段")
                return None

            # 3. 打印所有找到的行以便调试
            print("找到的旋转模式行:")
            for i, line in enumerate(rotation_lines, 1):
                print(f"{i}. {line}")

            # 4. 检查最后一行（通常是最新的状态）
            last_line = rotation_lines[-1]
            if "USER_ROTATION_FREE" in last_line:
                return True
            elif "USER_ROTATION_LOCKED" in last_line:
                return False

            # 5. 如果最后一行没有明确状态
            print("最后一行未包含明确的旋转状态")
            return None

        except Exception as e:
            log.error(f"检查Auto Rotate状态时发生未知错误: {e}")
            return None

    def check_smart_reminder_status(self) -> bool:
        """
                检查smart_reminder状态

                Returns:
                    bool: smart_reminder是否已开启
                """
        try:
            log.info("检查smart_reminder状态")

            # 方法1: 通过ADB命令检查smart_reminder状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "os_settings_open_smart_reminder"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                smart_reminder_status = result.stdout.strip()
                is_on = smart_reminder_status == "1"
                log.info(f"smart_reminder状态: {'开启' if is_on else '关闭'} (值: {smart_reminder_status})")
                return is_on
            else:
                log.warning(f"方法1获取smart_reminder状态失败: {result.stderr}")

            # 方法2: 通过dumpsys检查smart_reminder状态
            # 使用shell=True来处理管道符'|'
            dumpsys_command = 'adb shell "dumpsys settings | grep os_settings_open_smart_reminder"'

            result = subprocess.run(
                dumpsys_command,
                shell=True,  # shell=True 在这里是安全的，因为命令是固定的
                capture_output=True,
                text=True,
                timeout=10,
                check=False
            )

            if result.returncode == 0 and result.stdout:
                output = result.stdout.strip()
                log.info(f"成功获取到 dumpsys 输出: {output}")

                # 2. 在输出中查找关键信息 "value:1"
                if "value:1" in output:
                    log.info("状态判断 -> 开启 (原因: 在输出中找到了 'value:1')")
                    return True
                else:
                    # 只要有输出，但里面没有 "value:1"，我们就认为它是关闭的
                    log.info("状态判断 -> 关闭 (原因: 输出中未找到 'value:1')")
                    return False
            else:
                log.warning(f"方法2获取smart_reminder状态失败: {result.stderr or '无输出'}")

            log.warning("所有方法都无法确定smart_reminder状态，默认返回False")
            return False

        except Exception as e:
            log.error(f"检查smart_reminder状态失败: {e}")
            return False

    def check_airplane_status(self) -> bool:

        try:
            log.info("检查Airplane状态")

            # 方法1: 通过ADB命令检查Airplane状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "airplane_mode_on"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                auto_brightness_status = result.stdout.strip()
                is_on = auto_brightness_status == "1"
                log.info(f"Airplane状态: {'开启' if is_on else '关闭'} (值: {auto_brightness_status})")
                return is_on
            else:
                log.warning(f"方法1获取Airplane状态失败: {result.stderr}")

            # 方法2：使用 dumpsys 解析Airplane状态
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "wifi"],
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace',
                timeout=10
            )

            # 检查命令执行状态
            if result.returncode != 0:
                error_msg = result.stderr.strip() or "Unknown error"
                print(f"命令执行失败 (code {result.returncode}): {error_msg}")
                return None

            # 获取输出内容
            output = result.stdout

            # 检查输出是否为空
            if not output.strip():
                print("命令执行成功，但输出为空")
                return None

            # 查找包含 "AirplaneModeOn" 的行
            for line in output.splitlines():
                if "AirplaneModeOn" in line:
                    # 提取状态值（不区分大小写）
                    if "true" in line.lower():
                        return True
                    elif "false" in line.lower():
                        return False

            # 如果没找到 AirplaneModeOn，尝试使用正则表达式匹配其他可能的格式
            pattern = r'(?i)AirplaneMode(?:On)?\s*[=:]\s*(true|false|on|off|enabled|disabled|1|0)'
            match = re.search(pattern, output)

            if match:
                status = match.group(1).lower()
                if status in ["true", "on", "enabled", "1"]:
                    return True
                elif status in ["false", "off", "disabled", "0"]:
                    return False

            # 输出前200个字符用于调试
            print("输出内容（前200字符）：")
            print(output[:200] + "..." if len(output) > 200 else output)

            print("未找到 AirplaneModeOn 字段")
            return None

        except Exception as e:
            log.error(f"检查Airplane状态时发生未知错误: {e}")
            return None

    def check_mute_status(self) -> bool | None:
        """
        通过铃声(ring)和通知(notification)音量判断设备静音状态

        返回:
            True: 静音状态 (铃声音量和通知音量均为0)
            False: 非静音状态 (至少有一个音量大于0)
            None: 获取音量失败 (无法确定状态)
        """
        try:
            # 获取音量值并转换为整数
            ring_vol = int(self.get_ring_volume())
            notif_vol = int(self.get_notification_volume())

            # 检查获取结果是否有效
            if ring_vol == -1 or notif_vol == -1:
                return None

            # 判断静音状态
            return ring_vol == 0 and notif_vol == 0

        except (ValueError, TypeError):
            # 处理类型转换错误或意外返回值
            return None



if __name__ == '__main__':
    from core.base_driver import driver_manager

    checker = SystemStatusChecker(driver_manager.driver)

    # # 测试蓝牙状态
    # print("蓝牙状态:", checker.check_bluetooth_status())
    #
    # # 测试WiFi状态
    # print("WiFi状态:", checker.check_wifi_status())
    #
    # # 测试WiFi连接详细状态
    # wifi_info = checker.check_wifi_connection_status()
    # print("WiFi详细信息:", wifi_info)
    #
    # # 测试移动数据状态
    # print("移动数据状态:", checker.check_mobile_data_status())
    #
    # # 测试移动数据连接详细状态
    # mobile_data_info = checker.check_mobile_data_connection_status()
    # print("移动数据详细信息:", mobile_data_info)

    # 测试手电筒状态
    # print("手电筒状态:", checker.check_flashlight_status())

    # 测试手电筒详细状态
    # flashlight_info = checker.get_flashlight_detailed_status()
    # print("手电筒详细信息:", flashlight_info)

    # 测试Do Not Disturb状态
    # print("Do Not Disturb状态:", checker.check_do_not_disturb_status())

    # 测试屏幕亮度功能
    # print("屏幕亮度值:", checker.get_screen_brightness())

    # print("媒体音量:", checker.get_system_volume())  # 优化后：获取实时音量值，不再依赖settings
    # print("系统音量:", checker.get_volume_max_value())
    # print("系统音量详情:", checker.get_system_volume_detailed_status())  # 优化后：获取实时音量详情，包含准确的百分比
    # print("系统音量是否为最大值:", checker.is_music_volume_at_max ())  # 优化后：获取实时音量详情，包含准确的百分比
    # print("系统音量:", checker.get_volume_with_percentage())
    # print("定位服务:", checker.check_location_status())
    print("alarm音量:", checker.get_alarm_volume())
    # print("notification音量:", checker.get_notification_volume())
    # 测试主题状态
    # is_light = checker.check_light_theme_status()
    # print(f"当前系统主题: {'浅色主题' if is_light else '深色主题'}")

    # print("wifi:", checker.get_light_theme_detailed_status())

    # # 获取详细主题信息
    # theme_info = checker.get_light_theme_detailed_status()
    # print(f"主题模式: {theme_info['theme_mode']}")
    # print(f"UI夜间模式: {theme_info['ui_night_mode_description']}")
    # if theme_info['auto_mode_enabled']:
    #     print(f"自动模式当前状态: {theme_info['auto_mode_current_status']}")
    # print(f"检测方法: {theme_info['detection_method']}")

    # # 测试屏幕亮度详细状态
    # brightness_info = checker.get_screen_brightness_detailed_status()
    # print("屏幕亮度详细信息:", brightness_info)

    # 测试移动数据状态
    # print("check_mobile_data_status:", checker.check_mobile_data_status())
    # print("check_mobile_data_connection_status:", checker.check_mobile_data_connection_status())

    # 测试 Active Halo Lighting 状态
    # print("Active Halo Lighting 状态:", checker.check_active_halo_lighting_status())

    # 获取详细 Halo Lighting 信息
    # halo_info = checker.get_active_halo_lighting_detailed_status()
    # print(f"Halo Lighting 检测方法: {halo_info['detection_method']}")
    # print(f"通知LED状态: {'开启' if halo_info['notification_led_enabled'] else '关闭'}")

    # 测试Auto Brightness状态
    # print("check_auto_brightness_status:", checker.check_auto_brightness_status())

    # # 测试Auto Rotate状态
    # print("check_auto_rotate_status:", checker.check_auto_rotate_status())

    # 测试smart reminder状态
    # print("smart reminder:", checker.check_smart_reminder_status())

    # 测试Airplane状态
    # print("check_airplane_status:", checker.check_airplane_status())

    # 测试Mute状态
    # print("check_mute_status:", checker.check_mute_status())