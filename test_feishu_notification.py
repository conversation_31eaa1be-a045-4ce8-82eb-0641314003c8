#!/usr/bin/env python3
"""
测试飞书通知功能
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from tools.allure_to_excel import AllureToExcelConverter

def test_feishu_notification():
    """测试飞书通知功能"""
    print("🧪 测试飞书通知功能...")
    print("=" * 60)
    
    # 创建转换器实例
    converter = AllureToExcelConverter()
    
    # 模拟测试结果数据
    mock_test_results = [
        {
            '测试状态': 'passed',
            '用例名称': 'test_bluetooth_on',
            '开始时间': '2025-08-19 10:00:00',
            '结束时间': '2025-08-19 10:01:30'
        },
        {
            '测试状态': 'passed',
            '用例名称': 'test_wifi_on',
            '开始时间': '2025-08-19 10:02:00',
            '结束时间': '2025-08-19 10:03:00'
        },
        {
            '测试状态': 'failed',
            '用例名称': 'test_nfc_on',
            '开始时间': '2025-08-19 10:04:00',
            '结束时间': '2025-08-19 10:05:30'
        },
        {
            '测试状态': 'passed',
            '用例名称': 'test_location_on',
            '开始时间': '2025-08-19 10:06:00',
            '结束时间': '2025-08-19 10:07:00'
        },
        {
            '测试状态': 'passed',
            '用例名称': 'test_flashlight_on',
            '开始时间': '2025-08-19 10:08:00',
            '结束时间': '2025-08-19 10:09:30'
        }
    ]
    
    # 模拟状态统计
    status_counts = {
        'passed': 4,
        'failed': 1
    }
    
    total_cases = len(mock_test_results)
    
    print(f"📊 模拟测试数据:")
    print(f"   总用例数: {total_cases}")
    print(f"   成功数: {status_counts['passed']}")
    print(f"   失败数: {status_counts['failed']}")
    print(f"   成功率: {(status_counts['passed'] / total_cases * 100):.1f}%")
    
    print("\n📱 正在发送飞书通知...")
    
    try:
        # 调用发送通知方法
        converter._send_feishu_notification(mock_test_results, status_counts, total_cases)
        print("✅ 飞书通知测试完成")
        
    except Exception as e:
        print(f"❌ 飞书通知测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_device_info_loading():
    """测试设备信息加载"""
    print("\n🔧 测试设备信息加载...")
    print("=" * 60)
    
    converter = AllureToExcelConverter()
    
    try:
        device_info = converter._load_device_info()
        print("✅ 设备信息加载成功:")
        for key, value in device_info.items():
            print(f"   {key}: {value}")
            
    except Exception as e:
        print(f"❌ 设备信息加载失败: {e}")

def test_duration_calculation():
    """测试时长计算"""
    print("\n⏱ 测试时长计算...")
    print("=" * 60)
    
    converter = AllureToExcelConverter()
    
    mock_results = [
        {
            '开始时间': '2025-08-19 10:00:00',
            '结束时间': '2025-08-19 10:30:00'
        }
    ]
    
    try:
        duration = converter._calculate_test_duration(mock_results)
        print(f"✅ 计算的执行时长: {duration} 分钟")
        
    except Exception as e:
        print(f"❌ 时长计算失败: {e}")

def main():
    """主函数"""
    print("🎯 飞书通知功能测试")
    print("=" * 70)
    
    test_device_info_loading()
    test_duration_calculation()
    test_feishu_notification()
    
    print("\n" + "=" * 70)
    print("🎉 测试完成!")
    print("\n💡 说明:")
    print("   - 如果飞书通知发送成功，您应该能在测开小群中看到测试报告卡片")
    print("   - 卡片颜色会根据成功率自动调整：绿色(≥90%), 黄色(≥70%), 红色(<70%)")
    print("   - 设备信息来自 config/devices.yaml 中的当前设备配置")

if __name__ == '__main__':
    main()
