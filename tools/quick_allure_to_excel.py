#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动Allure报告转Excel工具
简化版本，直接运行即可生成当天的Excel报告
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from allure_to_excel import AllureToExcelConverter
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 allure_to_excel.py 文件存在于同一目录下")
    sys.exit(1)


def main():
    """快速启动主函数"""
    print("🚀 Allure报告转Excel工具 - 快速启动")
    print("=" * 50)
    
    # 使用默认的reports目录
    reports_dir = "reports"
    
    # 检查reports目录是否存在
    if not os.path.exists(reports_dir):
        print(f"❌ 报告目录不存在: {reports_dir}")
        print("请确保已经运行过Allure测试并生成了报告")
        return 1
    
    # 检查allure-results目录是否存在
    allure_results_dir = os.path.join(reports_dir, "allure-results")
    if not os.path.exists(allure_results_dir):
        print(f"❌ Allure结果目录不存在: {allure_results_dir}")
        print("请确保已经运行过Allure测试")
        return 1
    
    # 创建转换器并执行转换
    print(f"📁 使用报告目录: {reports_dir}")
    converter = AllureToExcelConverter(reports_dir)
    
    try:
        print("\n🔄 开始转换...")
        output_file = converter.convert_to_excel()
        print(f"\n✅ 转换完成！")
        print(f"📊 Excel文件已保存到: {output_file}")
        print(f"📝 文件包含以下工作表:")
        print(f"   - 测试用例结果")
        print(f"   - 容器信息") 
        print(f"   - 统计信息")
        print(f"\n💡 提示: 可以用Excel、WPS或LibreOffice打开查看")
        
    except Exception as e:
        print(f"\n❌ 转换失败: {e}")
        print(f"请检查:")
        print(f"  1. {allure_results_dir} 目录中是否有测试结果文件")
        print(f"  2. 是否有足够的磁盘空间")
        print(f"  3. 是否有写入权限")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
