#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败原因分析示例
演示如何使用Excel文件中的失败原因信息进行测试分析
"""

import pandas as pd
import sys
from pathlib import Path
from collections import Counter
import re


def analyze_failure_patterns():
    """分析失败模式"""
    
    # 查找最新的Excel文件
    reports_dir = Path("reports")
    excel_files = list(reports_dir.glob("allure_report_*.xlsx"))
    
    if not excel_files:
        print("❌ 未找到Excel报告文件")
        return False
    
    latest_file = max(excel_files, key=lambda x: x.stat().st_mtime)
    print(f"📊 分析文件: {latest_file}")
    
    try:
        # 读取测试用例结果
        df = pd.read_excel(latest_file, sheet_name='测试用例结果')
        
        # 筛选失败用例
        failed_cases = df[df['测试状态'].isin(['failed', 'broken'])]
        
        print(f"\n📋 总体统计:")
        print(f"   - 总用例数: {len(df)}")
        print(f"   - 失败用例数: {len(failed_cases)}")
        print(f"   - 失败率: {len(failed_cases)/len(df)*100:.1f}%")
        
        if len(failed_cases) == 0:
            print("🎉 没有失败用例！")
            return True
        
        # 分析失败原因模式
        print(f"\n🔍 失败原因分析:")
        analyze_failure_reasons(failed_cases)
        
        # 分析失败用例分布
        print(f"\n📊 失败用例分布:")
        analyze_failure_distribution(failed_cases)
        
        # 分析最常见的失败类型
        print(f"\n🏷️ 失败类型分析:")
        analyze_failure_types(failed_cases)
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False


def analyze_failure_reasons(failed_cases):
    """分析失败原因"""
    
    # 统计失败原因关键词
    reason_keywords = []
    
    for _, case in failed_cases.iterrows():
        reason = case.get('失败原因', '')
        if reason:
            # 提取关键词
            keywords = extract_keywords(reason)
            reason_keywords.extend(keywords)
    
    # 统计最常见的关键词
    keyword_counts = Counter(reason_keywords)
    top_keywords = keyword_counts.most_common(10)
    
    print("   最常见的失败关键词:")
    for keyword, count in top_keywords:
        percentage = count / len(failed_cases) * 100
        print(f"     - {keyword}: {count} 次 ({percentage:.1f}%)")


def extract_keywords(reason_text):
    """从失败原因中提取关键词"""
    if not reason_text:
        return []
    
    # 常见的失败关键词模式
    patterns = [
        r'AssertionError',
        r'TimeoutException',
        r'ElementNotFound',
        r'ConnectionError',
        r'未包含期望内容',
        r'响应.*包含',
        r'元素.*找到',
        r'超时',
        r'连接.*失败',
        r'not installed',
        r'download.*app',
        r'permission.*denied',
    ]
    
    keywords = []
    text_lower = reason_text.lower()
    
    for pattern in patterns:
        if re.search(pattern.lower(), text_lower):
            keywords.append(pattern)
    
    # 如果没有匹配到特定模式，提取前几个单词
    if not keywords:
        words = reason_text.split()[:3]
        keywords.extend([word for word in words if len(word) > 2])
    
    return keywords


def analyze_failure_distribution(failed_cases):
    """分析失败用例分布"""
    
    # 按测试套件分组
    suite_failures = failed_cases.groupby('标签_suite').size().sort_values(ascending=False)
    
    print("   按测试套件分布:")
    for suite, count in suite_failures.head(10).items():
        print(f"     - {suite}: {count} 个失败")
    
    # 按严重程度分组
    if '标签_severity' in failed_cases.columns:
        severity_failures = failed_cases.groupby('标签_severity').size().sort_values(ascending=False)
        
        print("\n   按严重程度分布:")
        for severity, count in severity_failures.items():
            print(f"     - {severity}: {count} 个失败")


def analyze_failure_types(failed_cases):
    """分析失败类型"""
    
    # 分类失败类型
    assertion_failures = 0
    timeout_failures = 0
    element_failures = 0
    app_failures = 0
    other_failures = 0
    
    for _, case in failed_cases.iterrows():
        reason = case.get('失败原因', '').lower()
        message = case.get('失败消息', '').lower()
        
        combined_text = f"{reason} {message}"
        
        if 'assertion' in combined_text or '断言' in combined_text:
            assertion_failures += 1
        elif 'timeout' in combined_text or '超时' in combined_text:
            timeout_failures += 1
        elif 'element' in combined_text or '元素' in combined_text:
            element_failures += 1
        elif 'not installed' in combined_text or 'download' in combined_text:
            app_failures += 1
        else:
            other_failures += 1
    
    total = len(failed_cases)
    
    print(f"   - 断言失败: {assertion_failures} ({assertion_failures/total*100:.1f}%)")
    print(f"   - 超时失败: {timeout_failures} ({timeout_failures/total*100:.1f}%)")
    print(f"   - 元素定位失败: {element_failures} ({element_failures/total*100:.1f}%)")
    print(f"   - 应用未安装: {app_failures} ({app_failures/total*100:.1f}%)")
    print(f"   - 其他失败: {other_failures} ({other_failures/total*100:.1f}%)")


def show_detailed_failures():
    """显示详细的失败信息"""
    
    reports_dir = Path("reports")
    excel_files = list(reports_dir.glob("allure_report_*.xlsx"))
    
    if not excel_files:
        print("❌ 未找到Excel报告文件")
        return
    
    latest_file = max(excel_files, key=lambda x: x.stat().st_mtime)
    
    try:
        df = pd.read_excel(latest_file, sheet_name='测试用例结果')
        failed_cases = df[df['测试状态'].isin(['failed', 'broken'])]
        
        print(f"\n📝 详细失败信息 (前5个):")
        print("=" * 80)
        
        for i, (_, case) in enumerate(failed_cases.head(5).iterrows()):
            print(f"\n{i+1}. 测试用例: {case['测试用例名称']}")
            print(f"   状态: {case['测试状态']}")
            print(f"   执行时长: {case['执行时长(ms)']}ms")
            
            if case.get('失败原因'):
                print(f"   失败原因: {case['失败原因']}")
            
            if case.get('步骤详情'):
                steps = case['步骤详情']
                if 'failed' in steps or 'broken' in steps:
                    print(f"   失败步骤: {steps}")
            
            print("-" * 60)
        
    except Exception as e:
        print(f"❌ 显示详细信息失败: {e}")


def main():
    """主函数"""
    print("🔍 Allure测试失败原因分析")
    print("=" * 50)
    
    # 执行失败模式分析
    success = analyze_failure_patterns()
    
    if success:
        # 显示详细失败信息
        show_detailed_failures()
        
        print("\n" + "=" * 50)
        print("💡 分析建议:")
        print("   1. 关注失败率最高的测试套件")
        print("   2. 优先修复断言失败和应用未安装问题")
        print("   3. 检查超时设置是否合理")
        print("   4. 验证元素定位器是否需要更新")
        print("   5. 确保测试环境中安装了必要的应用")
        
        return 0
    else:
        return 1


if __name__ == "__main__":
    exit(main())
