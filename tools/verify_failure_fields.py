#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Excel文件中是否包含失败原因字段
"""

import pandas as pd
import sys
from pathlib import Path


def verify_failure_fields():
    """验证Excel文件中的失败原因字段"""
    
    # 查找最新的Excel文件
    reports_dir = Path("reports")
    excel_files = list(reports_dir.glob("allure_report_*.xlsx"))
    
    if not excel_files:
        print("❌ 未找到Excel报告文件")
        return False
    
    # 使用最新的文件
    latest_file = max(excel_files, key=lambda x: x.stat().st_mtime)
    print(f"📊 检查文件: {latest_file}")
    
    try:
        # 读取测试用例结果工作表
        df = pd.read_excel(latest_file, sheet_name='测试用例结果')
        
        print(f"📋 总用例数: {len(df)}")
        
        # 检查是否包含失败原因相关字段
        expected_fields = ['失败消息', '失败堆栈', '失败原因']
        missing_fields = []
        
        for field in expected_fields:
            if field not in df.columns:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
            return False
        
        print("✅ 所有失败原因字段都存在")
        
        # 统计各状态的用例数
        status_counts = df['测试状态'].value_counts()
        print(f"\n📊 测试状态分布:")
        for status, count in status_counts.items():
            print(f"   - {status}: {count}")
        
        # 检查失败用例的失败原因
        failed_cases = df[df['测试状态'].isin(['failed', 'broken'])]
        print(f"\n🔍 失败用例数: {len(failed_cases)}")
        
        if len(failed_cases) > 0:
            print("\n📝 失败用例示例:")
            
            # 显示前3个失败用例的信息
            for i, (idx, case) in enumerate(failed_cases.head(3).iterrows()):
                print(f"\n{i+1}. 用例: {case['测试用例名称']}")
                print(f"   状态: {case['测试状态']}")
                
                failure_reason = case.get('失败原因', '')
                if failure_reason:
                    print(f"   失败原因: {failure_reason}")
                else:
                    print("   失败原因: (无)")
                
                failure_message = case.get('失败消息', '')
                if failure_message:
                    # 只显示前100个字符
                    short_message = failure_message[:100] + "..." if len(failure_message) > 100 else failure_message
                    print(f"   失败消息: {short_message}")
        
        # 统计有失败原因的用例数
        cases_with_reason = failed_cases[failed_cases['失败原因'].notna() & (failed_cases['失败原因'] != '')]
        print(f"\n📈 统计信息:")
        print(f"   - 失败/错误用例总数: {len(failed_cases)}")
        print(f"   - 有失败原因的用例数: {len(cases_with_reason)}")
        
        if len(failed_cases) > 0:
            coverage = len(cases_with_reason) / len(failed_cases) * 100
            print(f"   - 失败原因覆盖率: {coverage:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 验证Excel文件中的失败原因字段")
    print("=" * 50)
    
    success = verify_failure_fields()
    
    if success:
        print("\n✅ 验证完成！失败原因字段已正确添加到Excel文件中")
        return 0
    else:
        print("\n❌ 验证失败！")
        return 1


if __name__ == "__main__":
    exit(main())
