# Allure报告转Excel工具

## 功能描述

这个工具可以将Allure测试报告中的用例执行结果信息提取并存储到Excel表格中，文件名以当天日期（YY-MM-DD）命名。

## 主要特性

- 📊 **完整数据提取**: 提取Allure报告中的所有字段信息
- 📅 **自动命名**: 以当天日期自动命名Excel文件（格式：allure_report_YY-MM-DD.xlsx）
- 📋 **多工作表**: 分别创建测试用例结果、容器信息和统计信息工作表
- 🔍 **详细信息**: 包含测试步骤、附件、参数等详细信息
- ❌ **失败原因分析**: 自动提取和解析失败用例的错误消息、堆栈信息和简要原因
- 📈 **统计分析**: 自动生成测试结果统计信息

## 安装依赖

工具依赖以下Python包（项目中已包含）：
```bash
pip install pandas openpyxl
```

## 使用方法

### 1. 命令行使用

```bash
# 使用默认报告目录 (reports)
python tools/allure_to_excel.py

# 指定报告目录
python tools/allure_to_excel.py --reports-dir /path/to/reports
python tools/allure_to_excel.py -r /path/to/reports
```

### 2. Python代码中使用

```python
from tools.allure_to_excel import AllureToExcelConverter

# 创建转换器
converter = AllureToExcelConverter("reports")

# 执行转换
output_file = converter.convert_to_excel()
print(f"Excel文件已生成: {output_file}")
```

## 输出文件结构

生成的Excel文件包含以下工作表：

### 1. 测试用例结果工作表
包含每个测试用例的详细信息：
- UUID: 测试用例唯一标识
- 测试用例名称: 测试用例的名称
- 测试状态: passed/failed/broken/skipped等
- 描述: 测试用例描述
- 完整名称: 包含包路径的完整名称
- 历史ID: 用于历史追踪的ID
- 测试用例ID: 测试用例标识
- 开始时间: 测试开始时间
- 结束时间: 测试结束时间
- 执行时长(ms): 测试执行时长（毫秒）
- **失败消息**: 完整的失败错误消息（仅失败用例）
- **失败堆栈**: 详细的错误堆栈信息（仅失败用例）
- **失败原因**: 提取的简要失败原因（仅失败用例）
- 标签_*: 各种标签信息（feature、story、severity等）
- 步骤数量: 测试步骤总数
- 步骤详情: 详细的步骤信息（包含失败步骤的错误信息）
- 附件数量: 附件总数
- 附件详情: 附件详细信息
- 参数数量: 参数总数
- 参数详情: 参数详细信息

### 2. 容器信息工作表
包含测试容器的信息：
- UUID: 容器唯一标识
- 子项: 包含的子测试用例
- 开始时间: 容器开始时间
- 结束时间: 容器结束时间
- 执行时长(ms): 容器执行时长
- 前置操作: 前置钩子函数信息
- 后置操作: 后置钩子函数信息

### 3. 统计信息工作表
包含测试结果的统计信息：
- 总用例数
- 各状态用例数及百分比
- 报告生成时间

## 文件命名规则

生成的Excel文件命名格式：`allure_report_YY-MM-DD.xlsx`

例如：
- 2025年8月18日生成的文件：`allure_report_25-08-18.xlsx`

## 目录结构要求

工具期望的目录结构：
```
reports/
├── allure-results/          # Allure原始结果文件
│   ├── *-result.json       # 测试结果文件
│   ├── *-container.json    # 容器文件
│   └── *-attachment.*      # 附件文件
└── allure_report_YY-MM-DD.xlsx  # 生成的Excel文件
```

## 错误处理

- 如果报告目录不存在，工具会提示错误
- 如果JSON文件格式错误，会跳过该文件并继续处理其他文件
- 所有错误信息都会在控制台显示

## 示例输出

```
开始提取Allure报告数据...
找到 150 个测试结果文件
找到 75 个容器文件
提取到 150 个测试用例
提取到 75 个容器信息
测试用例结果已写入工作表 '测试用例结果'
容器信息已写入工作表 '容器信息'
统计信息已写入工作表 '统计信息'
Excel报告已生成: reports/allure_report_25-08-18.xlsx

✅ 转换完成！Excel文件已保存到: reports/allure_report_25-08-18.xlsx
```

## 注意事项

1. 确保Allure报告已经生成（存在allure-results目录）
2. 确保有足够的磁盘空间存储Excel文件
3. 如果同一天多次运行，新文件会覆盖旧文件
4. Excel文件可以用Microsoft Excel、LibreOffice Calc等软件打开

## 扩展功能

如需添加更多字段或自定义输出格式，可以修改`AllureToExcelConverter`类中的相关方法。
