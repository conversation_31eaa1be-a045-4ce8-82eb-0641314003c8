# 应用安装功能使用说明

## 功能概述

新增的应用安装功能可以根据应用名称自动查找并安装APK文件，主要包含以下步骤：

1. **接收应用名称** - 例如 "WhatsApp", "Chrome", "WeChat" 等
2. **检查应用是否已安装** - 通过包名检查应用安装状态
3. **查找APK文件** - 在指定目录下搜索对应的APK文件
4. **安装应用** - 使用adb命令安装到手机

## 使用方法

### 命令行使用

```bash
# 基本用法 - 安装WhatsApp（默认在/data目录搜索APK）
python adb_process_monitor.py --install-app WhatsApp

# 指定APK搜索目录
python adb_process_monitor.py --install-app Chrome --apk-dir /sdcard/Download

# 安装并导出结果
python adb_process_monitor.py --install-app WeChat --export install_result.json
```

### 编程方式使用

```python
from tools.adb_process_monitor import AdbProcessMonitor

# 创建监控器实例
monitor = AdbProcessMonitor()

# 安装应用
result = monitor.install_app_by_name("WhatsApp", "/data")

# 打印格式化结果
print(monitor.format_install_result(result))

# 检查安装结果
if result['install_success']:
    print("安装成功!")
elif result['already_installed']:
    print("应用已安装")
else:
    print(f"安装失败: {result['error']}")
```

## 支持的应用

### 社交应用
- WhatsApp → com.whatsapp
- WeChat → com.tencent.mm
- Telegram → org.telegram.messenger
- Facebook → com.facebook.katana
- Instagram → com.instagram.android
- Twitter → com.twitter.android

### 浏览器
- Chrome → com.android.chrome
- Firefox → org.mozilla.firefox
- Edge → com.microsoft.emmx
- Opera → com.opera.browser

### 媒体应用
- YouTube → com.google.android.youtube
- Netflix → com.netflix.mediaclient
- Spotify → com.spotify.music

### 工具应用
- Gmail → com.google.android.gm
- Google Drive → com.google.android.apps.docs
- Zoom → us.zoom.videomeetings

### 游戏
- PUBG → com.tencent.ig
- Minecraft → com.mojang.minecraftpe

## 返回结果格式

```python
{
    'app_name': str,           # 应用名称
    'package_name': str,       # 包名
    'already_installed': bool, # 是否已安装
    'apk_found': bool,        # 是否找到APK文件
    'apk_path': str,          # APK文件路径
    'install_success': bool,   # 安装是否成功
    'install_output': str,     # 安装输出信息
    'error': str              # 错误信息
}
```

## 测试功能

```bash
# 测试包名映射和安装状态检查
python tools/test_app_install.py

# 测试实际安装功能
python tools/test_app_install.py install
```

## 注意事项

1. **APK文件位置** - 确保APK文件存在于指定的搜索目录中
2. **文件权限** - 确保有读取APK文件的权限
3. **设备连接** - 确保设备已通过ADB连接
4. **安装权限** - 可能需要在设备上允许未知来源安装
5. **存储空间** - 确保设备有足够的存储空间

## 错误处理

- 如果应用名称无法识别，会返回错误信息
- 如果APK文件未找到，会在多个位置搜索
- 如果安装失败，会返回详细的错误信息
- 支持导出详细的安装日志到JSON文件
