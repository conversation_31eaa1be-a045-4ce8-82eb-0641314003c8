#!/usr/bin/env python3
"""
测试应用安装功能
"""
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from tools.adb_process_monitor import AdbProcessMonitor

def test_app_install():
    """测试应用安装功能"""
    monitor = AdbProcessMonitor()
    
    # 测试应用名称列表
    test_apps = [
        "WhatsApp",
        "Chrome", 
        "WeChat",
        "YouTube",
        "Gmail"
    ]
    
    print("🧪 开始测试应用安装功能...")
    print("=" * 60)
    
    for app_name in test_apps:
        print(f"\n📱 测试应用: {app_name}")
        print("-" * 40)
        
        # 测试包名映射
        package_name = monitor._get_package_name_by_app_name(app_name)
        if package_name:
            print(f"✅ 包名映射: {app_name} -> {package_name}")
            
            # 测试是否已安装
            is_installed = monitor._is_package_installed(package_name)
            print(f"📦 安装状态: {'已安装' if is_installed else '未安装'}")
        else:
            print(f"❌ 未找到包名映射: {app_name}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")

def test_install_specific_app():
    """测试安装特定应用"""
    monitor = AdbProcessMonitor()
    
    # 可以修改这里测试特定应用
    app_name = "Chrome"
    apk_dir = "/data"  # 可以修改为实际的APK目录
    
    print(f"🔧 测试安装应用: {app_name}")
    print(f"📁 APK搜索目录: {apk_dir}")
    print("=" * 60)
    
    result = monitor.install_app_by_name(app_name, apk_dir)
    print(monitor.format_install_result(result))

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == "install":
        test_install_specific_app()
    else:
        test_app_install()
