import os
from pathlib import Path

import requests

import hashlib
import base64
import hmac

import json

import lark_oapi as lark
from lark_oapi.api.im.v1 import *


CURRENT_PATH = os.path.dirname(__file__)  # 当前文件所在目录
CURRENT_DIR = Path(__file__)
SRC_DIR = CURRENT_DIR.parent.parent
DATA_JSON_DIR = os.path.join(SRC_DIR, 'data', 'json')


def gen_sign(timestamp, secret):
    # 拼接timestamp和secret
    string_to_sign = '{}\n{}'.format(timestamp, secret)
    hmac_code = hmac.new(string_to_sign.encode("utf-8"), digestmod=hashlib.sha256).digest()

    # 对结果进行base64处理
    sign = base64.b64encode(hmac_code).decode('utf-8')

    return sign


class FeishuBase:

    def __init__(self):
        self.chat_id = 'oc_fe3f1b0dd938bd1987165cfd2f2a263b'  # 当前默认取独立产品测试群组
        self.user_access_token = '**********************************************'  # 先写死，待后续处理

    # SDK 使用说明: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/python--sdk/preparations-before-development
    # 以下示例代码默认根据文档示例值填充，如果存在代码问题，请在 API 调试台填上相关必要参数后再复制代码使用
    def get_group_members(self):
        # 创建client
        # 使用 user_access_token 需开启 token 配置, 并在 request_option 中配置 token
        client = lark.Client.builder() \
            .enable_set_token(True) \
            .log_level(lark.LogLevel.DEBUG) \
            .build()

        # 构造请求对象  【获取指定群组人员信息-当前默认取独立产品测试群组】
        request: GetChatMembersRequest = GetChatMembersRequest.builder() \
            .chat_id(self.chat_id) \
            .member_id_type("user_id") \
            .build()

        # 发起请求
        option = lark.RequestOption.builder().user_access_token(
            self.user_access_token).build()
        response: GetChatMembersResponse = client.im.v1.chat_members.get(request, option)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.chat_members.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
            return

        # 处理业务结果
        # lark.logger.info(lark.JSON.marshal(response.data, indent=4))
        return lark.JSON.marshal(response.data, indent=4)

    def format_user_id_to_file(self):
        data = json.loads(self.get_group_members())
        tmp = {}
        for item in data['items']:
            tmp[item['name']] = item['member_id']
        # 将 json_file 文件内容写入到 json_file 文件中
        json_file = os.path.join(DATA_JSON_DIR, 'user_id_1.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(tmp, f, indent=4, ensure_ascii=False)

    def get_user_id(self, name):
        # json_file = os.path.join(DATA_JSON_DIR, 'user_id_1.json')
        json_file = os.path.join(DATA_JSON_DIR, 'user_id_all.json')
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            user_id = data.get(name.strip(), None)
            return user_id
        else:
            return None

    def get_user_id_by_name(self,name):
        # json_file = os.path.join(DATA_JSON_DIR, 'user_id_2.json')
        json_file = os.path.join(DATA_JSON_DIR, 'user_email_all.json')
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            user_id = data.get(name.strip(), None)
            return user_id
        else:
            return None


class FeishuBot:
    def __init__(self, webhook_url='https://open.feishu.cn/open-apis/bot/v2/hook/5121cd0e-997f-48a5-a4dd-75f9e6393e30'):
        # self.webhook_url = webhook_url # jira缺陷工具小群
        # self.webhook_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/425580ba-6c0c-42df-abd8-a7358f5d060f'  # 专项：Ella基础指令集自动化
        self.webhook_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/a2fe953f-21d3-41b7-a8e5-cf40dcca012b'  # 测开小群

    def send_bug_notification(self, bug_data):
        """
        Send bug information to Feishu group chat
        :param bug_data: Dictionary containing bug information
        :return: Response from Feishu API
        """
        message = {
            "msg_type": "interactive",
            "card": {
                "config": {
                    "wide_screen_mode": True
                },
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "content": f"**Bug ID**: {bug_data.get('key', 'N/A')}\n"
                                       f"**Summary**: {bug_data.get('summary', 'N/A')}\n"
                                       f"**Status**: {bug_data.get('status', 'N/A')}\n"
                                       f"**Priority**: {bug_data.get('priority', 'N/A')}\n"
                                       f"**Assignee**: {bug_data.get('assignee', 'N/A')}\n"
                                       f"**Created**: {bug_data.get('created', 'N/A')}\n"
                                       f"**Updated**: {bug_data.get('updated', 'N/A')}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "action",
                        "actions": [
                            {
                                "tag": "button",
                                "text": {
                                    "content": "View in Jira",
                                    "tag": "plain_text"
                                },
                                "type": "primary",
                                "url": bug_data.get('url', '')
                            }
                        ]
                    }
                ],
                "header": {
                    "title": {
                        "content": f"New Bug Update: {bug_data.get('key', 'N/A')}",
                        "tag": "plain_text"
                    },
                    "template": "red"
                }
            }
        }

        try:
            response = requests.post(
                self.webhook_url,
                headers={'Content-Type': 'application/json'},
                data=json.dumps(message)
            )
            return response.json()
        except Exception as e:
            print(f"Error sending Feishu notification: {str(e)}")
            return None

    def send_bug_notification_v1(self, bug_data, notification_type='待处理bug列表'):
        """
        Send bug information to Feishu group chat
        :param bug_data: Dictionary containing bug information
        :return: Response from Feishu API
        """
        diy_data = {}
        for bug in bug_data:
            diy_data.update({
                bug['issue_key']: {
                    'assignee': bug['assignee'],
                    'status': bug['status'],
                    'summary': bug['summary'],
                    'url': bug['url'],
                }
            })
        message = {
            "msg_type": "text",
            "content": {
                "text": f"<at user_id=\"all\">所有人</at> {notification_type}：" + "\n" + json.dumps(diy_data, indent=2,
                                                                                                    ensure_ascii=False)
            }

        }

        try:
            response = requests.post(
                self.webhook_url,
                headers={'Content-Type': 'application/json'},
                data=json.dumps(message)
            )
            return response.json()
        except Exception as e:
            print(f"Error sending Feishu notification: {str(e)}")
            return None

    def send_bug_notification_v2(self, bug_data, notification_type='待处理bug列表'):
        """
        Send bug information to Feishu group chat
        :param bug_data: Dictionary containing bug information
        :return: Response from Feishu API
        """
        diy_data = {}
        # print(bug_data)
        for bug in bug_data:
            # print(bug)
            # print(bug['assignee'])
            if bug['assignee'] not in diy_data.keys():
                diy_data.update({
                    bug['assignee']: [
                        {
                            'issue_key': bug['issue_key'],
                            'status': bug['status'],
                            'summary': bug['summary'],
                            'url': bug['url'],
                        }
                    ]
                })
            else:
                diy_data[bug['assignee']].append(
                    {
                        'issue_key': bug['issue_key'],
                        'status': bug['status'],
                        'summary': bug['summary'],
                        'url': bug['url'],
                    }
                )

        message = {
            "msg_type": "text",
            "content": {
                # "text": f"<at user_id=\"all\">所有人</at> {notification_type}：" + "\n" + json.dumps(diy_data, indent=2,ensure_ascii=False)
                "text": f"<at user_id=\"921217205094391809\">卜昌义</at> {notification_type}：" + "\n" + json.dumps(
                    diy_data, indent=2, ensure_ascii=False)
            }

        }

        try:
            response = requests.post(
                self.webhook_url,
                headers={'Content-Type': 'application/json'},
                data=json.dumps(message)
            )
            return response.json()
        except Exception as e:
            print(f"Error sending Feishu notification: {str(e)}")
            return None

    def send_bug_notification_v3(self, assignee, bug_data, notification_type='待处理bug列表'):
        """
        Send bug information to Feishu group chat
        :param bug_data: Dictionary containing bug information
        :return: Response from Feishu API
        """

        message = {
            "msg_type": "text",
            "content": {
                "text": f"{assignee}:{notification_type}：" + "\n" + json.dumps(bug_data, indent=2, ensure_ascii=False)
            }
        }

        try:
            response = requests.post(
                self.webhook_url,
                headers={'Content-Type': 'application/json'},
                data=json.dumps(message)
            )
            return response.json()
        except Exception as e:
            print(f"Error sending Feishu notification: {str(e)}")
            return None

    def send_bug_notification_v4(self, assignee, bug_data, notification_type='待处理bug列表'):
        """
        Send bug information to Feishu group chat
        :param assignee:
        :param bug_data: Dictionary containing bug information
        :return: Response from Feishu API
        """
        user_id = FeishuBase().get_user_id(assignee)
        if user_id:
            pass
        else:
            lark.logger.error(f"{assignee} is not in feishu user list")
            return None

        message = {
            "msg_type": "text",
            "content": {
                "text": f"<at user_id=\"{user_id}\">{assignee}</at> {notification_type}：" + "\n" + json.dumps(bug_data,
                                                                                                              indent=2,
                                                                                                              ensure_ascii=False)
            }
        }

        try:
            response = requests.post(
                self.webhook_url,
                headers={'Content-Type': 'application/json'},
                data=json.dumps(message)
            )
            return response.json()
        except Exception as e:
            print(f"Error sending Feishu notification: {str(e)}")
            return None

    def send_bug_notification_v5(self, assignee='', bug_data=None, notification_type='待处理bug列表'):

        # SDK 使用说明: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/python--sdk/preparations-before-development
        # 以下示例代码默认根据文档示例值填充，如果存在代码问题，请在 API 调试台填上相关必要参数后再复制代码使用
        # 复制该 Demo 后, 需要将 "YOUR_APP_ID", "YOUR_APP_SECRET" 替换为自己应用的 APP_ID, APP_SECRET.
        # 创建client
        client = lark.Client.builder() \
            .app_id("cli_a8821ea37977d01c") \
            .app_secret("YLuPbaYFE8MOaYUE5pyccdlywJtqkRPi") \
            .log_level(lark.LogLevel.DEBUG) \
            .build()

        user_id = FeishuBase().get_user_id(assignee)
        # if user_id =='921217205094391809':
        #     bug_data = str(bug_data).replace('\'','"')
        #     print(bug_data)
        if user_id:
            content = bug_data
            # print(content)
            # 构造请求对象
            request: CreateMessageRequest = CreateMessageRequest.builder() \
                .receive_id_type("user_id") \
                .request_body(CreateMessageRequestBody.builder()
                              .receive_id(user_id)
                              .msg_type("text")
                              .content(content)
                              .build()) \
                .build()

            # 发起请求
            response: CreateMessageResponse = client.im.v1.message.create(request)

            # 处理失败返回
            if not response.success():
                lark.logger.error(
                    f"client.im.v1.message.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
                return

            # 处理业务结果
            lark.logger.info(lark.JSON.marshal(response.data, indent=4))
        else:
            lark.logger.error(f"{assignee} is not in feishu user list")
    def send_bug_notification_v6(self, assignee='', bug_data=None, notification_type='待处理bug列表'):

        # SDK 使用说明: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/python--sdk/preparations-before-development
        # 以下示例代码默认根据文档示例值填充，如果存在代码问题，请在 API 调试台填上相关必要参数后再复制代码使用
        # 复制该 Demo 后, 需要将 "YOUR_APP_ID", "YOUR_APP_SECRET" 替换为自己应用的 APP_ID, APP_SECRET.
        # 创建client
        client = lark.Client.builder() \
            .app_id("cli_a8821ea37977d01c") \
            .app_secret("YLuPbaYFE8MOaYUE5pyccdlywJtqkRPi") \
            .log_level(lark.LogLevel.DEBUG) \
            .build()

        user_id = FeishuBase().get_user_id_by_name(assignee)
        # if user_id =='921217205094391809':
        #     bug_data = str(bug_data).replace('\'','"')
        #     print(bug_data)
        if user_id:
            content = bug_data
            # print(content)
            # 构造请求对象
            request: CreateMessageRequest = CreateMessageRequest.builder() \
                .receive_id_type("user_id") \
                .request_body(CreateMessageRequestBody.builder()
                              .receive_id(user_id)
                              .msg_type("text")
                              .content(content)
                              .build()) \
                .build()

            # 发起请求
            response: CreateMessageResponse = client.im.v1.message.create(request)

            # 处理失败返回
            if not response.success():
                lark.logger.error(
                    f"client.im.v1.message.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
                return

            # 处理业务结果
            lark.logger.info(lark.JSON.marshal(response.data, indent=4))
        else:
            self.send_bug_notification_to_dev(assignee='changyi.bu', bug_data=bug_data)
            lark.logger.error(f"{assignee} is not in feishu user list")
    def send_bug_notification_to_dev(self, assignee='', bug_data=None, notification_type='待处理bug列表'):

        # SDK 使用说明: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/python--sdk/preparations-before-development
        # 以下示例代码默认根据文档示例值填充，如果存在代码问题，请在 API 调试台填上相关必要参数后再复制代码使用
        # 复制该 Demo 后, 需要将 "YOUR_APP_ID", "YOUR_APP_SECRET" 替换为自己应用的 APP_ID, APP_SECRET.
        # 创建client
        client = lark.Client.builder() \
            .app_id("cli_a8821ea37977d01c") \
            .app_secret("YLuPbaYFE8MOaYUE5pyccdlywJtqkRPi") \
            .log_level(lark.LogLevel.DEBUG) \
            .build()

        user_id = FeishuBase().get_user_id_by_name('changyi.bu')
        if user_id:
            content = bug_data
            # print(content)
            # 构造请求对象
            request: CreateMessageRequest = CreateMessageRequest.builder() \
                .receive_id_type("user_id") \
                .request_body(CreateMessageRequestBody.builder()
                              .receive_id(user_id)
                              .msg_type("text")
                              .content(content)
                              .build()) \
                .build()

            # 发起请求
            response: CreateMessageResponse = client.im.v1.message.create(request)

            # 处理失败返回
            if not response.success():
                lark.logger.error(
                    f"client.im.v1.message.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
                return

            # 处理业务结果
            lark.logger.info(lark.JSON.marshal(response.data, indent=4))
        else:
            lark.logger.error(f"{assignee} is not in feishu user list")


if __name__ == '__main__':
    data = [{'assignee': 'yuting.qiu(仇玉婷)', 'components': 'Dialer', 'created': '2024/11/07', 'issue_type': '',
             'issue_key': 'TOS1501-23550', 'priority': '', 'labels': 'None', 'status': 'Verified',
             'summary': '【独立产品】【X6855】【STR4-1】【Smartcaller】【SR-NW-0001-004】进入通话记录设置界面，来电语音播报入口未放在声音和震动下面，和交互不一致',
             'updated': '2025/03/31', 'versions': 'X6855-H8917ABEFIJPQ-V-OP-241107V620',
             'url': 'http://jira.transsion.com/browse/TOS1501-23550'},
            {'assignee': 'bingqian.bai(白冰倩) [X] (Inactive)', 'components': 'HiOSLauncher', 'created': '2024/10/28',
             'issue_type': '', 'issue_key': 'TOS1501-18415', 'priority': '', 'labels': 'tOS16.0解决转task',
             'status': 'Resolved',
             'summary': '【交付一部】【独立产品】【CM8】【STR4】【HIOSLauncher】【偶现】点击桌面文件夹，展开文件夹后上划退出，再次点击文件夹，高概率出现重新点击文件夹，无法点击展开文件夹（有触屏的报点）',
             'updated': '2025/04/11', 'versions': 'CM8-H973A-V-OP-241025V395',
             'url': 'http://jira.transsion.com/browse/TOS1501-18415'},
            {'assignee': 'xuanxuan.mi(宓玄玄)', 'components': 'SplitScreen', 'created': '2024/06/25', 'issue_type': '',
             'issue_key': 'X6880H891-436', 'priority': '', 'labels': 'None', 'status': 'Resolved',
             'summary': '【独立产品】【X6880】【PreAlpha】【分屏】【三方】MX Player横屏播放视频时分屏退出分屏出现一半黑屏现象见视频',
             'updated': '2025/02/18', 'versions': 'X6880-H8912ABCDEFGHIJ-U-OP-240625V142',
             'url': 'http://jira.transsion.com/browse/X6880H891-436'},
            {'assignee': 'xuanxuan.mi(宓玄玄)', 'components': 'SplitScreen', 'created': '2024/06/11', 'issue_type': '',
             'issue_key': 'OS150V-2035', 'priority': '', 'labels': 'TCH-FF', 'status': 'Resolved',
             'summary': '【独立产品】【CL8】【Alpha】【分屏】智能面板拖出应用无法分屏', 'updated': '2025/02/18',
             'versions': 'CL8-H962IN-V-OP-240611V6349', 'url': 'http://jira.transsion.com/browse/OS150V-2035'},
            {'assignee': '段潇洋(xiaoyang.duan) [X] (Inactive)', 'components': 'HiOSLauncher', 'created': '2025/01/13',
             'issue_type': '', 'issue_key': 'TOS1510-13486', 'priority': '', 'labels': 'None', 'status': 'Verified',
             'summary': '【粉丝反馈】桌面时钟先部件无法同一行，添加两个。场景在国外，要加一个国外的时钟，一个国内的时钟',
             'updated': '2025/04/01', 'versions': 'CM5-15.1.0.012(OPPJ001PF001PJ)STG250323022035_SU',
             'url': 'http://jira.transsion.com/browse/TOS1510-13486'},
            {'assignee': '朱柯柯(keke.zhu)', 'components': 'Dynamic Color', 'created': '2023/12/14', 'issue_type': '',
             'issue_key': 'X6871H962-2265', 'priority': '', 'labels': 'None', 'status': 'Resolved',
             'summary': 'CLONE - 【系统产品】【X6871】【Alpha】【动态颜色】冷进入动态颜色设置界面会出现一会白屏',
             'updated': '2025/03/11', 'versions': 'X6871-H962CF-U-OP-231213V293',
             'url': 'http://jira.transsion.com/browse/X6871H962-2265'},
            {'assignee': 'ju.zhu(朱菊)', 'components': 'SplitScreen', 'created': '2023/11/15', 'issue_type': '',
             'issue_key': 'KJ7H895-3149', 'priority': '', 'labels': 'code不在mp', 'status': 'Resolved',
             'summary': '【系统产品】【KJ7】【Beta】【分屏】横屏应用界面触发、退出分屏异常', 'updated': '2023/12/27',
             'versions': 'KJ7-H895MNO-U-OP-231115V615', 'url': 'http://jira.transsion.com/browse/KJ7H895-3149'}]

    webhook_url = "https://open.feishu.cn/open-apis/bot/v2/hook/5121cd0e-997f-48a5-a4dd-75f9e6393e30"
    feishu_bot = FeishuBot(webhook_url)
    #
    # response = feishu_bot.send_bug_notification_v1(data)
    # print(response)
    # bug_list =[{
    #     "summary": "【交付一部】【独立产品】【S688LN】【STR4】【AI语音】查询SPD支持Ella，实际不支持",
    #     "url": "http://jira.transsion.com/browse/TOS1510-27646"
    # },{
    #     "summary": "【交付一部】【独立产品】【KM9】【STR4】【Ella识屏】查询PD支持识屏，实际不支持，请求合入。",
    #     "url": "http://jira.transsion.com/browse/TOS1510-27626"
    # },{
    #     "summary": "【交付一部】【系统产品】【KM7】【STR4-2】【Ella识屏】【体验专项】【主观体验】在桌面首页截图后点击识屏，识屏完成后等待一会儿，识别出来的天气时钟小部件中的时间会变蓝",
    #     "url": "http://jira.transsion.com/browse/TOS1510-27658"
    # }]
    # bug_list =[{'issue_key': 'TOS1510-28093', 'status': 'Submitted', 'summary': '【交付一部】【独立产品】【X6886】【STR4-2】【Ella】【4.4.0.170】中文下，AI搜索指令Feedback弹窗的问题类型错误', 'url': 'http://jira.transsion.com/browse/TOS1510-28093'}]
    bug_list =[{'issue_key': 'TOS1510-27942', 'status': 'Submitted', 'summary': '【交付一部】【系统产品】【X6876】【STR4】【Dialer】【体验专项】【主观体验】 通话助手界面预览图不会进行轮播，与其他界面预览图不一致', 'url': 'http://jira.transsion.com/browse/TOS1510-27942'}, {'issue_key': 'TOS1510-27875', 'status': 'Submitted', 'summary': '[Overseas] [Russia] [CM7]  [Subjective experience][Impossible to choose to display contacts from any account]', 'url': 'http://jira.transsion.com/browse/TOS1510-27875'}, {'issue_key': 'LJ8KH781-1278', 'status': 'Submitted', 'summary': '【粉丝反馈】【中国】【产品内部试用】电话的2个小部件显示一样，无法区分', 'url': 'http://jira-ex.transsion.com:6001/browse/LJ8KH781-1278'}, {'issue_key': 'LJ8KH781-1256', 'status': 'Submitted', 'summary': "[Overseas][India][Chennai][Thundersoft FT][LJ8k][Static]_DUT fails to display the device's location during an emergency call for Jio operator", 'url': 'http://jira-ex.transsion.com:6001/browse/LJ8KH781-1256'}, {'issue_key': 'X6725O1501-1610', 'status': 'Submitted', 'summary': '【粉丝反馈】【中国】打开拨号盘～设置，点击骚扰拦截，进入下一级菜单，这个界面的骚扰拦截字体都很细。对比打开同一级“陌生号码识别”和“SIM卡和网络设置”的下一级菜单，这两个界面的字体都比较粗。希望骚扰拦截界面的字体样式和其他同级菜单下的样式粗细一样', 'url': 'http://jira-ex.transsion.com:6001/browse/X6725O1501-1610'}, {'issue_key': 'CM8H973-4987', 'status': 'Submitted', 'summary': '[Free Test][MENAAE][Overseas][Jordan] [CM8][dialer] Call assistant icon overlaps on "on hold " in dialer in Arabic language.', 'url': 'http://jira-ex.transsion.com:6001/browse/CM8H973-4987'}, {'issue_key': 'CM8H973-4984', 'status': 'Submitted', 'summary': '[Free Test][MENAAE][Overseas][Jordan] [CM8][Dialer] wrong name end with special character displayed in AI auto answer interface during the call.', 'url': 'http://jira-ex.transsion.com:6001/browse/CM8H973-4984'}]

    new_bug_list = json.dumps(data).replace('"', '\\"')
    print(new_bug_list)
    # feishu_bot.send_bug_notification_v5(assignee='卜昌义',bug_data="{\"text\":\"Hello World\"}")
    feishu_bot.send_bug_notification_v6(assignee='zhi.li',bug_data="{\"text\":\"Hello World\"}")



# feishu_base = FeishuBase()
# print(feishu_base.get_group_members())
# print(feishu_base.get_user_id(name=' 卜昌义 '))
# feishu_base.format_user_id_to_file()
