#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Allure报告转Excel工具
将allure报告中的用例执行结果信息存储到以当天（YY-MM-DD）命名的.xlsx表格中
"""

import json
import os
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse
import yaml
import sys

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from tools.robot_tool import Robot
import yaml
import sys

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))


class AllureToExcelConverter:
    """Allure报告转Excel转换器"""
    
    def __init__(self, reports_dir: str = "reports"):
        """
        初始化转换器
        
        Args:
            reports_dir: 报告目录路径，默认为 "reports"
        """
        self.reports_dir = Path(reports_dir)
        self.allure_results_dir = self.reports_dir / "allure-results"
        self.output_dir = self.reports_dir
        
    def _get_today_filename(self) -> str:
        """获取今天日期的文件名"""
        today = datetime.now().strftime("%y-%m-%d_%H-%M-%S")
        return f"allure_report_{today}.xlsx"
    
    def _read_json_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        读取JSON文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            解析后的JSON数据，如果读取失败返回None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError, UnicodeDecodeError) as e:
            print(f"读取文件 {file_path} 失败: {e}")
            return None
    
    def _extract_test_results(self) -> List[Dict[str, Any]]:
        """
        从allure-results目录提取测试结果

        Returns:
            测试结果列表
        """
        test_results = []

        # 检查allure-results目录
        if not self.allure_results_dir.exists():
            print(f"❌ Allure结果目录不存在: {self.allure_results_dir}")
            print(f"当前工作目录: {Path.cwd()}")
            print(f"报告目录: {self.reports_dir}")
            return test_results

        print(f"✅ 找到Allure结果目录: {self.allure_results_dir}")
        
        # 读取所有result.json文件
        result_files = list(self.allure_results_dir.glob("*-result.json"))
        print(f"找到 {len(result_files)} 个测试结果文件")
        
        for result_file in result_files:
            result_data = self._read_json_file(result_file)
            if result_data:
                # 提取基本信息
                test_case = {
                    'UUID': result_data.get('uuid', ''),
                    '测试用例名称': result_data.get('name', ''),
                    '测试状态': result_data.get('status', ''),
                    '描述': result_data.get('description', ''),
                    '完整名称': result_data.get('fullName', ''),
                    '历史ID': result_data.get('historyId', ''),
                    '测试用例ID': result_data.get('testCaseId', ''),
                    '开始时间': self._format_timestamp(result_data.get('start')),
                    '结束时间': self._format_timestamp(result_data.get('stop')),
                    '执行时长(ms)': self._calculate_duration(result_data.get('start'), result_data.get('stop')),
                }

                # 提取失败原因信息
                status_details = result_data.get('statusDetails', {})
                test_case['失败消息'] = status_details.get('message', '')
                test_case['失败堆栈'] = status_details.get('trace', '')
                test_case['失败原因'] = self._extract_failure_reason(status_details)
                
                # 提取标签信息
                labels = result_data.get('labels', [])
                for label in labels:
                    label_name = label.get('name', '')
                    label_value = label.get('value', '')
                    if label_name:
                        test_case[f'标签_{label_name}'] = label_value
                
                # 提取步骤信息
                steps = result_data.get('steps', [])
                test_case['步骤数量'] = len(steps)
                test_case['步骤详情'] = self._extract_steps_info(steps)
                
                # 提取附件信息
                attachments = result_data.get('attachments', [])
                test_case['附件数量'] = len(attachments)
                test_case['附件详情'] = self._extract_attachments_info(attachments)
                
                # 提取参数信息
                parameters = result_data.get('parameters', [])
                test_case['参数数量'] = len(parameters)
                test_case['参数详情'] = self._extract_parameters_info(parameters)
                
                test_results.append(test_case)
        
        return test_results
    
    def _format_timestamp(self, timestamp: Optional[int]) -> str:
        """
        格式化时间戳
        
        Args:
            timestamp: 毫秒时间戳
            
        Returns:
            格式化的时间字符串
        """
        if timestamp is None:
            return ''
        try:
            # Allure使用毫秒时间戳
            dt = datetime.fromtimestamp(timestamp / 1000)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except (ValueError, OSError):
            return str(timestamp)
    
    def _calculate_duration(self, start: Optional[int], stop: Optional[int]) -> str:
        """
        计算执行时长

        Args:
            start: 开始时间戳
            stop: 结束时间戳

        Returns:
            执行时长（毫秒）
        """
        if start is None or stop is None:
            return ''
        return str(stop - start)

    def _extract_failure_reason(self, status_details: Dict[str, Any]) -> str:
        """
        提取失败原因的简要描述

        Args:
            status_details: 状态详情字典

        Returns:
            失败原因的简要描述
        """
        if not status_details:
            return ''

        message = status_details.get('message', '')
        if not message:
            return ''

        # 提取AssertionError的主要信息
        if 'AssertionError:' in message:
            # 提取AssertionError后的第一行作为主要错误信息
            lines = message.split('\n')
            for line in lines:
                if 'AssertionError:' in line:
                    # 去掉"AssertionError: "前缀
                    reason = line.replace('AssertionError:', '').strip()
                    return reason

        # 提取其他异常类型的信息
        if ':' in message:
            # 取第一行的异常信息
            first_line = message.split('\n')[0]
            if ':' in first_line:
                return first_line.split(':', 1)[1].strip()

        # 如果没有特殊格式，返回第一行
        return message.split('\n')[0].strip()
    
    def _extract_steps_info(self, steps: List[Dict[str, Any]]) -> str:
        """
        提取步骤信息

        Args:
            steps: 步骤列表

        Returns:
            步骤信息字符串
        """
        if not steps:
            return ''

        step_info = []
        for i, step in enumerate(steps, 1):
            step_name = step.get('name', f'步骤{i}')
            step_status = step.get('status', 'unknown')

            # 如果步骤失败，添加失败信息
            if step_status in ['failed', 'broken']:
                step_details = step.get('statusDetails', {})
                if step_details:
                    failure_reason = self._extract_failure_reason(step_details)
                    if failure_reason:
                        step_info.append(f"{i}. {step_name} ({step_status}) - {failure_reason}")
                    else:
                        step_info.append(f"{i}. {step_name} ({step_status})")
                else:
                    step_info.append(f"{i}. {step_name} ({step_status})")
            else:
                step_info.append(f"{i}. {step_name} ({step_status})")

        return '; '.join(step_info)
    
    def _extract_attachments_info(self, attachments: List[Dict[str, Any]]) -> str:
        """
        提取附件信息
        
        Args:
            attachments: 附件列表
            
        Returns:
            附件信息字符串
        """
        if not attachments:
            return ''
        
        attachment_info = []
        for attachment in attachments:
            name = attachment.get('name', '未知附件')
            type_info = attachment.get('type', '未知类型')
            source = attachment.get('source', '')
            attachment_info.append(f"{name} ({type_info})")
        
        return '; '.join(attachment_info)
    
    def _extract_parameters_info(self, parameters: List[Dict[str, Any]]) -> str:
        """
        提取参数信息
        
        Args:
            parameters: 参数列表
            
        Returns:
            参数信息字符串
        """
        if not parameters:
            return ''
        
        param_info = []
        for param in parameters:
            name = param.get('name', '未知参数')
            value = param.get('value', '')
            param_info.append(f"{name}={value}")
        
        return '; '.join(param_info)
    
    def _extract_container_info(self) -> List[Dict[str, Any]]:
        """
        从container.json文件提取容器信息
        
        Returns:
            容器信息列表
        """
        container_info = []
        
        # 读取所有container.json文件
        container_files = list(self.allure_results_dir.glob("*-container.json"))
        print(f"找到 {len(container_files)} 个容器文件")
        
        for container_file in container_files:
            container_data = self._read_json_file(container_file)
            if container_data:
                container = {
                    'UUID': container_data.get('uuid', ''),
                    '子项': ', '.join(container_data.get('children', [])),
                    '开始时间': self._format_timestamp(container_data.get('start')),
                    '结束时间': self._format_timestamp(container_data.get('stop')),
                    '执行时长(ms)': self._calculate_duration(container_data.get('start'), container_data.get('stop')),
                    '前置操作': self._extract_hooks_info(container_data.get('befores', [])),
                    '后置操作': self._extract_hooks_info(container_data.get('afters', [])),
                }
                container_info.append(container)
        
        return container_info
    
    def _extract_hooks_info(self, hooks: List[Dict[str, Any]]) -> str:
        """
        提取钩子函数信息
        
        Args:
            hooks: 钩子函数列表
            
        Returns:
            钩子函数信息字符串
        """
        if not hooks:
            return ''
        
        hook_info = []
        for hook in hooks:
            name = hook.get('name', '未知钩子')
            status = hook.get('status', 'unknown')
            hook_info.append(f"{name} ({status})")
        
        return '; '.join(hook_info)
    
    def convert_to_excel(self) -> str:
        """
        将allure报告转换为Excel文件

        Returns:
            生成的Excel文件路径
        """
        print("开始提取Allure报告数据...")

        # 检查并创建输出目录
        if not self.output_dir.exists():
            print(f"创建输出目录: {self.output_dir}")
            self.output_dir.mkdir(parents=True, exist_ok=True)

        # 提取测试结果
        test_results = self._extract_test_results()
        print(f"提取到 {len(test_results)} 个测试用例")

        # 提取容器信息
        container_info = self._extract_container_info()
        print(f"提取到 {len(container_info)} 个容器信息")

        # 生成Excel文件
        output_file = self.output_dir / self._get_today_filename()

        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 测试用例结果工作表
                if test_results:
                    df_results = pd.DataFrame(test_results)
                    df_results.to_excel(writer, sheet_name='测试用例结果', index=False)
                    print(f"测试用例结果已写入工作表 '测试用例结果'")

                # 容器信息工作表
                if container_info:
                    df_containers = pd.DataFrame(container_info)
                    df_containers.to_excel(writer, sheet_name='容器信息', index=False)
                    print(f"容器信息已写入工作表 '容器信息'")

                # 统计信息工作表
                self._create_summary_sheet(writer, test_results)

            print(f"Excel报告已生成: {output_file}")
            return str(output_file)

        except Exception as e:
            print(f"生成Excel文件时出错: {e}")
            raise
    
    def _create_summary_sheet(self, writer: pd.ExcelWriter, test_results: List[Dict[str, Any]]):
        """
        创建统计信息工作表并发送飞书通知

        Args:
            writer: Excel写入器
            test_results: 测试结果列表
        """
        if not test_results:
            return

        # 统计各种状态的用例数量
        status_counts = {}
        for result in test_results:
            status = result.get('测试状态', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1

        # 创建统计数据
        summary_data = []
        total_cases = len(test_results)

        summary_data.append(['总用例数', total_cases])
        summary_data.append(['', ''])  # 空行

        for status, count in status_counts.items():
            percentage = (count / total_cases * 100) if total_cases > 0 else 0
            summary_data.append([f'{status}用例数', f'{count} ({percentage:.1f}%)'])

        # 添加时间信息
        summary_data.append(['', ''])  # 空行
        summary_data.append(['报告生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])

        # 写入Excel
        df_summary = pd.DataFrame(summary_data, columns=['统计项', '数值'])
        df_summary.to_excel(writer, sheet_name='统计信息', index=False)
        print(f"统计信息已写入工作表 '统计信息'")

        # 发送飞书通知
        self._send_feishu_notification(test_results, status_counts, total_cases)

    def _load_device_info(self) -> Dict[str, Any]:
        """
        从devices.yaml加载当前设备信息

        Returns:
            Dict[str, Any]: 设备信息字典
        """
        try:
            devices_config_path = project_root / "config" / "devices.yaml"
            with open(devices_config_path, 'r', encoding='utf-8') as f:
                devices_config = yaml.safe_load(f)

            current_device_key = devices_config.get('current_device', 'current_device')
            device_info = devices_config.get('devices', {}).get(current_device_key, {})

            return device_info
        except Exception as e:
            print(f"加载设备信息失败: {e}")
            return {
                'device_id': 'Unknown',
                'device_name': 'Unknown Device',
                'android_version': 'Unknown',
                'brand': 'Unknown',
                'model': 'Unknown',
                'platform_version': 'Unknown'
            }

    def _get_test_time_info(self) -> Dict[str, Any]:
        """
        从summary.json获取测试时间信息

        Returns:
            Dict[str, Any]: 包含开始时间、结束时间、持续时长的字典
        """
        try:
            # 构建summary.json文件路径
            summary_path = Path(self.reports_dir) / "allure-report" / "widgets" / "summary.json"

            if not summary_path.exists():
                print(f"警告: summary.json文件不存在: {summary_path}")
                return self._get_default_time_info()

            # 读取summary.json文件
            with open(summary_path, 'r', encoding='utf-8') as f:
                summary_data = json.load(f)

            # 提取时间信息
            time_info = summary_data.get('time', {})

            # 时间戳转换（毫秒转秒）
            start_timestamp = time_info.get('start', 0) / 1000
            stop_timestamp = time_info.get('stop', 0) / 1000
            duration_ms = time_info.get('duration', 0)

            # 转换为可读时间格式
            start_time = datetime.fromtimestamp(start_timestamp).strftime('%H:%M:%S') if start_timestamp > 0 else "未知"
            end_time = datetime.fromtimestamp(stop_timestamp).strftime('%H:%M:%S') if stop_timestamp > 0 else "未知"

            # 持续时长转换
            duration_seconds = duration_ms / 1000 if duration_ms > 0 else 0.0
            duration_minutes = duration_seconds / 60 if duration_seconds > 0 else 0.0

            # 格式化持续时长显示
            if duration_seconds >= 3600:  # 超过1小时
                hours = int(duration_seconds // 3600)
                minutes = int((duration_seconds % 3600) // 60)
                seconds = int(duration_seconds % 60)
                duration_display = f"{hours}小时{minutes}分{seconds}秒"
            elif duration_seconds >= 60:  # 超过1分钟
                minutes = int(duration_seconds // 60)
                seconds = int(duration_seconds % 60)
                duration_display = f"{minutes}分{seconds}秒"
            else:  # 小于1分钟
                duration_display = f"{duration_seconds:.1f}秒"

            return {
                'start_time': start_time,
                'end_time': end_time,
                'duration_minutes': round(duration_minutes, 1),
                'duration_seconds': round(duration_seconds, 1),
                'duration_display': duration_display,
                'start_timestamp': start_timestamp,
                'stop_timestamp': stop_timestamp,
                'duration_ms': duration_ms
            }

        except Exception as e:
            print(f"从summary.json获取时间信息失败: {e}")
            return self._get_default_time_info()

    def _get_default_time_info(self) -> Dict[str, Any]:
        """
        获取默认时间信息

        Returns:
            Dict[str, Any]: 默认时间信息
        """
        current_time = datetime.now()
        return {
            'start_time': current_time.strftime('%H:%M:%S'),
            'end_time': current_time.strftime('%H:%M:%S'),
            'duration_minutes': 0.0,
            'start_timestamp': current_time.timestamp(),
            'stop_timestamp': current_time.timestamp(),
            'duration_ms': 0
        }

    def _send_feishu_notification(self, test_results: List[Dict[str, Any]],
                                  status_counts: Dict[str, int],
                                  total_cases: int):
        """
        发送飞书通知

        Args:
            test_results: 测试结果列表
            status_counts: 状态统计
            total_cases: 总用例数
        """
        try:
            # 加载设备信息
            device_info = self._load_device_info()

            # 计算统计信息
            success_count = status_counts.get('passed', 0) + status_counts.get('成功', 0)
            failed_count = status_counts.get('failed', 0) + status_counts.get('失败', 0)
            success_rate = (success_count / total_cases * 100) if total_cases > 0 else 0

            # 获取测试时间信息
            time_info = self._get_test_time_info()

            # 获取当前日期
            current_date = datetime.now().strftime('%Y-%m-%d')

            # 构建通知内容
            title = f"[Ella] UI自动化挂测报告 - {current_date} - {device_info.get('device_id', 'Unknown')}"

            content = f"""**【核心指标】**
✅ **通过率**: {success_rate:.1f}%
🔢 **总用例数**: {total_cases}
✅ **成功数**: {success_count}
❌ **失败数**: {failed_count}
⏱ **执行时长**: {time_info['duration_display']}
⏰ **执行时段**: {time_info['start_time']} - {time_info['end_time']}

**【设备信息】**
**android_version**: '{device_info.get('android_version', 'Unknown')}'
**brand**: {device_info.get('brand', 'Unknown')}
**cpu_abi**: {device_info.get('cpu_abi', 'Unknown')}
**device_id**: {device_info.get('device_id', 'Unknown')}
**device_name**: {device_info.get('device_name', 'Unknown')}
**model**: {device_info.get('model', 'Unknown')}
**platform_version**: {device_info.get('platform_version', 'Unknown')}"""

            # 创建按钮
            buttons = [
                {
                    "text": "查看详细报告",
                    "url": "http://10.158.72.160:8000"
                }
            ]

            # 根据成功率选择卡片颜色
            if success_rate >= 90:
                template = "green"
            elif success_rate >= 70:
                template = "yellow"
            else:
                template = "red"

            # 发送飞书通知
            robot = Robot()
            response = robot.send_card_message(
                title=title,
                content=content,
                buttons=buttons,
                template=template,
                at_all=False  # 根据需要决定是否@所有人
            )

            if response:
                print("✅ 飞书通知发送成功")
            else:
                print("❌ 飞书通知发送失败")

        except Exception as e:
            print(f"发送飞书通知时出错: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将Allure报告转换为Excel文件')
    parser.add_argument('--reports-dir', '-r', default=r'D:\aigc\app_test\reports',
                       help='报告目录路径 (默认: reports)')
    
    args = parser.parse_args()
    
    # 创建转换器并执行转换
    converter = AllureToExcelConverter(args.reports_dir)
    
    try:
        output_file = converter.convert_to_excel()
        print(f"\n✅ 转换完成！Excel文件已保存到: {output_file}")
    except Exception as e:
        print(f"\n❌ 转换失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
