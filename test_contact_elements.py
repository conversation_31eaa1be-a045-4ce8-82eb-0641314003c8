#!/usr/bin/env python3
"""
测试 Ask Screen 联系人相关页面元素和方法
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pages.apps.ella.floating_page import EllaFloatingPage
from core.logger import log


def test_contact_elements_existence():
    """测试联系人相关元素是否正确定义"""
    print("\n=== 测试联系人元素定义 ===")
    
    try:
        floating_page = EllaFloatingPage()
        
        # 测试输入框元素
        input_elements = [
            'contact_name_input',
            'phone_number_input'
        ]
        
        # 测试文本显示元素
        text_elements = [
            'contact_name_text',
            'phone_number_text',
            'recognized_number_text'
        ]
        
        # 测试容器元素
        container_elements = [
            'contact_info_container',
            'contact_actions_container',
            'contact_card_container'
        ]
        
        # 测试按钮元素
        button_elements = [
            'save_contact_button',
            'add_to_contacts_button',
            'confirm_number_button',
            'cancel_number_button'
        ]
        
        # 测试对话框元素
        dialog_elements = [
            'number_confirm_dialog'
        ]
        
        # 测试备选定位器
        fallback_elements = [
            'contact_related_text',
            'number_related_text'
        ]
        
        all_elements = (input_elements + text_elements + container_elements + 
                       button_elements + dialog_elements + fallback_elements)
        
        success_count = 0
        for element_name in all_elements:
            try:
                element = getattr(floating_page, element_name)
                if element:
                    print(f"✅ {element_name}: 定义正确")
                    success_count += 1
                else:
                    print(f"❌ {element_name}: 定义为空")
            except AttributeError:
                print(f"❌ {element_name}: 未找到定义")
            except Exception as e:
                print(f"❌ {element_name}: 定义异常 - {e}")
        
        print(f"\n元素定义测试完成: {success_count}/{len(all_elements)} 个元素定义正确")
        
        # 测试 page_elements 字典
        print("\n=== 测试 page_elements 字典 ===")
        page_elements = floating_page.page_elements
        contact_elements_in_dict = [key for key in page_elements.keys() 
                                   if 'contact' in key or 'phone' in key or 'number' in key]
        
        print(f"page_elements 中的联系人相关元素:")
        for element in contact_elements_in_dict:
            print(f"  - {element}")
        
        print(f"✅ page_elements 包含 {len(contact_elements_in_dict)} 个联系人相关元素")
        
    except Exception as e:
        print(f"❌ 测试联系人元素定义异常: {e}")


def test_contact_methods():
    """测试联系人相关方法是否正确定义"""
    print("\n=== 测试联系人方法定义 ===")
    
    try:
        floating_page = EllaFloatingPage()
        
        # 定义要测试的方法
        methods_to_test = [
            'get_contact_name',
            'get_phone_number',
            'set_contact_name',
            'set_phone_number',
            'save_contact',
            'confirm_number',
            'cancel_number',
            'is_contact_info_visible',
            'get_contact_info'
        ]
        
        success_count = 0
        for method_name in methods_to_test:
            try:
                method = getattr(floating_page, method_name)
                if callable(method):
                    print(f"✅ {method_name}: 方法定义正确")
                    success_count += 1
                else:
                    print(f"❌ {method_name}: 不是可调用方法")
            except AttributeError:
                print(f"❌ {method_name}: 方法未找到")
            except Exception as e:
                print(f"❌ {method_name}: 方法定义异常 - {e}")
        
        print(f"\n方法定义测试完成: {success_count}/{len(methods_to_test)} 个方法定义正确")
        
    except Exception as e:
        print(f"❌ 测试联系人方法定义异常: {e}")


def test_contact_info_structure():
    """测试联系人信息结构"""
    print("\n=== 测试联系人信息结构 ===")
    
    try:
        floating_page = EllaFloatingPage()
        
        # 测试 get_contact_info 方法的返回结构
        contact_info = floating_page.get_contact_info()
        
        expected_keys = ['name', 'phone', 'visible']
        
        print(f"get_contact_info 返回结果: {contact_info}")
        
        if isinstance(contact_info, dict):
            print("✅ 返回类型为字典")
            
            missing_keys = []
            for key in expected_keys:
                if key in contact_info:
                    print(f"✅ 包含字段 '{key}': {type(contact_info[key])}")
                else:
                    missing_keys.append(key)
                    print(f"❌ 缺少字段 '{key}'")
            
            if not missing_keys:
                print("✅ 联系人信息结构完整")
            else:
                print(f"❌ 缺少字段: {missing_keys}")
        else:
            print(f"❌ 返回类型错误，期望 dict，实际 {type(contact_info)}")
        
    except Exception as e:
        print(f"❌ 测试联系人信息结构异常: {e}")


def test_element_locators():
    """测试元素定位器格式"""
    print("\n=== 测试元素定位器格式 ===")
    
    try:
        floating_page = EllaFloatingPage()
        
        # 测试主要元素的定位器格式
        test_elements = [
            ('contact_name_input', 'com.transsion.aivoiceassistant:id/et_contact_name'),
            ('phone_number_input', 'com.transsion.aivoiceassistant:id/et_phone_number'),
            ('contact_name_text', 'com.transsion.aivoiceassistant:id/tv_contact_name'),
            ('phone_number_text', 'com.transsion.aivoiceassistant:id/tv_phone_number'),
            ('save_contact_button', 'com.transsion.aivoiceassistant:id/btn_save_contact'),
        ]
        
        for element_name, expected_resource_id in test_elements:
            try:
                element = getattr(floating_page, element_name)
                # 这里我们只能检查元素是否存在，实际的定位器验证需要在真实设备上进行
                print(f"✅ {element_name}: 元素对象创建成功")
            except Exception as e:
                print(f"❌ {element_name}: 元素对象创建失败 - {e}")
        
        print("✅ 元素定位器格式测试完成")
        
    except Exception as e:
        print(f"❌ 测试元素定位器格式异常: {e}")


def main():
    """主测试函数"""
    print("🚀 开始测试 Ask Screen 联系人相关功能")
    
    # 运行所有测试
    test_contact_elements_existence()
    test_contact_methods()
    test_contact_info_structure()
    test_element_locators()
    
    print("\n🎉 所有测试完成!")
    print("\n📝 注意: 这些测试只验证了代码结构的正确性")
    print("   实际的元素定位和交互需要在连接真实设备时进行验证")


if __name__ == "__main__":
    main()
