{"a192659952d6d75342a1c692afadb96d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d3df6c7fbbcf4def", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755503117826, "stop": 1755503137320, "duration": 19494}}]}, "7c50481bc992b9ff109abbeeeece073a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5de1b204ab8292d6", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['check mobile data balance of sim2 SIM 2 not detected', 'Please check and try again']\nassert False", "time": {"start": 1755496304718, "stop": 1755496324381, "duration": 19663}}]}, "8a87a1d0f534afc4770901f3d1dfa316": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "506746f07ec7c902", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Location is turned on now']\nassert False", "time": {"start": 1755494482071, "stop": 1755494501829, "duration": 19758}}]}, "1f9da5f1f2977bbbae31e0d3334eff82": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "43411a23d2e7e0a2", "status": "passed", "time": {"start": 1755494890601, "stop": 1755494910568, "duration": 19967}}]}, "4dcdc98a8f38f748728aec71f673e025": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5b2a3a710c3ce6e8", "status": "passed", "time": {"start": 1755490433645, "stop": 1755490548883, "duration": 115238}}]}, "c50847e2010bac3c5a9bb7ff0b690fb6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "25b19dae7e7898a9", "status": "failed", "statusDetails": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', 'Dialogue Explore Swipe down to view earlier chats How is tent structure designed to resist wind? Can physics & chemistry problems be solved by photo? AI Wallpaper search the address in the image I am sorry, but I am unable to search for addresses at this time. Generated by AI, for reference only Can I save the image address? How to extract address from the image Alternative ways to find the address DeepSeek-R1 Feel free to ask me any questions… 15:12']'\nassert None", "time": {"start": 1755501101833, "stop": 1755501123678, "duration": 21845}}]}, "8e367b8da758818b9a0fe21deca7ec48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "289f152ae316d34e", "status": "passed", "time": {"start": 1755502137097, "stop": 1755502156982, "duration": 19885}}]}, "783144ed3c9603f07a5306d78cb4fde3": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7c68f6256b732fcc", "status": "skipped", "statusDetails": "Skipped: power off 会导致设备断开，先跳过", "time": {"start": 1755492386684, "stop": 1755492386684, "duration": 0}}]}, "70c9bd8c4aab57e96eb06acb93ca2223": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d4b74b5ecef1cd42", "status": "passed", "time": {"start": 1755498641652, "stop": 1755498663735, "duration": 22083}}]}, "57c053de6acd628d4b4cd1230b702a40": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3dc6c9621080753b", "status": "passed", "time": {"start": 1755496576199, "stop": 1755496595659, "duration": 19460}}]}, "1d8131ec3deb65d953f2f16c259f261b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "653a0d3ede0b9bfe", "status": "passed", "time": {"start": 1755488242373, "stop": 1755488263523, "duration": 21150}}]}, "1a5cbbb97cbe59e003ae71750a8d910f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ca1ef5fb16629bf2", "status": "passed", "time": {"start": 1755490030557, "stop": 1755490053158, "duration": 22601}}]}, "fc477656b55eea3a3906a5bdcaa93554": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "872544f9e9fd08ba", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['I need to download WhatsApp to continue']\nassert False", "time": {"start": 1755495492039, "stop": 1755495512964, "duration": 20925}}]}, "36066a5bbb1f962a6ad5842baefe4ff3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b52f32404e514907", "status": "passed", "time": {"start": 1755500390909, "stop": 1755500415348, "duration": 24439}}]}, "de0ed312f350c708e7a00bb74aeaac0f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6f28d5f0698b5193", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['8:00 AM']\nassert False", "time": {"start": 1755486654099, "stop": 1755486684975, "duration": 30876}}]}, "3eec462c8da39eaf95742ed9ab45b7c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a1b74f21920cb042", "status": "passed", "time": {"start": 1755496888877, "stop": 1755496908506, "duration": 19629}}]}, "f27eb1a59c1d5c5c845852e05e6a17d9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "200fe6271a9e4a58", "status": "passed", "time": {"start": 1755492118698, "stop": 1755492140870, "duration": 22172}}]}, "a2c60aff2518d86c9e5686c943130f40": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d7ee269013f09469", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['There are transparent, glowing multicolored soap bubbles around it', '', '', '', \"Dialogue Explore Swipe down to view earlier chats How do termite mounds regulate temperature? There are transparent, glowing multicolored soap bubbles around it Wow, that sounds magical! Are you describing a scene you're seeing right now, or is it something you're imagining? The combination of yellow sunflowers and glowing, multicolored soap bubbles is quite enchanting. Generated by AI, for reference only What are the best soap bubble recipes? Where can I see soap bubble shows? How to create glowing soap bubbles? DeepSeek-R1 Feel free to ask me any questions… 15:46\"]'\nassert False", "time": {"start": 1755503187965, "stop": 1755503209685, "duration": 21720}}]}, "d45827de1782723ad9f8cd9d38f067dc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f7f3ffdd6c15e978", "status": "passed", "time": {"start": 1755501358373, "stop": 1755501383749, "duration": 25376}}]}, "c5debd3414c81b3cc4349236d4020867": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6eaaaf955dba0e22", "status": "passed", "time": {"start": 1755498784866, "stop": 1755498806913, "duration": 22047}}]}, "9b6faa79e3fe09fed639b1092082745b": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e4cfd384aca8dfd1", "status": "skipped", "statusDetails": "Skipped: 语言设置为中文，影响别的Case，先跳过", "time": {"start": 1755491144977, "stop": 1755491144977, "duration": 0}}]}, "19cc9ff22947964a912a8f57a87a3c68": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d42f16b5a33dd6ac", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['I am sorry', 'I can help you with that']，实际响应: '['could you please search an for me', '', '', '', \"Dialogue Explore Swipe down to view earlier chats NBA Rivalry Week Puzzles Fans Send my recent photo to mom on WhatsApp US Military's Drone Quest could you please search an for me I am sorry, I am unable to search for you at this time. Generated by AI, for reference only What can you search for me? What search engines do you use? Can you search for images? DeepSeek-R1 Feel free to ask me any questions… 11:21\"]'\nassert False", "time": {"start": 1755487257584, "stop": 1755487282091, "duration": 24507}}]}, "da0740a44317121ce1879d022e95ae23": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e4aa8ed0c268c28a", "status": "passed", "time": {"start": 1755490146175, "stop": 1755490225279, "duration": 79104}}]}, "1bf9bd9c91ab7da6f818ff587cfff7da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "588791b1af25ce2f", "status": "passed", "time": {"start": 1755488657796, "stop": 1755488681820, "duration": 24024}}]}, "b68db9e7b007fe641926babf537afa6c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "806430326f721111", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Brightness goes down']\nassert False", "time": {"start": 1755491386782, "stop": 1755491408118, "duration": 21336}}]}, "3bd2ea64304a19b2c3694e3a1975c668": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2889d3c9c873348d", "status": "passed", "time": {"start": 1755498335568, "stop": 1755498355453, "duration": 19885}}]}, "e8f9ac327bc5f166a4c4a14509f365bf": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b517460583a5ecfb", "status": "passed", "time": {"start": 1755494169748, "stop": 1755494190392, "duration": 20644}}]}, "f0ba0ad0a7e0160d42d963e5a0186d00": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b5dd074b2c9c41b6", "status": "failed", "statusDetails": "AssertionError: 文件不存在！\nassert False", "time": {"start": 1755492963161, "stop": 1755492989408, "duration": 26247}}]}, "ac6792be4ebb553a5655a2c44aca218c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c770dab2829aa3e5", "status": "passed", "time": {"start": 1755489742870, "stop": 1755489774812, "duration": 31942}}]}, "6d49aaf4a5e11b32b961aa0aa3dbbf6e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "64afb45b4049dff2", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755499765361, "stop": 1755499788231, "duration": 22870}}]}, "6c59bb8b6ba250c58da738ab8237ed3c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9651d4c2f67e969e", "status": "passed", "time": {"start": 1755488488227, "stop": 1755488511266, "duration": 23039}}]}, "13916c423237bf06a74b0b109aad0d66": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6b9d29ad9b68f533", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['set notifications volume to 50', 'Notification volume has been set to 50.', '', '']'\nassert None == 7", "time": {"start": 1755492734656, "stop": 1755492755099, "duration": 20443}}]}, "b236a18e19a6aa2218c85b80afef2746": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ef6e4ba0245ab44e", "status": "passed", "time": {"start": 1755488206196, "stop": 1755488227458, "duration": 21262}}]}, "7ec382c77bede0ad015817770cd9e1eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "56a494d5439cced1", "status": "passed", "time": {"start": 1755488129410, "stop": 1755488152644, "duration": 23234}}]}, "5dff8ffa0041df33df80919398086e48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "209faffdc0cf9c36", "status": "passed", "time": {"start": 1755493575787, "stop": 1755493599245, "duration": 23458}}]}, "f622c7c4831272dc58cb99e6af8d9943": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b865efb985f8433d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755495178062, "stop": 1755495203841, "duration": 25779}}]}, "5fc780d1e7f790011f0e4a521e125a16": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aa3123e8759f39e5", "status": "passed", "time": {"start": 1755502413908, "stop": 1755502434127, "duration": 20219}}]}, "e2beda2a0bda4155b33d47f14bdcb9ed": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f5eb2245322c21f3", "status": "passed", "time": {"start": 1755501398160, "stop": 1755501418314, "duration": 20154}}]}, "468e62202269d66d1ea3c0ae6e2a0e21": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ffd81a84f61ca800", "status": "passed", "time": {"start": 1755495853365, "stop": 1755495876323, "duration": 22958}}]}, "2b244b852ff1236f560ec792596ae556": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6587d831a5fec99", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755500287619, "stop": 1755500307141, "duration": 19522}}]}, "9e84af065ec0018044fd43f37b4c2179": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e548f96f2718102", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['help me generate a picture of a bamboo forest stream', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498369552, "stop": 1755498389207, "duration": 19655}}]}, "148d3ba280bfe2b41b8464beec5f6763": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a2b459cf2f08ab6c", "status": "passed", "time": {"start": 1755486384494, "stop": 1755486422610, "duration": 38116}}]}, "de524bccf252aabc016822f1a65de7f4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6310c03642591f6d", "status": "passed", "time": {"start": 1755488167616, "stop": 1755488191340, "duration": 23724}}]}, "fe7a0f349fbd2027990e72ab5a6650f2": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1b1be6b94c7951b3", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Notification volume has been turned up']\nassert False", "time": {"start": 1755494821239, "stop": 1755494840508, "duration": 19269}}]}, "0fa1017773031b1388876c73f0e0e653": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9471c23f067d080c", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Alarm volume has been turned up.', 'Alarm volume has been set to the maximum.']，实际响应: '['turn up alarm clock volume', 'Please turn off Airplane Mode and try again', 'Airplane Mode', '']'\nassert False", "time": {"start": 1755494778073, "stop": 1755494806657, "duration": 28584}}]}, "b3a1d4f5c4ea6e3e176798cf3deda55b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e1e0e6ef5950a214", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755497162511, "stop": 1755497183673, "duration": 21162}}]}, "04cef4934fe29e90ca0248af7b395794": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1da11297c221aa88", "status": "passed", "time": {"start": 1755493282358, "stop": 1755493304443, "duration": 22085}}]}, "fe3d09fe0bad56e7804ef2f5ea49d283": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "837a7a74c23590a8", "status": "passed", "time": {"start": 1755491539243, "stop": 1755491563996, "duration": 24753}}]}, "acca7d0b06a28ad8e6ccfe3c35828ce1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dffa0f0e3f9337b4", "status": "passed", "time": {"start": 1755489941935, "stop": 1755489973923, "duration": 31988}}]}, "223077c4b7372197db11e72856c8b7e6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3130376940e4019a", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['', '', '', '', \"Dialogue Explore Swipe down to view earlier chats 6 Reference materials Kinkaku-ji, also known as the Golden Pavilion, is a Zen Buddhist temple in Kyoto, Japan 4. Here's what you should know:&#10;&#10;History: The history of Kinkaku-ji dates back to 1397 when it was purchased by shōgun Ashikaga Yoshimitsu 4.&#10;Architecture: It is built in the karayō, or Zen temple, style, inspired by the Sung Chinese style 3.&#10;Visiting Information: It is open daily from 9:00 a.m. to 5:00 p.m. 5. The admission fee is 500 yen for general visitors 2. Generated by AI, for reference only Nearby attractions to Kinkaku-ji temple Kinkaku-ji's role in Japanese Buddhism DeepSeek-R1 Feel free to ask me any questions… 14:45\"]'\nassert False", "time": {"start": 1755499510636, "stop": 1755499532273, "duration": 21637}}]}, "6bdbaaabff6497c5d3be4727f1a7cd8d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "39cda43f623f1b61", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screenshot saved']\nassert False", "time": {"start": 1755486737335, "stop": 1755486761234, "duration": 23899}}]}, "693bce6b8bfdefa98827246122355bf1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "60c5c39af4e431bd", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Generate a cartoon-style puppy image for me with a 4:3 aspect ratio', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755497843381, "stop": 1755497863106, "duration": 19725}}]}, "762b1ab748e39965c1484eb7fe38bfe4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6db26062c8375bcf", "status": "passed", "time": {"start": 1755486201398, "stop": 1755486222360, "duration": 20962}}]}, "d6d97ebce763bf8ead601650bfb2383c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "253bfbce04bb2258", "status": "passed", "time": {"start": 1755487297563, "stop": 1755487318009, "duration": 20446}}]}, "2b4b7555520a6b757239820871731d81": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6805b12606331bab", "status": "failed", "statusDetails": "AssertionError: google_maps: 初始=None, 最终=None, 响应='['open maps', 'Done!', '', '', '[com.google.android.apps.maps页面内容] 此地区的最新动态 | 36° | nguyen thuy quynh | 本地向导 · 53 张照片  •  1 天前 | 此地区的最新动态 | 36° | 在此处搜索 | 探索 | 贡献']'\nassert None", "time": {"start": 1755500083972, "stop": 1755500110387, "duration": 26415}}]}, "edb3a77ed85c79b290dd8cce24f372c0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6560b59b368cba08", "status": "passed", "time": {"start": 1755489292164, "stop": 1755489313431, "duration": 21267}}]}, "e8c3c7bb72cf538a9e89a7b790c5e689": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "be2104ab93275ecd", "status": "passed", "time": {"start": 1755502939827, "stop": 1755502959419, "duration": 19592}}]}, "4aad505422b0d8c87c499477cd89ff5d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "dc13bcd8c8f14a21", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['decrease the volume to the minimun', 'Media volume has been set to the minimum.', '', '']'\nassert None == 0", "time": {"start": 1755491422791, "stop": 1755491444034, "duration": 21243}}]}, "6c46a38570672e3c21f37ef82690d639": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c6578d0be4f581fc", "status": "passed", "time": {"start": 1755493743763, "stop": 1755493783282, "duration": 39519}}]}, "79b68fd9ac84793c5f55250aad03649a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cdbb62d38624064f", "status": "passed", "time": {"start": 1755493354937, "stop": 1755493377857, "duration": 22920}}]}, "ff945a5d436679bddd13261b231955ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f1a2335fc00c0593", "status": "passed", "time": {"start": 1755501674594, "stop": 1755501694505, "duration": 19911}}]}, "05cfb3e6f186373be53bb1a7166ac69f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d665d8af26ee1129", "status": "passed", "time": {"start": 1755500653373, "stop": 1755500679284, "duration": 25911}}]}, "ad18e983dce31052b87b7404f3b347ce": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6589c9a71513424a", "status": "failed", "statusDetails": "AssertionError: 初始=44, 最终=74, 响应='['Adjustment the brightness to 50%', 'Brightness is at 50% now.', '', '']'\nassert 74 == 75", "time": {"start": 1755490976350, "stop": 1755490998528, "duration": 22178}}]}, "83e3a1b41834e87017b680efd7c16b92": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7a3c8a9940a0b583", "status": "passed", "time": {"start": 1755489074855, "stop": 1755489099765, "duration": 24910}}]}, "50e811b93ce50e5ac8364a9fea07e234": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7d585244b5c6ebec", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['help me generate a picture of an elegant girl', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498505560, "stop": 1755498525189, "duration": 19629}}]}, "32ba476d46e86e963aa12ced39981955": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ae5c1a8bfa978084", "status": "passed", "time": {"start": 1755491239106, "stop": 1755491259829, "duration": 20723}}]}, "b07852caec1a6673427b80552f64be85": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "11bc3ac871a42678", "status": "passed", "time": {"start": 1755491498462, "stop": 1755491524849, "duration": 26387}}]}, "a01628ae1e66d140ba93ff6827e01f9c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c58ae92034ab5c53", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['document content']，实际响应: '['document summary', 'Unable to summarize the content on this page. You can try sending links or uploading documents.', '', '']'\nassert False", "time": {"start": 1755497128531, "stop": 1755497148001, "duration": 19470}}]}, "af89bd1d18cd6a175678a8fe1f43ee33": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c920e1d39a5457ed", "status": "passed", "time": {"start": 1755500164467, "stop": 1755500193941, "duration": 29474}}]}, "039e454ca4c329751543f1bfbb5e008e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ddee4e71e5bec785", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755495258463, "stop": 1755495284372, "duration": 25909}}]}, "f2f6762c5ec83e110ace25b47e3112d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "76fa23815119f185", "status": "passed", "time": {"start": 1755487656973, "stop": 1755487687499, "duration": 30526}}]}, "d8a3659601151a79f3b71ba4e47cafee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "adc4d3baca711ab8", "status": "passed", "time": {"start": 1755503364942, "stop": 1755503386815, "duration": 21873}}]}, "f9558282973df5c72bd1c57fb0e19984": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e5b6ab6d55551330", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755495596462, "stop": 1755495615739, "duration": 19277}}]}, "eb142151b4b5ba4125a1a866dc2b58ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "62c530edb229f0e9", "status": "passed", "time": {"start": 1755502033366, "stop": 1755502053561, "duration": 20195}}]}, "3238a485ed8e76dc2866c0b0bcd4930e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ffe7bf6a0dfa964b", "status": "failed", "statusDetails": "AssertionError: alarm_volume: 初始=False, 最终=False, 响应='['set alarm volume 50', 'Alarm volume has been set to 50.', '', '']'\nassert False == 7", "time": {"start": 1755492611073, "stop": 1755492640588, "duration": 29515}}]}, "ff8706df57207971727cf6e1326d4a26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b77ca79b01255105", "status": "passed", "time": {"start": 1755500126629, "stop": 1755500147152, "duration": 20523}}]}, "95e68fb1d20b8d7ff190c67b0bbc2ee8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a7f088139b634a59", "status": "passed", "time": {"start": 1755489218571, "stop": 1755489241732, "duration": 23161}}]}, "a19924fb0a564cf26596907610c0f678": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2f9ea5aad1c33b10", "status": "passed", "time": {"start": 1755487972861, "stop": 1755487998989, "duration": 26128}}]}, "503ff57584874e8387e6b367bfa70c8c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "96dd49778d0c4c7", "status": "passed", "time": {"start": 1755496922866, "stop": 1755496942576, "duration": 19710}}]}, "c190fe929896ea57ed1e33f8bc5bf113": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e67a7663ced8295b", "status": "passed", "time": {"start": 1755494062809, "stop": 1755494085699, "duration": 22890}}]}, "db313838c77140c89e69785e101f25d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "30fd85a9beb451aa", "status": "passed", "time": {"start": 1755499725440, "stop": 1755499746593, "duration": 21153}}]}, "fcfaafeb2b49ed6f468b8db263c64a18": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3ec5cbee09598edc", "status": "passed", "time": {"start": 1755491651675, "stop": 1755491672337, "duration": 20662}}]}, "7af47ccbdaf69e5292e05041f822c0c7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1708b50a022925a5", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Auto-rotation is turned off now']\nassert False", "time": {"start": 1755494027598, "stop": 1755494048166, "duration": 20568}}]}, "70c4e0c16f5cb3629f87876768739a8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cdaf82c1cfc41208", "status": "passed", "time": {"start": 1755496157771, "stop": 1755496177801, "duration": 20030}}]}, "c7b8111fa78410a413dfc969cfe6f0e1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b8ee6665a3282604", "status": "passed", "time": {"start": 1755500207958, "stop": 1755500237529, "duration": 29571}}]}, "9da64d3434f91a12d693ed9c71b62e87": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fa879c77558497aa", "status": "passed", "time": {"start": 1755486010922, "stop": 1755486032810, "duration": 21888}}]}, "8b2d3084bb429ea5def5db416bbf10a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7197877879ecb7dd", "status": "passed", "time": {"start": 1755485412162, "stop": 1755485446416, "duration": 34254}}]}, "f4da532f5d62abff197a05947efc027a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d09f5306f4c399f0", "status": "passed", "time": {"start": 1755487893953, "stop": 1755487919771, "duration": 25818}}]}, "fcda536a017e05b2edb24a2e80ce1ec0": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b443e1f1845998b3", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['What do you want it to say?']\nassert False", "time": {"start": 1755502973579, "stop": 1755502997522, "duration": 23943}}]}, "d29b58e8e7ac85336b7dc255831fd5ba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "27f16104520ecaf3", "status": "passed", "time": {"start": 1755488393202, "stop": 1755488418887, "duration": 25685}}]}, "bd4f9d0c0f70cf6b24bb9923810b25c1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8228e1e49acbaade", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Please state your parking address?']，实际响应: '['remember the parking space', '\"remember the parking space\" recorded', '', '']'\nassert False", "time": {"start": 1755500957873, "stop": 1755500977591, "duration": 19718}}]}, "733cc57b9e666f7c16017a85f41c410d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "992b6c1c0b6fe0af", "status": "passed", "time": {"start": 1755492257734, "stop": 1755492277893, "duration": 20159}}]}, "f1bf796cd6804ca9b19a3e3f949a04ba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9be58cb8aa969561", "status": "passed", "time": {"start": 1755499154075, "stop": 1755499173945, "duration": 19870}}]}, "00495562396e9306113e5f37378ac991": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9d6e041a4e052a6d", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Generate an image of a chubby orange cat chef with a round body and an endearing appearance', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498014336, "stop": 1755498033977, "duration": 19641}}]}, "c359e36718cf3ac8fd335c333a6470cf": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8b6bb38522a867", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755499802486, "stop": 1755499829878, "duration": 27392}}]}, "71c64c122f83e6fd138db517bbda4aef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "333e71c4eb012946", "status": "passed", "time": {"start": 1755502591417, "stop": 1755502611565, "duration": 20148}}]}, "49ae31ee7fe8baa7f1604fd83d56bb68": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f343337a7260b4dc", "status": "passed", "time": {"start": 1755496338662, "stop": 1755496358528, "duration": 19866}}]}, "0e5513d569c7270e4332e484218ae36b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a86f0c6331d66a91", "status": "passed", "time": {"start": 1755497349215, "stop": 1755497369252, "duration": 20037}}]}, "fee3033814a8b17ff8c8abe6bbcdc839": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "60eae10d83a48064", "status": "passed", "time": {"start": 1755488618787, "stop": 1755488643152, "duration": 24365}}]}, "c796c03cca51cea23bdc87f3f9d6fa95": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c639ed2f10e8347", "status": "passed", "time": {"start": 1755499119563, "stop": 1755499139697, "duration": 20134}}]}, "e14cd5a605f26d24de7f5f63d4667c68": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b25b8c84b1510e0d", "status": "passed", "time": {"start": 1755501778076, "stop": 1755501803407, "duration": 25331}}]}, "4b3ad3bdf0873599e48d8f20d70246c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f869bb5568174dcd", "status": "passed", "time": {"start": 1755491869555, "stop": 1755491889866, "duration": 20311}}]}, "cb21afc40e4aec32b847936756c8ba6e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e1fc245a4c62b942", "status": "passed", "time": {"start": 1755486346554, "stop": 1755486369835, "duration": 23281}}]}, "cf3df9e3e259f083301aa2ec640729fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b56253c07e5ce123", "status": "passed", "time": {"start": 1755485975177, "stop": 1755485996442, "duration": 21265}}]}, "dbd7a7f96e1740fa05f50ed6fa7becfb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "580b4adaa8def73c", "status": "passed", "time": {"start": 1755502557069, "stop": 1755502577028, "duration": 19959}}]}, "4bda544c08fa4bc5494c7dda01d4cc77": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1e3aa146f0f14553", "status": "passed", "time": {"start": 1755503331142, "stop": 1755503350931, "duration": 19789}}]}, "eb6abc860fad339739076abacb13ac83": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3c7fe7646472c23b", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755497806064, "stop": 1755497825479, "duration": 19415}}]}, "217ee9f7b3be9f625903076716d45106": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e1fadb569d8be957", "status": "passed", "time": {"start": 1755500356386, "stop": 1755500376522, "duration": 20136}}]}, "0a0a3640b2ba4adce516043bd9362070": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1d40808d468eab6e", "status": "passed", "time": {"start": 1755485679396, "stop": 1755485699552, "duration": 20156}}]}, "540cff5d6d552c22ec37f66efd17315f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a77bf5e5bbe2c7b5", "status": "passed", "time": {"start": 1755499291887, "stop": 1755499316921, "duration": 25034}}]}, "87f3dc53ab72c729262e053c16a3dbcb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1db1a12e38c8a7be", "status": "passed", "time": {"start": 1755485494854, "stop": 1755485514733, "duration": 19879}}]}, "d4ded95517fa8a5af49f09554cc49725": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "21edfa2d93a4ff06", "status": "passed", "time": {"start": 1755497383560, "stop": 1755497403725, "duration": 20165}}]}, "b9fc05e613fdd4d145434e9cf6378c4b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d12df20c75c0fc6e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Here are your alarms. Which one do you want to turn off? ']\nassert False", "time": {"start": 1755489489557, "stop": 1755489523149, "duration": 33592}}]}, "6075008522e5d0ae1667c4ac4be759eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1347de287338b45d", "status": "passed", "time": {"start": 1755486437646, "stop": 1755486475589, "duration": 37943}}]}, "400a9b197316d3b1e59fe33ed78a836a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4a26301ac4389f00", "status": "passed", "time": {"start": 1755496042787, "stop": 1755496075183, "duration": 32396}}]}, "3215de286c6ddd59d6e52a44f2a9967d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ac6c261df16fa973", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755497314879, "stop": 1755497334572, "duration": 19693}}]}, "0e7fd56ff1d5c85e0ce1830d9899313d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d0c3faa56381cbd6", "status": "passed", "time": {"start": 1755488526223, "stop": 1755488549501, "duration": 23278}}]}, "104cf8a7ef102b6850b6d14f4cb14052": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1ff42489c15972ba", "status": "passed", "time": {"start": 1755501501992, "stop": 1755501522119, "duration": 20127}}]}, "657acdf17dda1a11abf6946763f6ed52": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f1f1e91e929dcf70", "status": "passed", "time": {"start": 1755497564872, "stop": 1755497584467, "duration": 19595}}]}, "a8ceb9cec2faa4662cde95140569091b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "dff6c54af645754a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"I'm glad to open Youtube Music for you\", 'You need to select music to play.']\nassert False", "time": {"start": 1755500470020, "stop": 1755500502239, "duration": 32219}}]}, "ebd2c9b6cd7c07b69e348328a207e18b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5a173ed6b092c53", "status": "passed", "time": {"start": 1755493992335, "stop": 1755494013150, "duration": 20815}}]}, "1bc9389e45f0f75c30d3dfb39134948d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6d349ca2a630ab5d", "status": "passed", "time": {"start": 1755502206625, "stop": 1755502226455, "duration": 19830}}]}, "c9c4c38d0ca341040a41178716623909": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ce8763c3cc105dc3", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['make the phone mute', 'Mute is turned on now.', 'Mute', '']'\nassert None", "time": {"start": 1755491792028, "stop": 1755491812856, "duration": 20828}}]}, "92c8c8e017b096314ffde2f610a6791e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b9026a212463de8f", "status": "passed", "time": {"start": 1755492655050, "stop": 1755492676264, "duration": 21214}}]}, "123d65cc114fff79e459e14acfbcd445": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4fdcaea029954f3b", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['turn on adaptive brightness', 'Auto-brightness is turned on now.', 'Auto-brightness', '']'\nassert None", "time": {"start": 1755494204923, "stop": 1755494225635, "duration": 20712}}]}, "c046a1c6e6cc8effa10641e329b1cfad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d4295feb52943498", "status": "passed", "time": {"start": 1755502448608, "stop": 1755502468888, "duration": 20280}}]}, "459c099a876d1129ddcb7cb28663b756": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fb9a425653523825", "status": "passed", "time": {"start": 1755491577983, "stop": 1755491601550, "duration": 23567}}]}, "6f2c4144233271771cdd01a5c48ea3ca": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c81f08c0cb37b1f7", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755495065128, "stop": 1755495087257, "duration": 22129}}]}, "70b49b2a6719030c7c51e642fdaec270": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "791f368cc19ac887", "status": "passed", "time": {"start": 1755489113664, "stop": 1755489134798, "duration": 21134}}]}, "4f84a6588e41dde581e4eef4fccd6344": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7564d1515ce0e54c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755494992988, "stop": 1755495013872, "duration": 20884}}]}, "d60fcab377d9b5093e0f03ecf20f5d10": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7bccc690a6e3cd8f", "status": "passed", "time": {"start": 1755487417680, "stop": 1755487439502, "duration": 21822}}]}, "6980dbcce9a72cd9dea6dee04c6891de": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6f13be11e2084573", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A furry little monkey', '', '', '', \"Dialogue Explore Swipe down to view earlier chats <PERSON><PERSON><PERSON> Addresses Liverpool Transfer Future Send my recent photo to mom on WhatsApp A furry little monkey Okay! I can generate an image of a furry little monkey. Is there anything specific you'd like me to include in the image? For example, a particular pose, background, or style? Generated by AI, for reference only <PERSON> in a cartoon style image Monkey in a jungle environment Monkey with a specific accessory DeepSeek-R1 Feel free to ask me any questions… 14:24\"]'\nassert False", "time": {"start": 1755498264880, "stop": 1755498287132, "duration": 22252}}]}, "7acb737855a3a3110ed556a3e5fe1256": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "91de0985ae36a1fe", "status": "passed", "time": {"start": 1755492919728, "stop": 1755492948544, "duration": 28816}}]}, "99709ca7d9951f6f7049b49ea81d0cd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e490f1dc443a73e2", "status": "passed", "time": {"start": 1755492363961, "stop": 1755492385305, "duration": 21344}}]}, "44720253edec52ccf0868b33f1938265": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3afa524a119e3a10", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Three Little Pigs', '', '', '', 'Dialogue Explore Swipe down to view earlier chats Set an alarm Screenshot Open Facebook Three Little Pigs Ah, the classic tale! Would you like me to tell you the story of the Three Little Pigs? Or perhaps you have something else in mind regarding them? Generated by AI, for reference only The Three Little Pigs characters The Three Little Pigs story details The Three Little Pigs moral of story DeepSeek-R1 Feel free to ask me any questions… 15:48']'\nassert False", "time": {"start": 1755503260669, "stop": 1755503282774, "duration": 22105}}]}, "ecfbca0f5d1122fac0e0543e38291ce2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e3c404ed4bb57196", "status": "passed", "time": {"start": 1755493164991, "stop": 1755493194263, "duration": 29272}}]}, "b89775573784e6ef95769309baebeae4": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fd332225564f8747", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Redirecting to the Play Store to search for QQ']\nassert False", "time": {"start": 1755495102163, "stop": 1755495123208, "duration": 21045}}]}, "876e77318cece5d1079b726f0c97bc45": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "156d2dae48c170a2", "status": "passed", "time": {"start": 1755487182764, "stop": 1755487204092, "duration": 21328}}]}, "3333dd58fd9312a504ae6bc6edf830af": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6276d226895c70b7", "status": "passed", "time": {"start": 1755490110747, "stop": 1755490131503, "duration": 20756}}]}, "b5e1711cce3102fc710ff74e18bf9129": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4204334de6610ed", "status": "passed", "time": {"start": 1755502483424, "stop": 1755502503269, "duration": 19845}}]}, "19df0c79ab8c9ce909771e1a9d21fed3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e7fa0fed0f28cc86", "status": "passed", "time": {"start": 1755503508944, "stop": 1755503542089, "duration": 33145}}]}, "b6eee20d1fad16a048ce8490d7189be4": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "dcbdd9b25bac60dc", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', 'Do you enjoy seeing sports cars?', \"That's a cool observation! Does it look like a car you'd like to own? What kind of sports car is it?\"]，实际响应: '['A sports car is parked on the street side', '', '', '', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 13:45 Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How to use Ask About Screen Can you help with high school function problems? Credit Card Hacks Cut US Trip Costs A sports car is parked on the street side Generating image… 60% Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate 13:45\"]'\nassert False", "time": {"start": 1755495926808, "stop": 1755495949771, "duration": 22963}}]}, "7b90ee3ed7bdd2837a37215aac61cfd9": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d1f779592775f55d", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Ringtone volume has been turned up', 'Ringtone volume has been set to the maximum']，实际响应: '['turn up ring volume', 'Please turn off Airplane Mode and try again', 'Airplane Mode', '']'\nassert False", "time": {"start": 1755494855200, "stop": 1755494876035, "duration": 20835}}]}, "18377608d977aa5cb5e2fc6b03b9ad05": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3a2687a1b3501681", "status": "passed", "time": {"start": 1755499474555, "stop": 1755499496045, "duration": 21490}}]}, "3a27fef360a79f638f96f0461df262da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce32b9d5043b65c7", "status": "passed", "time": {"start": 1755501640065, "stop": 1755501660159, "duration": 20094}}]}, "2428ad915810150c12838b88ee13f49c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e35b7d224c3c195", "status": "passed", "time": {"start": 1755488085038, "stop": 1755488114447, "duration": 29409}}]}, "b8a3cad490db8fccbe1d65628faf6743": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fdefecb0e53024bd", "status": "passed", "time": {"start": 1755499965447, "stop": 1755499987851, "duration": 22404}}]}, "563b9c2c8d2e1d68901eeac733e12913": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "59429721dd0dace4", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['increase the volume to the maximun', 'Media volume has been set to the maximum.', '', '']'\nassert None == 15", "time": {"start": 1755491720776, "stop": 1755491740415, "duration": 19639}}]}, "9511be8e6426d5078713c6e78f3b02e3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d0630ac4c0c281af", "status": "passed", "time": {"start": 1755492292331, "stop": 1755492312786, "duration": 20455}}]}, "fc75b92fb4a100575b2c948dd6c5a008": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4ec6630e05337997", "status": "passed", "time": {"start": 1755497634544, "stop": 1755497654370, "duration": 19826}}]}, "ae86b8d534909e1e7c8c7adb4ee39e5c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ccbcb71e423ab04e", "status": "passed", "time": {"start": 1755494448052, "stop": 1755494467621, "duration": 19569}}]}, "fa47cb0b4427dd62fb8f91c9e5e15ace": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7488546749f49d74", "status": "passed", "time": {"start": 1755503772918, "stop": 1755503792961, "duration": 20043}}]}, "57acf2797af332487c1fdb9a53a30e4f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f9a03571405bfb36", "status": "passed", "time": {"start": 1755497418549, "stop": 1755497438074, "duration": 19525}}]}, "a416a85ec867e3b2cfd6e23150d72859": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "89f7719a53a2bc63", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations', '', '', '', \"Dialogue Explore Swipe down to view earlier chats Math problem photo solving <PERSON>'s <PERSON> Debut Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations Sorry, that's a bit difficult. Let me think it over. Generated by AI, for reference only Ring design for online jewelry sales Jewelry ring's e-commerce presentation Flamingo ring materials and crafting DeepSeek-R1 Feel free to ask me any questions… 13:57\"]'\nassert False", "time": {"start": 1755496643683, "stop": 1755496665302, "duration": 21619}}]}, "b7bfa1ba094155307273abc83c43a0d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f1e757e84c48e869", "status": "passed", "time": {"start": 1755488313386, "stop": 1755488336741, "duration": 23355}}]}, "c0d6ce0b7c5e41242c01a6e0c0186608": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9ce970b1f233022b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"I'm <PERSON>\", 'assistant']\nassert False", "time": {"start": 1755489871371, "stop": 1755489892166, "duration": 20795}}]}, "643a7bbfbf5c5eedbae7ae814fbc8b52": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a3149f6a5e3b5e87", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1755493613879, "stop": 1755493634903, "duration": 21024}}]}, "3dae350db69abded432b3e7f5f8463c8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b8228732f7da085b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Here's a joke for you\"]\nassert False", "time": {"start": 1755489448500, "stop": 1755489474709, "duration": 26209}}]}, "44c9275711c93730c8d2cb2a7374b3cd": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8be90e8c859e6876", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following number is recognized. Please confirm before calling.']，实际响应: '['Dial the number on the screen', 'Who would you like to call?', '', '']'\nassert False", "time": {"start": 1755496679686, "stop": 1755496699340, "duration": 19654}}]}, "772728b3468560788490a3673352724d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3b3589ad2946f7f9", "status": "passed", "time": {"start": 1755489405539, "stop": 1755489433993, "duration": 28454}}]}, "92e2909ea81e82011e43342b4fc06c3b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fad168214781956", "status": "passed", "time": {"start": 1755501536289, "stop": 1755501556322, "duration": 20033}}]}, "4933b925ec694ecfcd17b3423ac28184": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a0b415653780924f", "status": "passed", "time": {"start": 1755495338578, "stop": 1755495365164, "duration": 26586}}]}, "3d685d9ca6a0d7795be3c96921595318": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "42454a177c3a8e30", "status": "passed", "time": {"start": 1755496956788, "stop": 1755496976420, "duration": 19632}}]}, "28f9087c186df37701fba71f366c084e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7069d39903a118ae", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The alarm']\nassert False", "time": {"start": 1755494959142, "stop": 1755494978335, "duration": 19193}}]}, "bb51fd67dac102de95e755be72996bd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b6a9aaccc0115e5b", "status": "passed", "time": {"start": 1755496990678, "stop": 1755497011783, "duration": 21105}}]}, "7d8296483e3dfc0902b42ccfe6759e59": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a3d165df3ebffcd2", "status": "passed", "time": {"start": 1755501955063, "stop": 1755501981777, "duration": 26714}}]}, "9458f45d3c37d9141658da9964a470f5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "510077bcecd96a52", "status": "passed", "time": {"start": 1755501288430, "stop": 1755501308954, "duration": 20524}}]}, "51b4b46de04a8c1e37077a9f688cb490": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e8aa8be0b5219380", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755500251685, "stop": 1755500273123, "duration": 21438}}]}, "286e9cba8578d73b1f445f9d6d3a7d2e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4da3b33db15cae6e", "status": "passed", "time": {"start": 1755495777580, "stop": 1755495800781, "duration": 23201}}]}, "2ead070abca949d328b727d85148a577": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "83097c2bd2ac1ce0", "status": "passed", "time": {"start": 1755491458817, "stop": 1755491484210, "duration": 25393}}]}, "4fd50eb7a7e49fc09f612442a33e3010": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b5d45a222a1bf25a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Please tell me the name or number to call']\nassert False", "time": {"start": 1755496003786, "stop": 1755496028196, "duration": 24410}}]}, "3e1e4da6344de7cdf40fa1d59c43dcc3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4ed0911ea04026c5", "status": "passed", "time": {"start": 1755489256403, "stop": 1755489277589, "duration": 21186}}]}, "59f89eb284ef9300c926c5e2f1d3fd26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a7aa9e18a7a454b7", "status": "passed", "time": {"start": 1755492805464, "stop": 1755492826920, "duration": 21456}}]}, "02dda06829926b51f170419357629e86": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4a5ec0a66b60f2b9", "status": "passed", "time": {"start": 1755498084214, "stop": 1755498104387, "duration": 20173}}]}, "4072ba85d37e03a8ef0a5dd9d0741631": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c610d0415d4aadd9", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['min ring volume', 'Ringtone volume has been set to the minimum.', '', '']'\nassert None == 0", "time": {"start": 1755492189155, "stop": 1755492208910, "duration": 19755}}]}, "18415b75388fbfdac9a7e4232373c000": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ee36fc1f2dcef2d9", "status": "passed", "time": {"start": 1755487534969, "stop": 1755487558541, "duration": 23572}}]}, "3bafa6d8eb5b49bc5b77f1784275285e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ea730b2eeebc920e", "status": "passed", "time": {"start": 1755493208731, "stop": 1755493231909, "duration": 23178}}]}, "51c103053dd0c5596d9f4d9f178c3d8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e66bce615188d1d4", "status": "passed", "time": {"start": 1755495815218, "stop": 1755495839049, "duration": 23831}}]}, "178c119ddfd51f19a22377df428e3fc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6a23b0c00399f863", "status": "passed", "time": {"start": 1755496123463, "stop": 1755496143376, "duration": 19913}}]}, "a0efcebc4cee6024e690bd290b4f3fbb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "84a3333e716ccffd", "status": "passed", "time": {"start": 1755502905384, "stop": 1755502925587, "duration": 20203}}]}, "12eb3852c333145c5906579f2346c37a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9f59d47c1b9d0499", "status": "passed", "time": {"start": 1755487497054, "stop": 1755487520197, "duration": 23143}}]}, "3d6cfc87445d1bd76dceee439d00b3d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "da6c5c69e299f37a", "status": "passed", "time": {"start": 1755487857560, "stop": 1755487879012, "duration": 21452}}]}, "75c9edd252211f9e74fd8c1a2faeefd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8819ae8dbd92792c", "status": "passed", "time": {"start": 1755492480225, "stop": 1755492505070, "duration": 24845}}]}, "1615e8617cafbed9e30baf38018d96b0": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6e7affd9337554b1", "status": "skipped", "statusDetails": "Skipped: reset phone 会导致设备断开，先跳过", "time": {"start": 1755501014105, "stop": 1755501014105, "duration": 0}}]}, "44e27936b56f219f63af671a8fd7f5fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7edfe091bfb65cfc", "status": "passed", "time": {"start": 1755503594479, "stop": 1755503614526, "duration": 20047}}]}, "4ae696581fe41611547bc10ddba4f526": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c4e2274bc0bb8ee", "status": "passed", "time": {"start": 1755487934474, "stop": 1755487957803, "duration": 23329}}]}, "20bd2e7c8179ca1901bc63c9a02b9ee1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "88a4b61efd7b7383", "status": "passed", "time": {"start": 1755498191416, "stop": 1755498214653, "duration": 23237}}]}, "8bcc4c0c2b314e79a7177168f7d787b8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "40e869175e17f9c", "status": "passed", "time": {"start": 1755496372821, "stop": 1755496392201, "duration": 19380}}]}, "5c75e7ecaa2fe55a9b9666aae0ca1b5a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1e52d2148711dd51", "status": "passed", "time": {"start": 1755502659288, "stop": 1755502686615, "duration": 27327}}]}, "5697ea2b6f1cefef94d5b32e213e05a7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9d90e5d81b7acbb", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The QR code', 'Generated by AI']\nassert False", "time": {"start": 1755490782714, "stop": 1755490896767, "duration": 114053}}]}, "c121335a9bce64dd570aaf5b221b21df": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "17e3bc1a6301489b", "status": "passed", "time": {"start": 1755500992344, "stop": 1755501012650, "duration": 20306}}]}, "9445ebf64ec65c769332fec8dd505332": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "905b452af4c83531", "status": "passed", "time": {"start": 1755502517955, "stop": 1755502542549, "duration": 24594}}]}, "56a09613cdb882018377e1c2c4e78472": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5cb6e78030f32b51", "status": "passed", "time": {"start": 1755489988144, "stop": 1755490016220, "duration": 28076}}]}, "bd4d204a449f3a4013b03af9a9101446": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2394ed6d3cea5a91", "status": "passed", "time": {"start": 1755487702026, "stop": 1755487731533, "duration": 29507}}]}, "a306f4c2244f596f1ab838aa7a80dd45": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ff9cf571c0e69633", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['', '', '', '', \"Dialogue Explore Swipe down to view earlier chats That sounds like a lovely image! A cute little boy skiing conjures up scenes of winter fun and adventure. Here are a few things that come to mind:&#10;&#10;The Scene : Imagine a snowy mountain slope, crisp air, and sunshine. The little boy might be bundled up in a colorful ski suit, a warm hat, and gloves.&#10;The Action : He could be a beginner, carefully making his way down the slope, or perhaps a more experienced skier, confidently carving turns in the snow.&#10;The Emotion : It's likely a scene filled with joy, excitement, and maybe a little bit of determination.&#10;&#10;Do you have any specific details you'd like to add? For example, is he wearing a particular color, or is there a specific mountain or setting you're imagining? Generated by AI, for reference only Skiing safety tips for young children DeepSeek-R1 Feel free to ask me any questions… 13:42\"]'\nassert False", "time": {"start": 1755495703796, "stop": 1755495725963, "duration": 22167}}]}, "e8f03971277a71512b5ebaad612bc964": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8c4c3d95b1e7cab9", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755495218368, "stop": 1755495243930, "duration": 25562}}]}, "75c56947a7b1061f7ec858fb20919b50": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b664f8d7d1f71f7e", "status": "failed", "statusDetails": "AssertionError: 应用未打开: 初始=False, 最终=False, 响应='['navigation to the lucky', 'Please turn off Airplane Mode and try again', 'Airplane Mode', '']'\nassert False", "time": {"start": 1755495298831, "stop": 1755495324177, "duration": 25346}}]}, "c45251d74b80bdc9105dde9b444ef1c7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b6bdba3a7fac2838", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755496508237, "stop": 1755496527656, "duration": 19419}}]}, "88a6294df5f2ab484e181b1f196ff253": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fe79bc89c4872fda", "status": "passed", "time": {"start": 1755499400306, "stop": 1755499425614, "duration": 25308}}]}, "0eb27e9cfa9ed24b7ee5e6be6e495cb7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8b09801babb8c73c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['OK']\nassert False", "time": {"start": 1755486544815, "stop": 1755486565826, "duration": 21011}}]}, "5f09c86ae320138eb73de73a25f73607": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7d9eec4fb5525906", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['help me generate a picture of green trees in shade and distant mountains in a hazy state', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498573575, "stop": 1755498593110, "duration": 19535}}]}, "a4af452e0448ec3c1ecc9afcc30459be": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "391ec1d93138b383", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1755493246299, "stop": 1755493267414, "duration": 21115}}]}, "61e923bb9b35a687b231b0c27b5ec620": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "36628fbfae9837fe", "status": "passed", "time": {"start": 1755502379321, "stop": 1755502399431, "duration": 20110}}]}, "9c301cfc137fb94f119957b5f74291ec": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8779cfa151cd8b27", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Sorry, I couldn't locate the setting\"]\nassert False", "time": {"start": 1755503556431, "stop": 1755503580071, "duration": 23640}}]}, "caccad19499f4bc8494953ac84d8d23c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6b805288adc04f20", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry', 'a bit difficult']\nassert False", "time": {"start": 1755490239551, "stop": 1755490353944, "duration": 114393}}]}, "929b39cceeb7a01f602573951f5fef39": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1b84d57f52a023ae", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含[\"I'm your friendly digital assistant, <PERSON>!\", \"I'm <PERSON>, your personal assistant.\"]，实际响应: '[\"what's your name\", \"I'm <PERSON>—your AI assistant here to help with writing, images, questions, and more. What would you like to do?\", '', '']'\nassert False", "time": {"start": 1755503738391, "stop": 1755503758272, "duration": 19881}}]}, "e865942f74e70950eccebd8243dd6035": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f0fd43da9bcfbf0f", "status": "passed", "time": {"start": 1755489537426, "stop": 1755489562571, "duration": 25145}}]}, "776dca6a65836abb5a943543ee2b9f12": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a2f54ffc67eb0791", "status": "passed", "time": {"start": 1755503910166, "stop": 1755503930283, "duration": 20117}}]}, "e78aa9affdc78502b8ad3b712ecf28b9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "37117e96ddab6ea7", "status": "passed", "time": {"start": 1755491686786, "stop": 1755491706411, "duration": 19625}}]}, "ffb0a39af30beaa699329479ec564117": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9efd7f5987a69bbb", "status": "passed", "time": {"start": 1755486236762, "stop": 1755486277722, "duration": 40960}}]}, "356db57bafcf61266d2f62fd1b8ab4e2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2becd5b57d7a4c07", "status": "passed", "time": {"start": 1755495740661, "stop": 1755495763344, "duration": 22683}}]}, "9c84b087eb7d9fde94ed5bb5370b275b": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c382d9a7c98e9700", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1755499886498, "stop": 1755499913812, "duration": 27314}}]}, "0c44c94f08feed70addcec44e96bda5a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8e605cf9348c1ee4", "status": "passed", "time": {"start": 1755489665145, "stop": 1755489685068, "duration": 19923}}]}, "ffd7dc86cbeda13ca78bbca09f06422a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ac48c98400f9cccd", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['countdown 5 min', 'Done!', '', '']'\nassert False", "time": {"start": 1755491345083, "stop": 1755491372424, "duration": 27341}}]}, "613ef0933e4be87696bbedd56b4f0052": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e1091f928abe2f4e", "status": "passed", "time": {"start": 1755496192237, "stop": 1755496212179, "duration": 19942}}]}, "17788b061637289a04732fe5840218ee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8c4d1ad56423ecf1", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Recording completed']\nassert False", "time": {"start": 1755486580698, "stop": 1755486602875, "duration": 22177}}]}, "1ca8d9c300d55584cdcf637ede08bdba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "618f97bb7f63f3e2", "status": "passed", "time": {"start": 1755496849485, "stop": 1755496874693, "duration": 25208}}]}, "1d15cba90ae0426fa12e3218f1c542a6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e0eaa952e2440198", "status": "passed", "time": {"start": 1755485713876, "stop": 1755485733399, "duration": 19523}}]}, "0b659537bc9c9b47c2c23f702fadd56b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "938a4df873a1b318", "status": "passed", "time": {"start": 1755497025963, "stop": 1755497045637, "duration": 19674}}]}, "eadc304b3069d4918c06805d847a62d7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2065d4815c11e1a2", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['help me generate a picture of blue and gold landscape', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498539605, "stop": 1755498559261, "duration": 19656}}]}, "1d703b69183fa98eda60c159840c4ffd": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6757ac4ac3bd0d5", "status": "failed", "statusDetails": "AssertionError: contacts: 初始=False, 最终=None, 响应='['call mom', \"You've reached the image generation limit for today.\", '', '']'\nassert None", "time": {"start": 1755495964254, "stop": 1755495989024, "duration": 24770}}]}, "b7e448432379b6f8a430f1cbdb3ee3fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aa90c0c998f79e87", "status": "passed", "time": {"start": 1755488948288, "stop": 1755488971810, "duration": 23522}}]}, "0c4bd81bf0dbac094265e3ac47550bbd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "361a771981e36dd1", "status": "passed", "time": {"start": 1755489624877, "stop": 1755489650766, "duration": 25889}}]}, "e8d2430f1712f99f0a178507bb398709": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e845d7ff9eca6135", "status": "skipped", "statusDetails": "Skipped: 重启会导致设备断开，先跳过", "time": {"start": 1755501014112, "stop": 1755501014112, "duration": 0}}]}, "b4e75f584d82368436f820de28f92cfd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "acb7103c5318ed9", "status": "passed", "time": {"start": 1755486292615, "stop": 1755486331803, "duration": 39188}}]}, "753ba105235625e906d023bd3aaa0821": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c61ed480a6d5fb82", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1755493539874, "stop": 1755493560957, "duration": 21083}}]}, "b846e0492742bba04cf4a26ee4530889": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b9ec27d846cd74ff", "status": "failed", "statusDetails": "AssertionError: contacts: 初始=False, 最终=None, 响应='['redial', '', '', '', \"15:08 Dialogue Explore Swipe down to view earlier chats 15:07 Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh How to solve primary & junior high questions? Send my recent photo to mom on WhatsApp Extract key points from document redial DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert None", "time": {"start": 1755500878434, "stop": 1755500908190, "duration": 29756}}]}, "794f685415bbcd702feae0b55a4dd537": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e002d2408e9ba989", "status": "passed", "time": {"start": 1755489148984, "stop": 1755489170291, "duration": 21307}}]}, "ab2195315637668cad08b0606ef7ff17": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d4bb3fe8535c92ab", "status": "passed", "time": {"start": 1755499188546, "stop": 1755499208696, "duration": 20150}}]}, "7b1fce8b7d3ff59dca969b439bb82f75": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fe831e92f9edd09d", "status": "broken", "statusDetails": "TypeError: a bytes-like object is required, not 'dict'", "time": {"start": 1755486990352, "stop": 1755487013471, "duration": 23119}}]}, "48a2a80bfed06f0c82b99a0aaa26e252": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e04729e8f6d4112", "status": "passed", "time": {"start": 1755499331264, "stop": 1755499351357, "duration": 20093}}]}, "f09a8375806e200073a99f1cbabdc35c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "17d3915d67aefacf", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Ok']\nassert False", "time": {"start": 1755486165222, "stop": 1755486186256, "duration": 21034}}]}, "1ab60ce22774c460e557aaa3b3f9120a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "729eed61791867c6", "status": "passed", "time": {"start": 1755493045101, "stop": 1755493068990, "duration": 23889}}]}, "ffd9b4e8a27f1469e05050bd5989e500": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b8607aa0c2a301f6", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1755493502668, "stop": 1755493524813, "duration": 22145}}]}, "aecf9a6f67cd29766190cbcc133448d2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "92cef72143d43475", "status": "passed", "time": {"start": 1755501432865, "stop": 1755501452916, "duration": 20051}}]}, "9420fd606a04614c6f09bf36f2873f93": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "79e6f5dd0a5829a6", "status": "passed", "time": {"start": 1755493318975, "stop": 1755493340374, "duration": 21399}}]}, "1147a84f37b71eb5b15008169cadcc53": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c00d5166e751ea4a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Here are your alarms']\nassert False", "time": {"start": 1755489029671, "stop": 1755489060129, "duration": 30458}}]}, "e9039096aff36ba6e4fae58e40eb8539": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6b97305455e1990b", "status": "passed", "time": {"start": 1755500516536, "stop": 1755500539093, "duration": 22557}}]}, "57c5370b1a13de534ed16f0ce2ee85b9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cba7776fbf23f8ef", "status": "passed", "time": {"start": 1755493838419, "stop": 1755493870842, "duration": 32423}}]}, "c052c8813edd9c2261dc1bcc29786fe9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f764933e115da665", "status": "passed", "time": {"start": 1755502735267, "stop": 1755502754931, "duration": 19664}}]}, "bfddb3863bb9971cace5dae92df6977d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bc302b2dd49b99c1", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Thank you']\nassert False", "time": {"start": 1755498714361, "stop": 1755498736158, "duration": 21797}}]}, "e56b7788214bc4e18231f16dfd713954": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "45cf25b25529ab14", "status": "passed", "time": {"start": 1755486776260, "stop": 1755486804710, "duration": 28450}}]}, "154a720f41d8f5a908552e8c7cf8e781": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3d6a8621cf946c5b", "status": "passed", "time": {"start": 1755501467355, "stop": 1755501487645, "duration": 20290}}]}, "afa6af304cfb25a990764680de5fa777": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "51fc2408e1b48e72", "status": "passed", "time": {"start": 1755497668850, "stop": 1755497688364, "duration": 19514}}]}, "fcabf101e08450542157f8740eeec9a7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ac6e3c740d8f0174", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755495379035, "stop": 1755495399598, "duration": 20563}}]}, "d094a0b21c0bd532e6db707dcbab5564": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f4641168eac1c411", "status": "passed", "time": {"start": 1755497094112, "stop": 1755497114205, "duration": 20093}}]}, "f416bca94fc67372d77ac2dd1f3e4517": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "67dbb54ba8b43268", "status": "passed", "time": {"start": 1755488789643, "stop": 1755488813409, "duration": 23766}}]}, "5050d8dc816d181ca0c76dc56c8cb5f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "72d70c2c9fdf6684", "status": "passed", "time": {"start": 1755498971872, "stop": 1755498991682, "duration": 19810}}]}, "6ecc7e0fc961d0d4e7e46672c033625a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6e18d4a6a8575a63", "status": "passed", "time": {"start": 1755487819150, "stop": 1755487842355, "duration": 23205}}]}, "4276e587385154206726240ad06acd24": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e98db407de5b1b14", "status": "passed", "time": {"start": 1755499257348, "stop": 1755499277636, "duration": 20288}}]}, "8acd3c85f9c9d0b7f252da4466c049e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8a35e737a53bfab0", "status": "passed", "time": {"start": 1755492399786, "stop": 1755492427291, "duration": 27505}}]}, "00ae864f57f2146374ff8bf301b9b8af": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4d7c1112f9245cc0", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.', '', '', '', \"Dialogue Explore Swipe down to view earlier chats hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively. Sorry, that's a bit difficult. Let me think it over. Generated by AI, for reference only <PERSON><PERSON> mascot for brand promotion Hamster mascot in different poses Ham<PERSON> mascot design variations DeepSeek-R1 Feel free to ask me any questions… 14:22\"]'\nassert False", "time": {"start": 1755498155240, "stop": 1755498176860, "duration": 21620}}]}, "a297156fb1e7af53c032ac3c6277feee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3502fc0fb5c7596d", "status": "passed", "time": {"start": 1755503628737, "stop": 1755503655895, "duration": 27158}}]}, "b59687eed5ccb758650c7c0d96ed6bc1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "422d5172f04f4d88", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done', 'duration']\nassert False", "time": {"start": 1755502700860, "stop": 1755502720800, "duration": 19940}}]}, "7a670647c2336e6a5a5d07824fe89da6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4d5db14a3cdbfbb1", "status": "passed", "time": {"start": 1755497702872, "stop": 1755497722716, "duration": 19844}}]}, "ce2018f4cca8041a465d2a753ade920b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d9c4e23c7973b52e", "status": "passed", "time": {"start": 1755491013583, "stop": 1755491033995, "duration": 20412}}]}, "098126ed77f375b3e0f5370b3ec7d0b7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6fdc15bc54a2fd9a", "status": "passed", "time": {"start": 1755486819466, "stop": 1755486851150, "duration": 31684}}]}, "76a63bd8a63882a3a1ada32e63f1c971": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "182235ca56db8a65", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['it wears a yellow leather collar', '', '', '', \"14:38 Dialogue Explore Swipe down to view earlier chats Do traditional dance steps contain numerical sequences? Can you help with high school function problems? it wears a yellow leather collar Okay! I can generate an image of a furry little monkey wearing a yellow leather collar. Is there anything else you'd like to add to the image? Perhaps a specific background or style? Generated by AI, for reference only Monkey in a specific environment Monkey with a different collar color Monkey with other accessories DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1755499082851, "stop": 1755499104910, "duration": 22059}}]}, "a0ea006ce61aacded2720f8d2a03ba5b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "383bffd77d6bb1f1", "status": "passed", "time": {"start": 1755488013650, "stop": 1755488035054, "duration": 21404}}]}, "6748f677dff755a4da95c520c3f05506": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9a9a4f0cdad8c1f8", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['These suggestions']\nassert False", "time": {"start": 1755487110032, "stop": 1755487131021, "duration": 20989}}]}, "8971377f4371d1ea3384cde4ed276db1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2faa1eee8a3aa9d0", "status": "passed", "time": {"start": 1755494344905, "stop": 1755494364672, "duration": 19767}}]}, "0ce2a3efa79db58c34a5590015948f51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "281d46b2ea7e677a", "status": "passed", "time": {"start": 1755498228770, "stop": 1755498250782, "duration": 22012}}]}, "e76af38ac3a594aa2b7d7173d57e98ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d64080247fe0d3ec", "status": "passed", "time": {"start": 1755502275675, "stop": 1755502295573, "duration": 19898}}]}, "7cd08c87d5de8ec73ac863e8a636c8aa": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "68439b448a6afa57", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['minimum volume', 'Media volume has been set to the minimum.', '', '']'\nassert None == 0", "time": {"start": 1755492223504, "stop": 1755492242921, "duration": 19417}}]}, "11875095b9997cfc7edbe407c3074b7e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f291c191cd068630", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755496439848, "stop": 1755496459351, "duration": 19503}}]}, "d6ad2be1232377f5942b2d4f816b2e71": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2a20d290ca467f0d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['NFC is turned on now.']\nassert False", "time": {"start": 1755494516417, "stop": 1755494535770, "duration": 19353}}]}, "063471c2e7f2d00ecd08e780860e0cf2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9563fc1b2d7ac310", "status": "passed", "time": {"start": 1755493649715, "stop": 1755493673397, "duration": 23682}}]}, "464c8ea2a15f7ff86b8e0a347a821945": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cb998d564c571bbb", "status": "passed", "time": {"start": 1755486129429, "stop": 1755486150805, "duration": 21376}}]}, "94acf463e3e6d87a1e9cf7ff754044a2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "668552dfe8255d0", "status": "passed", "time": {"start": 1755499928693, "stop": 1755499951104, "duration": 22411}}]}, "d304f4e805a15a0351109cc931c26ffc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bd6b00d48fc75e9c", "status": "passed", "time": {"start": 1755488696656, "stop": 1755488719424, "duration": 22768}}]}, "f4d12b1367b35df96178a58e48fe8f5e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ad34dd261ee40182", "status": "failed", "statusDetails": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False", "time": {"start": 1755485643465, "stop": 1755485664990, "duration": 21525}}]}, "dee08db8cb0f1293bf864f56326992d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e7b08554d99612b", "status": "passed", "time": {"start": 1755487374544, "stop": 1755487402701, "duration": 28157}}]}, "578e52c6d5e868d5464682b454971c51": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ed36ea751cd0e957", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Which app']\nassert False", "time": {"start": 1755495028615, "stop": 1755495050864, "duration": 22249}}]}, "34f3c9cc9098f792051e7099b7a9fdc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7659a0165dec7300", "status": "passed", "time": {"start": 1755502871234, "stop": 1755502891230, "duration": 19996}}]}, "306cbf11cdbcb045eb3c3c716515b1d6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b8a845bc7ae17991", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['thank you']\nassert False", "time": {"start": 1755498678124, "stop": 1755498700032, "duration": 21908}}]}, "ed94d8d97b63a623a1f6438b57774ff1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2be3c27007c5ea", "status": "passed", "time": {"start": 1755500429868, "stop": 1755500455655, "duration": 25787}}]}, "c5050ea089fe0f7a5b962119cd32b32e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "baae95d2ae9dceda", "status": "passed", "time": {"start": 1755486953193, "stop": 1755486975296, "duration": 22103}}]}, "f5346ff0fa4cb76e4b6ceea6116693ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "508a8d82009e707f", "status": "passed", "time": {"start": 1755488278178, "stop": 1755488298157, "duration": 19979}}]}, "8f69a86b2d665eb6925fa007d973040e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b53378e78d39c315", "status": "failed", "statusDetails": "AssertionError: clock: 初始=False, 最终=False, 响应='['max alarm clock volume', 'Alarm volume has been set to the maximum.', '', '']'\nassert False == 15", "time": {"start": 1755491827304, "stop": 1755491855258, "duration": 27954}}]}, "5fe7611f5b7d3ef438ce938b66e0b99f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "911ef56e93154a6e", "status": "passed", "time": {"start": 1755493004364, "stop": 1755493030148, "duration": 25784}}]}, "5ad5ef8bf0f7913e709dbc1e706db1e5": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6a2210c6e321bc0e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['joke for you']\nassert False", "time": {"start": 1755503046199, "stop": 1755503068153, "duration": 21954}}]}, "79ed3b173757e627534e24ad9289f338": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "80b713fe8f81f97a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['No parking space information is found, do you need to add new information']\nassert False", "time": {"start": 1755503875692, "stop": 1755503895478, "duration": 19786}}]}, "8d12bedb52d3f000f4269afc25f3fe30": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "527bdb5696f0b1d7", "status": "passed", "time": {"start": 1755489789590, "stop": 1755489820435, "duration": 30845}}]}, "2bf170e8c0013ab361afb23f8f059db8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "47a97ffc4113f69d", "status": "passed", "time": {"start": 1755497486619, "stop": 1755497511525, "duration": 24906}}]}, "b911308f3c1fe764715d778a884946c2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a1b32f0518e05e8", "status": "passed", "time": {"start": 1755503944641, "stop": 1755503964691, "duration": 20050}}]}, "2ecac23eb3f511651fafc6ba6a3725f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4488dc5233d49315", "status": "passed", "time": {"start": 1755493797832, "stop": 1755493823777, "duration": 25945}}]}, "29293108cad153db021139a26e3455ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce671eab76db995d", "status": "passed", "time": {"start": 1755503012049, "stop": 1755503032018, "duration": 19969}}]}, "5cc849d46714fff99c626e94dc28932d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8f972b9e1b77bee1", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755495562726, "stop": 1755495581886, "duration": 19160}}]}, "d9e62135fb3d98a8cadd206c651db3d7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6a3c95959cdeda8f", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['set ringtone volume to 50', 'Ringtone volume has been set to 50.', '', '']'\nassert None == 7", "time": {"start": 1755492769756, "stop": 1755492790590, "duration": 20834}}]}, "a1088ee9683cc60d86b0994865138921": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "37dfbcf9e6e18077", "status": "passed", "time": {"start": 1755488866540, "stop": 1755488895299, "duration": 28759}}]}, "a54454fcb441a45b0e29dcbbf21679aa": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b65d69907df5275e", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['max ring volume', 'Ringtone volume has been set to the maximum.', '', '']'\nassert None == 15", "time": {"start": 1755491941481, "stop": 1755491962474, "duration": 20993}}]}, "9ba28642fd60826e21a949609570a951": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cb3ff0a46733676a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Oops, out of my reach, ask me again after I've learned it\"]\nassert False", "time": {"start": 1755501179706, "stop": 1755501202710, "duration": 23004}}]}, "5dbd6c476e40c9de0215f0509dd43986": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2ed067e7efa850d8", "status": "passed", "time": {"start": 1755498935861, "stop": 1755498957822, "duration": 21961}}]}, "aff947fee562ec2636c3ce68a270b88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7972db2b2bb137e3", "status": "passed", "time": {"start": 1755494310879, "stop": 1755494330672, "duration": 19793}}]}, "411d5cfcc1960041b8df4decf67232f6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "335c6b5fe0366e9f", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['help me generate a picture of an airplane', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498471118, "stop": 1755498491007, "duration": 19889}}]}, "fef04fbdd26caf7d3f0f60df2c3ed14d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9bed2611f2f7371b", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Help me generate a 3D rendered picture of a Song-style palace surrounded by auspicious clouds and with delicate colors', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498301618, "stop": 1755498321272, "duration": 19654}}]}, "7ebb09688c661659cc2b4a26d54a347f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ced91b5cbc3f13c7", "status": "passed", "time": {"start": 1755503807286, "stop": 1755503827297, "duration": 20011}}]}, "969451307307a13b4d89a24bb46ad0bb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "10bd30f7006334a1", "status": "passed", "time": {"start": 1755501604999, "stop": 1755501625585, "duration": 20586}}]}, "b60d4c80cd1df80873da3fea78736e6a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3fc16b4876c03a0a", "status": "passed", "time": {"start": 1755489699531, "stop": 1755489728689, "duration": 29158}}]}, "db1dca9aabe4b3e03e7003d17cf3fc9c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fea69f0980519c29", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['turn on airplane mode', 'Airplane Mode is turned on now.', 'Airplane Mode', '']'\nassert None", "time": {"start": 1755494240437, "stop": 1755494261161, "duration": 20724}}]}, "74da34fd3c558df1234d7c0e937641b3": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4e2648abb16ff5b8", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['there are many yellow sunflowers on the ground', '', '', '', 'Dialogue Explore Swipe down to view earlier chats <PERSON><PERSON> Joins <PERSON> After G League Triumph Why does copper conduct heat efficiently? Air Force Targets Cybertrucks in Drills there are many yellow sunflowers on the ground That sounds like a beautiful sight! Do you happen to be near a sunflower field? Generated by AI, for reference only What are sunflower growing tips? Sunflower field photography tips? How to care for sunflowers? DeepSeek-R1 Feel free to ask me any questions… 15:46']'\nassert False", "time": {"start": 1755503151747, "stop": 1755503173682, "duration": 21935}}]}, "e1f3b35ff3714fe0225699eb84cc1b87": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "21240f257da35cf6", "status": "passed", "time": {"start": 1755497198137, "stop": 1755497222758, "duration": 24621}}]}, "eda6bef994b1b0ef78f60f433fb1d4f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1afa938cea03331f", "status": "passed", "time": {"start": 1755488827629, "stop": 1755488852084, "duration": 24455}}]}, "8f73bb4e2ab622960daf9c39a4008510": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bac48bbae7e722ea", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['it wears a red leather collar', '', '', '', \"Dialogue Explore Swipe down to view earlier chats <PERSON>'s Family Safety Phrase Can physics & chemistry problems be solved by photo? it wears a red leather collar Okay! I can generate an image of a furry little monkey wearing a red leather collar. Is there anything else you'd like to add to the image? Perhaps a specific background or style? Generated by AI, for reference only <PERSON> with a blue collar Monkey with a green collar Monkey in a jungle setting DeepSeek-R1 Feel free to ask me any questions… 14:37\"]'\nassert False", "time": {"start": 1755499046665, "stop": 1755499068421, "duration": 21756}}]}, "891e31ec8bf99ceed4f462ce0c8629db": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b01a284348dc6800", "status": "passed", "time": {"start": 1755496781520, "stop": 1755496801351, "duration": 19831}}]}, "084048a337d3081654dc4414f67fce70": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "996118d4554ccb4", "status": "passed", "time": {"start": 1755501920538, "stop": 1755501940759, "duration": 20221}}]}, "0a76822399c9a8e342924e5ae6cce12c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6a4c8523ed534ea7", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Generated by AI']，实际响应: '['', '', '', '', \"Dialogue Explore Swipe down to view earlier chats 6 Reference materials A clear and pink crystal necklace in water involves considerations about the crystal's safety and how to clean it. Here's a breakdown:&#10;&#10;1. Crystal Safety in Water&#10;&#10;When considering submerging a pink crystal like pink tourmaline, the water's quality is essential; distilled or purified water is best 1.&#10;Limit the duration of submersion to brief periods 1.&#10;Extreme water temperatures (hot or cold) might pose risks; lukewarm water is recommended 1.&#10;Some crystals like agate and rose quartz are generally safe to clean with water 2.&#10;&#10;2. Cleaning and Maintenance&#10;&#10;For cleaning pink tourmaline, use a soft, lint-free cloth to wipe the gemstone gently 1.&#10;Avoid harsh chemicals or abrasive cleaning solutions 1.&#10;&#10;3. Necklace Characteristics&#10;&#10;There are handmade necklaces available with pink and clear crystal beads 3.&#10;Rose quartz is a popular crystal used in necklaces 56. DeepSeek-R1 Feel free to ask me any questions… 13:40\"]'\nassert False", "time": {"start": 1755495630162, "stop": 1755495653322, "duration": 23160}}]}, "e82a80866bdbe9a7e1ac367f20c977b5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "743d8d64aae8d5f3", "status": "passed", "time": {"start": 1755503435976, "stop": 1755503455814, "duration": 19838}}]}, "b165a17be8ab35920a6af9be7611a2c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "895da84e7cca2494", "status": "passed", "time": {"start": 1755486865908, "stop": 1755486896025, "duration": 30117}}]}, "f46a1c12d07d5949dbcf4b31314824ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "84cd193acbab3e9d", "status": "passed", "time": {"start": 1755492519120, "stop": 1755492552471, "duration": 33351}}]}, "31c983fdcc8b62f5927f99c0482b828d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "84f75d265fcaa190", "status": "passed", "time": {"start": 1755500923028, "stop": 1755500943497, "duration": 20469}}]}, "e3cc499fef74e68f1c802e097ccd0f42": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "438b148c98987bcc", "status": "passed", "time": {"start": 1755497274834, "stop": 1755497300842, "duration": 26008}}]}, "cf0ebfd1b4e2ab43e2f516ad6a1a6917": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "99cb87ce20d5cb25", "status": "passed", "time": {"start": 1755490067733, "stop": 1755490096057, "duration": 28324}}]}, "89b134ac1374e88187e793daf9f8fcab": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b6c79c64811b995d", "status": "passed", "time": {"start": 1755501851699, "stop": 1755501871785, "duration": 20086}}]}, "c2ecb960f7f893feeaa2f24a34c9d77e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f5910b6312c64eeb", "status": "passed", "time": {"start": 1755497237022, "stop": 1755497260561, "duration": 23539}}]}, "8c62567d8b8a27f77124afc90fa44336": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cdc954c986749540", "status": "passed", "time": {"start": 1755485569625, "stop": 1755485589782, "duration": 20157}}]}, "5cf50058091f34fd1ed89d0b8f717355": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6b5024390c60d478", "status": "passed", "time": {"start": 1755494664270, "stop": 1755494689073, "duration": 24803}}]}, "de5ff490f92fc399976d91fe0edc371e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6698044fc356cdfd", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755495449245, "stop": 1755495471155, "duration": 21910}}]}, "cce948f7c988b6f22bd4e8d08ca74deb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5b556f0e4be6c4d2", "status": "passed", "time": {"start": 1755502802990, "stop": 1755502822974, "duration": 19984}}]}, "1a94a631f074dc4c0ba2bd39c9518123": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "498f84666e902962", "status": "passed", "time": {"start": 1755491048611, "stop": 1755491071357, "duration": 22746}}]}, "e60dab4e55edacbecf632d4d22f368e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "30bf17e43a39dd9a", "status": "passed", "time": {"start": 1755501323608, "stop": 1755501343903, "duration": 20295}}]}, "728822fc623e888cd9efa450f4737787": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "67af36d833e6ea53", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['', '', '', '', \"Dialogue Explore Swipe down to view earlier chats A clear glass cup is a simple yet versatile item! Here's what comes to mind:&#10;&#10;Uses: It's perfect for drinking water, juice, or any beverage. You can also use it for serving desserts, holding small items, or as a decorative piece.&#10;Types: There are many different types of clear glass cups, from simple tumblers to elegant wine glasses and mugs.&#10;Care: They're generally easy to clean, but you should be careful to avoid thermal shock (sudden temperature changes) that can cause the glass to break.&#10;&#10;Do you have a specific type of clear glass cup in mind, or are you interested in something else about them? Generated by AI, for reference only Clear glass cup design variations Glass cup material properties DeepSeek-R1 Feel free to ask me any questions… 13:41\"]'\nassert False", "time": {"start": 1755495667937, "stop": 1755495689285, "duration": 21348}}]}, "a34b87ce6db1ff744d0ab6c172eb93da": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e3b85aff8f796ffc", "status": "skipped", "statusDetails": "Skipped: 重启会导致设备断开，先跳过", "time": {"start": 1755501014108, "stop": 1755501014108, "duration": 0}}]}, "04263bf3ad5402ebd901c9c0e6682325": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5b8f76f9ef2d973c", "status": "passed", "time": {"start": 1755497737069, "stop": 1755497756990, "duration": 19921}}]}, "7413abdce214459d0e44671ef65b660b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d91c0ef56e885c23", "status": "passed", "time": {"start": 1755491194871, "stop": 1755491225162, "duration": 30291}}]}, "6af3d12b439ba44d7cce5c3d3ba19e86": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d73582501560b23b", "status": "skipped", "statusDetails": "Skipped: reboot 会导致设备断开，先跳过", "time": {"start": 1755500865454, "stop": 1755500865454, "duration": 0}}]}, "f7282303534c1c8599c3343608e6f453": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "18748d8f6cbd34e5", "status": "passed", "time": {"start": 1755487619607, "stop": 1755487641791, "duration": 22184}}]}, "1498285b0d63f23df3a22c9c5262f7f3": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "29ff9d063c63e76e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Generated by AI']\nassert False", "time": {"start": 1755490654075, "stop": 1755490767973, "duration": 113898}}]}, "bfd4a9e37b70dca0b14b0ccf5246fc4a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2b071456a0f5b37e", "status": "passed", "time": {"start": 1755498750564, "stop": 1755498770495, "duration": 19931}}]}, "80dab16fde357aadc4387b5d440ed276": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e59cb7cd3b526a00", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755501219260, "stop": 1755501239140, "duration": 19880}}]}, "7d3b4e67344145885187c529ee88a9aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7e3649c5cead3c40", "status": "passed", "time": {"start": 1755501885970, "stop": 1755501906182, "duration": 20212}}]}, "ea48444c2b0789e59a64850ccfab3722": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3775db10c32d8b4d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The following event has been added for you.']\nassert False", "time": {"start": 1755485529159, "stop": 1755485555322, "duration": 26163}}]}, "ca9dd7f70b2888aafceb94247d7986f0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "76178af16b4619f6", "status": "passed", "time": {"start": 1755502102701, "stop": 1755502122824, "duration": 20123}}]}, "cd2650c8b690a339f6c3696b18b9dc0d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "520e6941e9933016", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The alarm for 9:00 AM has been set.']\nassert False", "time": {"start": 1755492841536, "stop": 1755492869129, "duration": 27593}}]}, "7fb2c589f1fda51205a5af1b549d5045": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8786240d94a2dca6", "status": "passed", "time": {"start": 1755491157581, "stop": 1755491180503, "duration": 22922}}]}, "19fa56a92b4343c1894780564290d112": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a991d6f0d8c5ec1a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Please tell me the name or number to call']\nassert False", "time": {"start": 1755498896867, "stop": 1755498921404, "duration": 24537}}]}, "54b47105d42d2a9f18eec071fba40c73": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9af44badf1488f71", "status": "passed", "time": {"start": 1755485363283, "stop": 1755485397730, "duration": 34447}}]}, "56528a816381bba6ed1ca007f557362f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f2597eda9e19a1f8", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755497771401, "stop": 1755497791206, "duration": 19805}}]}, "f05a6eb960fbbc415e4c605538080373": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3350a9687471e643", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1755488909793, "stop": 1755488933402, "duration": 23609}}]}, "5d0a6cda1787168fa2fdaaae1dee86f3": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "33d6f35532fb2f0e", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Generate a picture in the night forest for me', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755497945622, "stop": 1755497965451, "duration": 19829}}]}, "0f4d3881c287fa46a9fdaf099fc19f8d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b9e7c23ccbc12dce", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Auto-rotation is turned on now']\nassert False", "time": {"start": 1755494275918, "stop": 1755494296230, "duration": 20312}}]}, "2b2dcc407b5c428f968f62d94fe8025c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9b3d3443e1680243", "status": "passed", "time": {"start": 1755501570753, "stop": 1755501590736, "duration": 19983}}]}, "7c32e753573a480d7d5c09abab43469e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3ff715dddf18a033", "status": "failed", "statusDetails": "AssertionError: 联系人应用未打开: 初始=False, 最终=None\nassert None", "time": {"start": 1755485842173, "stop": 1755485877670, "duration": 35497}}]}, "ae0ee984c3712fd05ea04b52289e14fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7c21262394c1c3b2", "status": "passed", "time": {"start": 1755485747784, "stop": 1755485777990, "duration": 30206}}]}, "a3c84dd7a2924ee198fdb33cbc4e20b6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f0083b6882568b62", "status": "passed", "time": {"start": 1755496265650, "stop": 1755496290580, "duration": 24930}}]}, "918c52f1eb9803594ff76c724b43d5f8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "912922ba8c35081a", "status": "failed", "statusDetails": "AssertionError: : 初始=False, 最终=False, 响应='['find a restaurant near me', 'Please turn off Airplane Mode and try again', 'Airplane Mode', '']'\nassert False", "time": {"start": 1755495138083, "stop": 1755495163575, "duration": 25492}}]}, "cc44ad4097a589726631a345e0cd01ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "259b9154fa8999e4", "status": "passed", "time": {"start": 1755487746333, "stop": 1755487766680, "duration": 20347}}]}, "c3dde525f6a284fe4a3e4b670182329f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8b33ad00ff2563f7", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.']，实际响应: '['merry christmas', 'Merry Christmas to you', '', '']'\nassert False", "time": {"start": 1755499677285, "stop": 1755499699849, "duration": 22564}}]}, "e32881dd9d54414fa74d523ef27b055c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "284c47e961fd5f29", "status": "passed", "time": {"start": 1755494378817, "stop": 1755494399764, "duration": 20947}}]}, "488c24d02f5d5348f35881280e505f32": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2971cd2a051395c6", "status": "failed", "statusDetails": "AssertionError: GoogleMap应用未打开: 初始=False, 最终=False, 响应='['navigation to the address in thie image', '', '', '', 'Dialogue Explore Swipe down to view earlier chats Can physics & chemistry problems be solved by photo? How to solve primary & junior high questions? navigation to the address in thie image I am sorry, but I am unable to navigate to a specific address at this time. Generated by AI, for reference only Address search using voice commands Alternative ways to find the address Troubleshooting navigation issues DeepSeek-R1 Feel free to ask me any questions… 14:51']'\nassert False", "time": {"start": 1755499844460, "stop": 1755499871815, "duration": 27355}}]}, "ab0169efb30d26689cbce230ff455598": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a96ede0d1550352a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"I'm glad to open Youtube Music for you\", 'You need to select music to play.']\nassert False", "time": {"start": 1755500603403, "stop": 1755500638611, "duration": 35208}}]}, "cf1bdc2b1d9b604939681e5b6ac6f506": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fab7fcbbd2e8922", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Smart reminder is turned on now']\nassert False", "time": {"start": 1755494550218, "stop": 1755494570448, "duration": 20230}}]}, "963c2cd1bdb409e4cfe9589a18006e88": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "903bd91eec3dc183", "status": "failed", "statusDetails": "AssertionError: alarm_volume: 初始=False, 最终=False, 响应='['min alarm clock volume', 'Alarm volume has been set to the minimum.', '', '']'\nassert False == 1", "time": {"start": 1755492076119, "stop": 1755492104069, "duration": 27950}}]}, "a5a3cc08eb97e600c97acb65a7439ec0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "58fcfe1877b70f21", "status": "passed", "time": {"start": 1755502344477, "stop": 1755502364735, "duration": 20258}}]}, "bd9c64dabd06671b98d60748492be267": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "15f6f632a70c44dc", "status": "passed", "time": {"start": 1755487028916, "stop": 1755487056335, "duration": 27419}}]}, "c2a64f07232d43585d1dfee25c2f9407": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bf1ac4cb68f2f679", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Chong Qing Shi is Fair today. The high is forecast as 37°C and the low as 28°C.']\nassert False", "time": {"start": 1755489835403, "stop": 1755489856515, "duration": 21112}}]}, "a84e06839a37b7806fefd316aa632437": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "49ccb8ead21b239", "status": "passed", "time": {"start": 1755498119012, "stop": 1755498141101, "duration": 22089}}]}, "8b6d374ba70006ef7591c8e0bf72bb00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3b64e6faa650c1b9", "status": "passed", "time": {"start": 1755502837015, "stop": 1755502857027, "duration": 20012}}]}, "0f3523ec9dc3ea86ebaca76b6956f01c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3e428b7187a8cf95", "status": "passed", "time": {"start": 1755491119384, "stop": 1755491143604, "duration": 24220}}]}, "489e5c631d3a3ce20f77bce4c7c6632a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8d124ee927e189e3", "status": "failed", "statusDetails": "AssertionError: clock: 初始=False, 最终=False, 响应='['set my alarm volume to 50%', 'Alarm volume has been set to 50.', '', '']'\nassert False == 7", "time": {"start": 1755492690620, "stop": 1755492720206, "duration": 29586}}]}, "329fa4b06eb0b0d769c2c418ed03dab7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cbacf406046ab12b", "status": "passed", "time": {"start": 1755486700223, "stop": 1755486722902, "duration": 22679}}]}, "66496441d7401e453a580b4a8c23d111": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c5a8bb32aeabc6cc", "status": "passed", "time": {"start": 1755492327095, "stop": 1755492349610, "duration": 22515}}]}, "169e5b613c0fec2cebd053175998bf17": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9961206a47431e4b", "status": "passed", "time": {"start": 1755485794635, "stop": 1755485827601, "duration": 32966}}]}, "a5e15bb05795c5949d67f33200f4d69b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4a1cc7fd5ab62fa0", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '']'\nassert None == 15", "time": {"start": 1755491904158, "stop": 1755491926567, "duration": 22409}}]}, "37d8f85ba7c46a46b390c4fc5ab20de7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cdf5fdcbd094781a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['5 minutes', '10 minutes', '20 minutes']\nassert False", "time": {"start": 1755485892416, "stop": 1755485913499, "duration": 21083}}]}, "44b646d68146a0c48da2623a58b17f6f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "376b4abb06a154cb", "status": "passed", "time": {"start": 1755486618179, "stop": 1755486639288, "duration": 21109}}]}, "4f538fc772535a0c0811ad87d3aa9494": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d7f0e92d59493a29", "status": "passed", "time": {"start": 1755502068065, "stop": 1755502088184, "duration": 20119}}]}, "309f3fbb8586cc15e324e94cec37e7ea": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "110a39c49361d59d", "status": "passed", "time": {"start": 1755487219100, "stop": 1755487243072, "duration": 23972}}]}, "8814f1dafa698e785ee1f58faa6e745d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5447509a46e116e5", "status": "failed", "statusDetails": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', \"Dialogue Explore Swipe down to view earlier chats Can you help with high school function problems? Why don't tropical regions have auroras? Draw a beautiful girl Search for addresses on the screen I am sorry, but I am unable to search for addresses at this time. Generated by AI, for reference only Troubleshooting address search issues? Alternative methods for address search? How to manually input an address? DeepSeek-R1 Feel free to ask me any questions… 15:11\"]'\nassert None", "time": {"start": 1755501065460, "stop": 1755501087263, "duration": 21803}}]}, "b12ee4e4a5d7e51ba0abe166b6c90352": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2caaf5389b91cfdd", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"I'm glad to open Youtube Music for you\", 'You need to select music to play.']\nassert False", "time": {"start": 1755500553285, "stop": 1755500588707, "duration": 35422}}]}, "d9f01ef1af79559082ce9e9b2e40295f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "47ae2654f354a092", "status": "passed", "time": {"start": 1755485314449, "stop": 1755485348812, "duration": 34363}}]}, "08bb1ce458e948b9a8084fb3ec1f2c83": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b1ce2a37101255d3", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '[\"Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'\", \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498607644, "stop": 1755498627395, "duration": 19751}}]}, "0e1d64e0daaaf11983cdc488ea4b0993": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "23a5a6d1930c8de0", "status": "skipped", "statusDetails": "Skipped: power off 会导致设备断开，先跳过", "time": {"start": 1755500794247, "stop": 1755500794247, "duration": 0}}]}, "984a0fd313bba0ca2f20f0bbff732eb8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f91b1dc90fffec91", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755503297233, "stop": 1755503316995, "duration": 19762}}]}, "e5dc64184617a9497ec52a3b74103b55": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "13bdbf09737f718", "status": "broken", "statusDetails": "TypeError: '>=' not supported between instances of 'NoneType' and 'NoneType'", "time": {"start": 1755491616048, "stop": 1755491637028, "duration": 20980}}]}, "981b71ad744b9a603c31ab4832ff9439": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a146b3901c7ec634", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['document provides a summary of the application']\nassert False", "time": {"start": 1755490911418, "stop": 1755490961512, "duration": 50094}}]}, "0d9f0969c1336e077b7ada5963a2516a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "63058376ece98827", "status": "skipped", "statusDetails": "Skipped: 该脚本较特殊，先跳过", "time": {"start": 1755491332233, "stop": 1755491332233, "duration": 0}}]}, "3f0254c16b7bc20d18094d502f49138a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "646f811621529e05", "status": "passed", "time": {"start": 1755498821100, "stop": 1755498841227, "duration": 20127}}]}, "461fa25c15c0a862f353e5384438bc5d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "33c969279ed591b6", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['help me generate a picture of a white facial cleanser product advertisement', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498437045, "stop": 1755498456665, "duration": 19620}}]}, "aac47e3ff4229b41a579f7c9ec938afb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8bc16052575dc591", "status": "passed", "time": {"start": 1755488351959, "stop": 1755488378215, "duration": 26256}}]}, "a084b34b62992b8f17deb64af6e57e39": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aaa5ad47c39b0896", "status": "passed", "time": {"start": 1755503841503, "stop": 1755503861474, "duration": 19971}}]}, "600ddf60808e2a751a4a4742a65811c7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6d46a40ce803f513", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Please tell me the name or number to call.']\nassert False", "time": {"start": 1755503469728, "stop": 1755503494396, "duration": 24668}}]}, "8c5a5747e91f2cb0412111d5027bb7ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e1e98f02734c9d61", "status": "passed", "time": {"start": 1755494627022, "stop": 1755494649704, "duration": 22682}}]}, "ca269ac93364dc6180676f5680956b32": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a420961df9d02bd2", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755500807069, "stop": 1755500827069, "duration": 20000}}]}, "5b1e68388004fe021690469c3d83b485": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "afcefa8a3e8473ce", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The alarm for 10:00 AM has been set.']\nassert False", "time": {"start": 1755492566837, "stop": 1755492596225, "duration": 29388}}]}, "e4bab2ec1074fdbc8b4508dfad12adfc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "afac8a8324ac4f58", "status": "passed", "time": {"start": 1755492015277, "stop": 1755492061907, "duration": 46630}}]}, "afde8e86697e1ec7ad65ac0c993e60f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fbc3d56dd9367d19", "status": "passed", "time": {"start": 1755494134610, "stop": 1755494155212, "duration": 20602}}]}, "89e187687dfa3317845107c44a62f287": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "552c1620bb9cc1e6", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['help me generate a picture of a puppy', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755498403678, "stop": 1755498422882, "duration": 19204}}]}, "bf94eb080274f830f3097dd5adde1ed1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e67b14d97324c4e8", "status": "passed", "time": {"start": 1755493921112, "stop": 1755493943194, "duration": 22082}}]}, "fd79b0d35f1f4639521f70b269d3aadc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a0e7cec935f6313e", "status": "passed", "time": {"start": 1755500046558, "stop": 1755500066788, "duration": 20230}}]}, "09f397887d36f3ef1e86e3c78272f1d6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b08b47535b38df0", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755495413969, "stop": 1755495434773, "duration": 20804}}]}, "d18ed3013dc7867440fb611fb474ca05": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "50d71690f48a3a47", "status": "passed", "time": {"start": 1755491308350, "stop": 1755491330893, "duration": 22543}}]}, "599b7a465f619c38a4638073f59c38c0": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ca63fd21108cd88d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755503400629, "stop": 1755503421629, "duration": 21000}}]}, "c612b04b455e8fbc03acd18c2fc89827": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5fb4ac38ac8412ad", "status": "passed", "time": {"start": 1755498049088, "stop": 1755498069323, "duration": 20235}}]}, "16f38913a9d7e0ffc4c5ac51d5acf5c7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f6ab1c2ea88d0024", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755494413985, "stop": 1755494433523, "duration": 19538}}]}, "8bf93fd8ac952a757cc694ecc78b8d51": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "64622074bee7fe1f", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Sorry, I couldn't locate the setting option(s) for call hold\"]\nassert False", "time": {"start": 1755485604078, "stop": 1755485629027, "duration": 24949}}]}, "18fb8c43c609a9825fe52e528761fd1b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5be804837474a0ad", "status": "passed", "time": {"start": 1755492441441, "stop": 1755492466169, "duration": 24728}}]}, "519d11d818a361bc75d5af94c6a68b28": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "70f46be2f8439194", "status": "passed", "time": {"start": 1755489906620, "stop": 1755489927585, "duration": 20965}}]}, "700a4f81d53d76c265778c230c99dd8c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "39524bbe77ff3663", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Generate a picture of a jungle stream for me', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755497980095, "stop": 1755497999632, "duration": 19537}}]}, "b9bb05ac1dcf8926da63d4ecbb1524cd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "99fe32ddc0a0735c", "status": "passed", "time": {"start": 1755491274030, "stop": 1755491293750, "duration": 19720}}]}, "57a9b2e6f318afd186b838ed42ebd55c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9d12063ebb288a1d", "status": "passed", "time": {"start": 1755497598928, "stop": 1755497620227, "duration": 21299}}]}, "e21c5dda6a9f09862a68c3a0bcda554a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a3a50fc447cbdbb6", "status": "passed", "time": {"start": 1755502171721, "stop": 1755502192104, "duration": 20383}}]}, "dc901cadfe1de0042de7c0f7461a804e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7aa33dc6346e497d", "status": "passed", "time": {"start": 1755496542275, "stop": 1755496561944, "duration": 19669}}]}, "e1ef5de48cb99781fc16bc01be62dca2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5c144d97a0be73bb", "status": "passed", "time": {"start": 1755498855312, "stop": 1755498882872, "duration": 27560}}]}, "6ba20fdcdbf83dd327ea91bd93e697e0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1325fb4910bba5dc", "status": "passed", "time": {"start": 1755500001861, "stop": 1755500029431, "duration": 27570}}]}, "543965b4120af95548616c95b1b70ef1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4cb65b029de52b72", "status": "passed", "time": {"start": 1755489328127, "stop": 1755489355410, "duration": 27283}}]}, "07dafe2b3e3ed9841a34e9fd19de58be": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "893a242d4f3c4168", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '[\"Summarize what I'm reading\", 'Unable to summarize the content on this page. You can try sending links or uploading documents.', '', '']'\nassert False", "time": {"start": 1755502768861, "stop": 1755502788565, "duration": 19704}}]}, "c04d9357fdaf44e6ee27f8a97ece6c5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2bb64e9111015fac", "status": "passed", "time": {"start": 1755501709038, "stop": 1755501728993, "duration": 19955}}]}, "5d0294174a7d609e38392f61f2170810": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "604ad5d0d288c7a9", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755496473969, "stop": 1755496493684, "duration": 19715}}]}, "3004e41c81a7ebd857f79d043aaf59df": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "95447eece93e8f08", "status": "passed", "time": {"start": 1755487573336, "stop": 1755487604887, "duration": 31551}}]}, "d960192ea83ce0c13a534ec13ca1700e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "88e23c874fd42d8d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Here are your alarms']\nassert False", "time": {"start": 1755489577196, "stop": 1755489610377, "duration": 33181}}]}, "fa8d6ac5c42acf5f644e6f5370a9a773": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9887a04d8566e90d", "status": "passed", "time": {"start": 1755496747596, "stop": 1755496767129, "duration": 19533}}]}, "544fc8b021d2dbcaf295cd05b798f816": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d8d6a9cb72f9a9b2", "status": "passed", "time": {"start": 1755502310048, "stop": 1755502330262, "duration": 20214}}]}, "1470cf4116a3328d5a8812cd15bb56f8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5840f511edbe2418", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['turn up the volume to the max', 'Media volume has been set to the maximum.', '', '']'\nassert None == 15", "time": {"start": 1755494924832, "stop": 1755494944364, "duration": 19532}}]}, "53ec4c77118606257c016fc2f7b22065": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1efae155b7e6807e", "status": "passed", "time": {"start": 1755486910782, "stop": 1755486938129, "duration": 27347}}]}, "85b61394b4b07c85faf6e5081371fbf2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5471e52cd0826090", "status": "passed", "time": {"start": 1755494100287, "stop": 1755494120235, "duration": 19948}}]}, "1695232002b2ad29ffa1faf52965470d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cf3c5480c5f7b9db", "status": "passed", "time": {"start": 1755497059913, "stop": 1755497079816, "duration": 19903}}]}, "9fcc7f87aa3845b28893959bf17baa2b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ced88cbf8385740", "status": "passed", "time": {"start": 1755503082614, "stop": 1755503103467, "duration": 20853}}]}, "e1b97b8698ff620d6d8faf32f381c874": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "62ad3951ae903186", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Generate a landscape painting image for me', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755497911818, "stop": 1755497931402, "duration": 19584}}]}, "2060dd1cfd03194548c0456a10798266": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a2427cf826f1db8f", "status": "passed", "time": {"start": 1755487332893, "stop": 1755487359566, "duration": 26673}}]}, "0dc117f78053bbddaf3ffcf69df0af9d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d20b72b0d68e40dc", "status": "passed", "time": {"start": 1755500693798, "stop": 1755500719696, "duration": 25898}}]}, "43a8e6496d8d78f2b8bc066858f8bdd9": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "dbf0ff17969dd66a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755500321856, "stop": 1755500341678, "duration": 19822}}]}, "569e770c250388bfbcf64d0cbbb8b351": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "380eda55eb3e533d", "status": "passed", "time": {"start": 1755496406106, "stop": 1755496425612, "duration": 19506}}]}, "7c74ed3e622ad29dd79be61222ad59bc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cff3b4cbfa4db88a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1755492884081, "stop": 1755492904872, "duration": 20791}}]}, "e7de7a828ef1b59725204585ed7e1d64": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c38c8c516435e68d", "status": "passed", "time": {"start": 1755501996136, "stop": 1755502018765, "duration": 22629}}]}, "b28e20f7b76af65c54477681eee78169": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3c1bd1aed8b1f3f3", "status": "failed", "statusDetails": "AssertionError: contacts: 初始=False, 最终=None, 响应='['make a phone call to 17621905233', '', '', '', \"Dialogue Explore Swipe down to view earlier chats 14:47 Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Can you help with high school function problems? How do wind towers achieve natural cooling? Chelsea's Nkunku Sale Funds Simons Move make a phone call to 17621905233 DeepSeek-R1 Feel free to ask me any questions… 14:47\", '[com.sh.smart.caller页面内容] Tom | 176 2190 5233 | 上海市 | 正在呼叫 | 开始录音 | 静音 | 保持 | 纯净通话 | 纯净通话 | 添加通话']'\nassert None", "time": {"start": 1755499625974, "stop": 1755499662647, "duration": 36673}}]}, "acdb323f998d6127fbebbc545f6e8a59": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "584b2a81c0df7d9", "status": "failed", "statusDetails": "AssertionError: Dalier应用未打开: 初始=True, 最终=None, 响应='['open phone', 'Done!', '', '', '[com.sh.smart.caller页面内容] 通话记录 | 联系人']'\nassert None", "time": {"start": 1755486047168, "stop": 1755486079242, "duration": 32074}}]}, "c076d6e18e779bfeb810e69b30339aa2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "71476eaab257c12", "status": "passed", "time": {"start": 1755493687630, "stop": 1755493729442, "duration": 41812}}]}, "edbb2ef8b0440e0325be2bfae4eb0bee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "eb943ac598b62ae4", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755496815606, "stop": 1755496835096, "duration": 19490}}]}, "d235f5ef7da67b833ad362b7c693c8f1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7728451f1dfab02c", "status": "passed", "time": {"start": 1755494585004, "stop": 1755494612849, "duration": 27845}}]}, "8373737839693413a37e35b627a0f5de": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f8882631a98768d3", "status": "broken", "statusDetails": "TypeError: '<=' not supported between instances of 'NoneType' and 'NoneType'", "time": {"start": 1755493885157, "stop": 1755493906253, "duration": 21096}}]}, "4cfe8e55b2a91a62bbf1141ffc0cc530": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4e5427c48277aaae", "status": "passed", "time": {"start": 1755496713774, "stop": 1755496733278, "duration": 19504}}]}, "d8a01bf3d9b9092622318d8d22f17d9e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c05ea50a96c6d6b7", "status": "passed", "time": {"start": 1755496089361, "stop": 1755496109052, "duration": 19691}}]}, "d18ea588937139bb162adb1092a66013": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "edb4f252b7f5ccca", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['turn on wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'\nassert False", "time": {"start": 1755494742747, "stop": 1755494763728, "duration": 20981}}]}, "511b4baaab6d7793eefd3f92b3a77d8b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e1865ec637991cf1", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['min notifications volume', 'Notification volume has been set to the minimum.', '', '']'\nassert None == 0", "time": {"start": 1755492155024, "stop": 1755492174571, "duration": 19547}}]}, "b3fa1d22b59def5f059cd9b0eefbe2b0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c92ac4ae9a3abff4", "status": "passed", "time": {"start": 1755487454549, "stop": 1755487482328, "duration": 27779}}]}, "548362627ce690e10e5f8ca35d247c62": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "642f6aad5be1ce56", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['maximum volume', 'Media volume has been set to the maximum.', '', '']'\nassert None == 15", "time": {"start": 1755491977526, "stop": 1755492000401, "duration": 22875}}]}, "359341836df6d43ec99426e6918dc6ca": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "80a4a14aa96684fa", "status": "passed", "time": {"start": 1755490563243, "stop": 1755490639997, "duration": 76754}}]}, "861f0c94cdad5a5d60cd9fc71e2429d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6b94bd4c67704947", "status": "passed", "time": {"start": 1755487146098, "stop": 1755487167886, "duration": 21788}}]}, "30903d6e764eebda77a45c5af4464d00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7ed72d7e314cdad5", "status": "passed", "time": {"start": 1755489184518, "stop": 1755489204319, "duration": 19801}}]}, "7ba4a9d343c0f63e9f654ce03ea4fa51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f309194462f0435e", "status": "passed", "time": {"start": 1755502241083, "stop": 1755502261282, "duration": 20199}}]}, "5eaa5a3015ce02f75c4c021fdbd2f78d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "83b3448b88954127", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', 'Do you enjoy being outdoors?', 'enjoy running', 'outdoors', 'Do you have a favorite park or location where you like to go running on the grass?']，实际响应: '['running on the grass', '', '', '', 'Dialogue Explore Swipe down to view earlier chats Can you help with high school function problems? Make me a personal webpage AI Wallpaper running on the grass That sounds like a lot of fun! I bet your puppy loves running on the grass. Is it a regular part of your routine? Generated by AI, for reference only How to keep puppy safe on grass? Best puppy-friendly grass types? Puppy health benefits of grass play? DeepSeek-R1 Feel free to ask me any questions… 15:10']'\nassert False", "time": {"start": 1755501027117, "stop": 1755501050646, "duration": 23529}}]}, "42bb23fa5566b20ae050e85bbee099ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "78e25780691074a5", "status": "passed", "time": {"start": 1755497452537, "stop": 1755497472192, "duration": 19655}}]}, "fca782bf64e9cf595a09003471d4cc31": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4bcc04d743d0b567", "status": "passed", "time": {"start": 1755499365745, "stop": 1755499385908, "duration": 20163}}]}, "434b905bf8be3ce2ee79606468e155db": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5e11b33a5d2e52ff", "status": "failed", "statusDetails": "AssertionError: google_playstore: 初始=False, 最终=False, 响应='['install whatsapp', 'Redirecting to the Play Store to search for whatsapp messenger', '', '', '[com.android.vending页面内容] 14:36']'\nassert False", "time": {"start": 1755499005649, "stop": 1755499032171, "duration": 26522}}]}, "ebd9dc4871a5e78e68997fd53d4e9d06": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "650a1e3e10f46f5a", "status": "passed", "time": {"start": 1755494703631, "stop": 1755494728665, "duration": 25034}}]}, "d4409d015660212a7021f4aa7f849f30": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3499b9f6e42a76b", "status": "passed", "time": {"start": 1755495890875, "stop": 1755495912294, "duration": 21419}}]}, "6df22ddc82daabcf8389e60296dc694e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "149ef15a425e15e8", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Premier League Goals Ranking']\nassert False", "time": {"start": 1755488986658, "stop": 1755489015120, "duration": 28462}}]}, "7c862e3b7376f0fe8afd8d3c5d41a1ab": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c840d107195e187", "status": "passed", "time": {"start": 1755503703863, "stop": 1755503723945, "duration": 20082}}]}, "d28e2beb1494e42c051e9b99add608fb": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "98c96ed1e0215951", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Here are your alarms. Which one do you want to delete?']\nassert False", "time": {"start": 1755488734617, "stop": 1755488774867, "duration": 40250}}]}, "861aa58f9a3d0d9c9861d88316e784c5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cd06fc08aaa2d6c8", "status": "passed", "time": {"start": 1755486093731, "stop": 1755486115097, "duration": 21366}}]}, "6b4c2fb43e48aa6ef45b7a33c8b2d9ef": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "972dfc9d92038e5f", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.']，实际响应: '['puppy', '', '', '', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats Welch Loans to <PERSON> “<PERSON>” <PERSON><PERSON>'s Demand Stalls £20m Man Utd Exit puppy Oh, you have a puppy! That's wonderful. I hope you're having a great time with your furry friend. Do you have any fun stories to share about your puppy? Generated by AI, for reference only Puppy training tips and tricks Puppy food and nutrition advice Puppy health and vaccination schedule DeepSeek-R1 Feel free to ask me any questions… 15:07\"]'\nassert False", "time": {"start": 1755500841668, "stop": 1755500863743, "duration": 22075}}]}, "cda905ef365af8bbcc5fba28f6bde9ea": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e141d666e22735b1", "status": "passed", "time": {"start": 1755493124347, "stop": 1755493150306, "duration": 25959}}]}, "bc062eca91b16841cac5c9865921b5c1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9006abb6710c0f91", "status": "passed", "time": {"start": 1755485460649, "stop": 1755485480746, "duration": 20097}}]}, "a455fee07fb3c7d389380cb95d9a092c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a406107bc0f5f0df", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Please tell me the name or number to call']\nassert False", "time": {"start": 1755499547024, "stop": 1755499571768, "duration": 24744}}]}, "ff8d76af98b9fdfaa206acbf87daa843": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fe288e058f9786e6", "status": "passed", "time": {"start": 1755497525905, "stop": 1755497550632, "duration": 24727}}]}, "1da800483d0bd7f8dbe657a8d5c37f76": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b46a87de54c4347a", "status": "passed", "time": {"start": 1755496609728, "stop": 1755496629475, "duration": 19747}}]}, "4a00cb3818a7086991fb9f7a4d2a3bb5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7d15a70337843500", "status": "passed", "time": {"start": 1755503669750, "stop": 1755503689735, "duration": 19985}}]}, "81f981a4ddbff762d2de1cd977c5568a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "15425d13f5f3e6ac", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Generate a circular car logo image with a three-pointed star inside the logo', \"You've reached the image generation limit for today.\", '', '']'\nassert False", "time": {"start": 1755497877576, "stop": 1755497897111, "duration": 19535}}]}, "6f7052acfdd45e34e5dded44ad87416e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8b9bdfa7542f060d", "status": "passed", "time": {"start": 1755491755037, "stop": 1755491777929, "duration": 22892}}]}, "fdcf3737e32a4361e11902caf25fed5f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3c79f2c5305979dc", "status": "passed", "time": {"start": 1755493958081, "stop": 1755493978603, "duration": 20522}}]}, "abcb20b3882e9c0dbf1add7f63082581": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5c85d779948a47f0", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755500770998, "stop": 1755500792516, "duration": 21518}}]}, "00e9182de9c9d3297d90ff42d6771a57": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "995c07e42bbb58ee", "status": "passed", "time": {"start": 1755501817438, "stop": 1755501837336, "duration": 19898}}]}, "1ed60306ec6b3749ffacc2d410664db2": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c24284a89688e8f0", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['AIGC', 'document provides a summary of the application']\nassert False", "time": {"start": 1755490368695, "stop": 1755490418920, "duration": 50225}}]}, "d41a9c89a400d807309a9cecf36c0728": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "be1705873140635c", "status": "passed", "time": {"start": 1755486490554, "stop": 1755486529925, "duration": 39371}}]}, "a6002c930e7d5977f441f6060af6308d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4b776d21ba83ca4b", "status": "passed", "time": {"start": 1755488049679, "stop": 1755488069821, "duration": 20142}}]}, "6c315a350a546e1382e435255d28245b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ae97570254349a81", "status": "passed", "time": {"start": 1755501743438, "stop": 1755501763776, "duration": 20338}}]}, "d8bd499fa9e4e04741c5c255fac9036d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "96cbde4d10134ba1", "status": "passed", "time": {"start": 1755499440032, "stop": 1755499460063, "duration": 20031}}]}, "936ae2bf6db744b69d4acf28b22f7646": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1907c176f4ae5832", "status": "passed", "time": {"start": 1755485928184, "stop": 1755485960809, "duration": 32625}}]}, "436193bc4e8c44d21b6520da0589f88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "26ec988295aa927a", "status": "passed", "time": {"start": 1755499222973, "stop": 1755499242838, "duration": 19865}}]}, "b2f52f3c587a626460e5698cac861baf": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ad2c40aef0eb132d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755493465877, "stop": 1755493487673, "duration": 21796}}]}, "78164cec6ab8359be9416229a4882ef9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f706113c78495e8c", "status": "passed", "time": {"start": 1755487071226, "stop": 1755487095199, "duration": 23973}}]}, "f06396240414bb7ac3c5c049002eef1e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "45006ddc3ec43548", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done', 'Welcome to WhatsApp']\nassert False", "time": {"start": 1755501138302, "stop": 1755501165239, "duration": 26937}}]}, "bda3c1f82ca5c246958b5bf50db09a67": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3c70382151a25d97", "status": "passed", "time": {"start": 1755502626119, "stop": 1755502645058, "duration": 18939}}]}, "78de5607a6208f59723ba6cf4fcf09c4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4fcf5d7f761f5451", "status": "passed", "time": {"start": 1755487781800, "stop": 1755487804342, "duration": 22542}}]}, "eda16efd838471b84f33f12ec91662c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dda705191d063540", "status": "passed", "time": {"start": 1755501253489, "stop": 1755501273810, "duration": 20321}}]}, "fbd0b78d6e1c10d8dc70e2bd96aa5bdc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "90249de4c88a455f", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Your current location:']\nassert False", "time": {"start": 1755500733993, "stop": 1755500756225, "duration": 22232}}]}, "45b57073b776ed5666f2f20a47a4638f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "43ef1df42706b5f2", "status": "passed", "time": {"start": 1755493427898, "stop": 1755493451072, "duration": 23174}}]}, "9375a7a6d7b67755564dec18857d7c65": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5081078a491c2abe", "status": "passed", "time": {"start": 1755496226465, "stop": 1755496251176, "duration": 24711}}]}, "454f04318d433db60e7e6f2de5790fc3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "350286206c706767", "status": "passed", "time": {"start": 1755493083377, "stop": 1755493109965, "duration": 26588}}]}, "c45aa63628fe12b375ba7e65c39d93b1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4fc9d1ce458d51a0", "status": "passed", "time": {"start": 1755489369956, "stop": 1755489391123, "duration": 21167}}]}, "cc158f7c05891fbac271a86692bc57a6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6aeb533208e0773f", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%', 'That sounds lovely!']，实际响应: '['there is a colorful butterfly beside it', '', '', '', \"Dialogue Explore Swipe down to view earlier chats Liverpool Eye Isak Move there is a colorful butterfly beside it That paints a lovely picture! A field of sunflowers, surrounded by glowing bubbles, and a colorful butterfly...it sounds like a scene from a dream. Are you enjoying this moment, or is there anything specific you'd like to know or do related to this imagery? Generated by AI, for reference only Describe the butterfly in more detail How to create glowing bubbles? What is the meaning of this scene? DeepSeek-R1 Feel free to ask me any questions… 15:47\"]'\nassert False", "time": {"start": 1755503224026, "stop": 1755503246190, "duration": 22164}}]}, "fae56e9bcf9e0511ef4a7c93775731e3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dc97a20445a73af", "status": "passed", "time": {"start": 1755495527342, "stop": 1755495548491, "duration": 21149}}]}, "85e8e199dba3ac2c9d89e132805a4404": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6adf98475880470c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['not installed yet. Please download the app and try again.']\nassert False", "time": {"start": 1755488433728, "stop": 1755488473304, "duration": 39576}}]}, "29390733aaf67e070f7c061b70bad8a5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ba6ba48f138d3670", "status": "passed", "time": {"start": 1755491085520, "stop": 1755491105073, "duration": 19553}}]}, "221d94649ab3a384e6bc24767dceba21": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "55db0b714c0ed415", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['No number for a. Please tell me the name or number to call.']\nassert False", "time": {"start": 1755499586623, "stop": 1755499611252, "duration": 24629}}]}, "228f8713a07d6ddbb8729f2f567c21d0": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5dc251e239065bf5", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['not installed yet. Please download the app and try again.', 'I need to download']，实际响应: '['play music on visha', 'Done!', '', '', '[com.transsion.magicshow页面内容] 所有视频 | 播放列表 | 文件夹 | 8月15日 | 00:28 | Screen_Recording_20250815_144855.mp4 | 3.52 MB | 01:01 | Screen_Recording_20250815_144649.mp4 | 7.60 MB | 00:30 | Screen_Recording_20250815_144544.mp4 | 3.85 MB | 00:29 | Screen_Recording_20250815_143916.mp4 | 3.35 MB | 00:30 | Screen_Recording_20250815_142637.mp4 | 3.33 MB | Screen_Recording_20250815_115313.mp4']'\nassert False", "time": {"start": 1755488564197, "stop": 1755488603757, "duration": 39560}}]}, "d2d9aa669417404f06e84a7a4387c55a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "14b0ac6aa516cfc", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755493392051, "stop": 1755493413114, "duration": 21063}}]}}