{"uid": "cb998d564c571bbb", "name": "测试pause music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_pause_music.TestEllaPauseMusic#test_pause_music", "historyId": "464c8ea2a15f7ff86b8e0a347a821945", "time": {"start": 1755486129429, "stop": 1755486150805, "duration": 21376}, "description": "pause music", "descriptionHtml": "<p>pause music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755486116465, "stop": 1755486129427, "duration": 12962}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755486129427, "stop": 1755486129428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "pause music", "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1755486129429, "stop": 1755486150599, "duration": 21170}, "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1755486129429, "stop": 1755486150372, "duration": 20943}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755486150372, "stop": 1755486150598, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "775ef866c6667f37", "name": "测试总结", "source": "775ef866c6667f37.txt", "type": "text/plain", "size": 179}, {"uid": "d2727c40c4bc00ad", "name": "test_completed", "source": "d2727c40c4bc00ad.png", "type": "image/png", "size": 183556}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755486150599, "stop": 1755486150601, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755486150601, "stop": 1755486150804, "duration": 203}, "status": "passed", "steps": [], "attachments": [{"uid": "44cbee94ed45c782", "name": "测试总结", "source": "44cbee94ed45c782.txt", "type": "text/plain", "size": 179}, {"uid": "ded3d5e6ca9db177", "name": "test_completed", "source": "ded3d5e6ca9db177.png", "type": "image/png", "size": 183613}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "84d13d8d268a09cd", "name": "stdout", "source": "84d13d8d268a09cd.txt", "type": "text/plain", "size": 11544}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755486150806, "stop": 1755486150806, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755486150808, "stop": 1755486152174, "duration": 1366}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_music"}, {"name": "subSuite", "value": "TestEllaPauseMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "cb998d564c571bbb.json", "parameterValues": []}