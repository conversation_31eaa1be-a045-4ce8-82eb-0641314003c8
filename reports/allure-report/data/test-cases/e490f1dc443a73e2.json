{"uid": "e490f1dc443a73e2", "name": "测试open wifi", "fullName": "testcases.test_ella.system_coupling.test_open_wifi.TestEllaOpenWifi#test_open_wifi", "historyId": "99709ca7d9951f6f7049b49ea81d0cd1", "time": {"start": 1755492363961, "stop": 1755492385305, "duration": 21344}, "description": "测试open wifi指令", "descriptionHtml": "<p>测试open wifi指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755492350942, "stop": 1755492363960, "duration": 13018}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755492363960, "stop": 1755492363960, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "测试open wifi指令", "status": "passed", "steps": [{"name": "执行命令: open wifi", "time": {"start": 1755492363961, "stop": 1755492385068, "duration": 21107}, "status": "passed", "steps": [{"name": "执行命令: open wifi", "time": {"start": 1755492363961, "stop": 1755492384820, "duration": 20859}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755492384820, "stop": 1755492385068, "duration": 248}, "status": "passed", "steps": [], "attachments": [{"uid": "e114e7e4f111d658", "name": "测试总结", "source": "e114e7e4f111d658.txt", "type": "text/plain", "size": 168}, {"uid": "a27da214635e5fd", "name": "test_completed", "source": "a27da214635e5fd.png", "type": "image/png", "size": 149359}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755492385068, "stop": 1755492385071, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证wifi已打开", "time": {"start": 1755492385071, "stop": 1755492385071, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755492385071, "stop": 1755492385304, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "990433bcb4d09cd7", "name": "测试总结", "source": "990433bcb4d09cd7.txt", "type": "text/plain", "size": 168}, {"uid": "a6bc51dab828d410", "name": "test_completed", "source": "a6bc51dab828d410.png", "type": "image/png", "size": 149599}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "ef0f3e75338957e7", "name": "stdout", "source": "ef0f3e75338957e7.txt", "type": "text/plain", "size": 11841}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 6, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755492385306, "stop": 1755492385306, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755492385308, "stop": 1755492386676, "duration": 1368}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_wifi"}, {"name": "subSuite", "value": "TestEllaOpenWifi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_wifi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e490f1dc443a73e2.json", "parameterValues": []}