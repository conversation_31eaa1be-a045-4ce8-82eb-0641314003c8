{"uid": "dcbdd9b25bac60dc", "name": "测试A sports car is parked on the street side", "fullName": "testcases.test_ella.unsupported_commands.test_a_sports_car_is_parked_on_the_street_side.TestEllaOpenPlayPoliticalNews#test_a_sports_car_is_parked_on_the_street_side", "historyId": "b6eee20d1fad16a048ce8490d7189be4", "time": {"start": 1755495926808, "stop": 1755495949771, "duration": 22963}, "description": "测试A sports car is parked on the street side 指令", "descriptionHtml": "<p>测试A sports car is parked on the street side 指令</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['The following images are generated for you.', 'Do you enjoy seeing sports cars?', \"That's a cool observation! Does it look like a car you'd like to own? What kind of sports car is it?\"]，实际响应: '['A sports car is parked on the street side', '', '', '', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 13:45 Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How to use Ask About Screen Can you help with high school function problems? Credit Card Hacks Cut US Trip Costs A sports car is parked on the street side Generating image… 60% Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate 13:45\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_a_sports_car_is_parked_on_the_street_side.TestEllaOpenPlayPoliticalNews object at 0x0000020BD2ECD350>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD65E66D0>\n\n    @allure.title(\"测试A sports car is parked on the street side\")\n    @allure.description(\"测试A sports car is parked on the street side 指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_a_sports_car_is_parked_on_the_street_side(self, ella_app):\n        \"\"\"测试A sports car is parked on the street side 命令\"\"\"\n        command = \"A sports car is parked on the street side\"\n        expected_text = ['The following images are generated for you.','Do you enjoy seeing sports cars?',\n                         \"That's a cool observation! Does it look like a car you'd like to own? What kind of sports car is it?\"]\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['The following images are generated for you.', 'Do you enjoy seeing sports cars?', \"That's a cool observation! Does it look like a car you'd like to own? What kind of sports car is it?\"]，实际响应: '['A sports car is parked on the street side', '', '', '', \"Dialogue Explore Swipe down to view earlier chats 13:45 Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh How to use Ask About Screen Can you help with high school function problems? Credit Card Hacks Cut US Trip Costs A sports car is parked on the street side Generating image… 60% Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate 13:45\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_a_sports_car_is_parked_on_the_street_side.py:31: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755495913694, "stop": 1755495926807, "duration": 13113}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755495926807, "stop": 1755495926807, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "测试A sports car is parked on the street side 指令", "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['The following images are generated for you.', 'Do you enjoy seeing sports cars?', \"That's a cool observation! Does it look like a car you'd like to own? What kind of sports car is it?\"]，实际响应: '['A sports car is parked on the street side', '', '', '', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 13:45 Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How to use Ask About Screen Can you help with high school function problems? Credit Card Hacks Cut US Trip Costs A sports car is parked on the street side Generating image… 60% Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate 13:45\"]'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_a_sports_car_is_parked_on_the_street_side.TestEllaOpenPlayPoliticalNews object at 0x0000020BD2ECD350>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD65E66D0>\n\n    @allure.title(\"测试A sports car is parked on the street side\")\n    @allure.description(\"测试A sports car is parked on the street side 指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_a_sports_car_is_parked_on_the_street_side(self, ella_app):\n        \"\"\"测试A sports car is parked on the street side 命令\"\"\"\n        command = \"A sports car is parked on the street side\"\n        expected_text = ['The following images are generated for you.','Do you enjoy seeing sports cars?',\n                         \"That's a cool observation! Does it look like a car you'd like to own? What kind of sports car is it?\"]\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)\n>           assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\nE           AssertionError: 响应文本应包含['The following images are generated for you.', 'Do you enjoy seeing sports cars?', \"That's a cool observation! Does it look like a car you'd like to own? What kind of sports car is it?\"]，实际响应: '['A sports car is parked on the street side', '', '', '', \"Dialogue Explore Swipe down to view earlier chats 13:45 Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh How to use Ask About Screen Can you help with high school function problems? Credit Card Hacks Cut US Trip Costs A sports car is parked on the street side Generating image… 60% Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate 13:45\"]'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_a_sports_car_is_parked_on_the_street_side.py:31: AssertionError", "steps": [{"name": "执行命令: A sports car is parked on the street side", "time": {"start": 1755495926808, "stop": 1755495949762, "duration": 22954}, "status": "passed", "steps": [{"name": "执行命令: A sports car is parked on the street side", "time": {"start": 1755495926808, "stop": 1755495949541, "duration": 22733}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755495949541, "stop": 1755495949762, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "969daba7822b7b3c", "name": "测试总结", "source": "969daba7822b7b3c.txt", "type": "text/plain", "size": 627}, {"uid": "7205e380cc992461", "name": "test_completed", "source": "7205e380cc992461.png", "type": "image/png", "size": 205583}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755495949763, "stop": 1755495949769, "duration": 6}, "status": "failed", "statusMessage": "AssertionError: 响应文本应包含['The following images are generated for you.', 'Do you enjoy seeing sports cars?', \"That's a cool observation! Does it look like a car you'd like to own? What kind of sports car is it?\"]，实际响应: '['A sports car is parked on the street side', '', '', '', \"Dialogue Explore <PERSON>wi<PERSON> down to view earlier chats 13:45 Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh How to use Ask About Screen Can you help with high school function problems? Credit Card Hacks Cut US Trip Costs A sports car is parked on the street side Generating image… 60% Exit AI Image Generator DeepSeek-R1 Describe the image you want to generate 13:45\"]'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_a_sports_car_is_parked_on_the_street_side.py\", line 31, in test_a_sports_car_is_parked_on_the_street_side\n    assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 0, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "1e80ce827b28cdba", "name": "stdout", "source": "1e80ce827b28cdba.txt", "type": "text/plain", "size": 15310}], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 3, "hasContent": true, "stepsCount": 4, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755495949779, "stop": 1755495949978, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "2a93fc662f12e7b", "name": "失败截图-TestEllaOpenPlayPoliticalNews", "source": "2a93fc662f12e7b.png", "type": "image/png", "size": 204320}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755495949980, "stop": 1755495951367, "duration": 1387}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_a_sports_car_is_parked_on_the_street_side"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_a_sports_car_is_parked_on_the_street_side"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "dcbdd9b25bac60dc.json", "parameterValues": []}