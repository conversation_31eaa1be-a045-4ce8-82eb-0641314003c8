{"uid": "5a173ed6b092c53", "name": "测试turn off adaptive brightness能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_adaptive_brightness.TestEllaTurnOffAdaptiveBrightness#test_turn_off_adaptive_brightness", "historyId": "ebd2c9b6cd7c07b69e348328a207e18b", "time": {"start": 1755493992335, "stop": 1755494013150, "duration": 20815}, "description": "turn off adaptive brightness", "descriptionHtml": "<p>turn off adaptive brightness</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755493979878, "stop": 1755493992333, "duration": 12455}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755493992333, "stop": 1755493992333, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "turn off adaptive brightness", "status": "passed", "steps": [{"name": "执行命令: turn off adaptive brightness", "time": {"start": 1755493992335, "stop": 1755494012919, "duration": 20584}, "status": "passed", "steps": [{"name": "执行命令: turn off adaptive brightness", "time": {"start": 1755493992335, "stop": 1755494012689, "duration": 20354}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494012689, "stop": 1755494012918, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "609011ec386a86f5", "name": "测试总结", "source": "609011ec386a86f5.txt", "type": "text/plain", "size": 227}, {"uid": "57987d7da55a158f", "name": "test_completed", "source": "57987d7da55a158f.png", "type": "image/png", "size": 169554}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755494012919, "stop": 1755494012921, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证应用已打开", "time": {"start": 1755494012921, "stop": 1755494012921, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494012921, "stop": 1755494013149, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "9f6f8f415e1b2be5", "name": "测试总结", "source": "9f6f8f415e1b2be5.txt", "type": "text/plain", "size": 227}, {"uid": "ef3d4eae420011f", "name": "test_completed", "source": "ef3d4eae420011f.png", "type": "image/png", "size": 169588}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "75efa10a7a39d589", "name": "stdout", "source": "75efa10a7a39d589.txt", "type": "text/plain", "size": 11963}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 6, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755494013151, "stop": 1755494013151, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755494013155, "stop": 1755494014510, "duration": 1355}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_adaptive_brightness"}, {"name": "subSuite", "value": "TestEllaTurnOffAdaptiveBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_adaptive_brightness"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5a173ed6b092c53.json", "parameterValues": []}