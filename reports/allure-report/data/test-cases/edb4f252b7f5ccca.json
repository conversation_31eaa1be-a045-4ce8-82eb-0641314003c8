{"uid": "edb4f252b7f5ccca", "name": "测试turn on wifi能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_wifi.TestEllaTurnWifi#test_turn_on_wifi", "historyId": "d18ea588937139bb162adb1092a66013", "time": {"start": 1755494742747, "stop": 1755494763728, "duration": 20981}, "description": "turn on wifi", "descriptionHtml": "<p>turn on wifi</p>\n", "status": "failed", "statusMessage": "AssertionError: 初始=False, 最终=False, 响应='['turn on wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_turn_on_wifi.TestEllaTurnWifi object at 0x0000020BD2C8DE10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD4F36890>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_turn_on_wifi(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=False, 最终=False, 响应='['turn on wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_turn_on_wifi.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755494730021, "stop": 1755494742745, "duration": 12724}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755494742745, "stop": 1755494742745, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "turn on wifi", "status": "failed", "statusMessage": "AssertionError: 初始=False, 最终=False, 响应='['turn on wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_turn_on_wifi.TestEllaTurnWifi object at 0x0000020BD2C8DE10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD4F36890>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_turn_on_wifi(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=False, 最终=False, 响应='['turn on wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_turn_on_wifi.py:36: AssertionError", "steps": [{"name": "执行命令: turn on wifi", "time": {"start": 1755494742747, "stop": 1755494763723, "duration": 20976}, "status": "passed", "steps": [{"name": "执行命令: turn on wifi", "time": {"start": 1755494742747, "stop": 1755494763475, "duration": 20728}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494763475, "stop": 1755494763722, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "e1ec531528041412", "name": "测试总结", "source": "e1ec531528041412.txt", "type": "text/plain", "size": 176}, {"uid": "a709d8f9f3425ad0", "name": "test_completed", "source": "a709d8f9f3425ad0.png", "type": "image/png", "size": 172675}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755494763723, "stop": 1755494763726, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证应用已打开", "time": {"start": 1755494763726, "stop": 1755494763726, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: 初始=False, 最终=False, 响应='['turn on wifi', 'Wi-Fi is turned on now.', 'Wi-Fi', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_turn_on_wifi.py\", line 36, in test_turn_on_wifi\n    assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 0, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "db4f5c5116d445b3", "name": "stdout", "source": "db4f5c5116d445b3.txt", "type": "text/plain", "size": 12113}], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 3, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755494763735, "stop": 1755494763949, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "8efe68eb43f3352", "name": "失败截图-TestEllaTurnWifi", "source": "8efe68eb43f3352.png", "type": "image/png", "size": 173008}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755494763950, "stop": 1755494765361, "duration": 1411}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_wifi"}, {"name": "subSuite", "value": "TestEllaTurnWifi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_wifi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "edb4f252b7f5ccca.json", "parameterValues": []}