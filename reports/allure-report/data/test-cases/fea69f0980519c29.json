{"uid": "fea69f0980519c29", "name": "测试turn on airplane mode能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_airplane_mode.TestEllaTurnAirplaneMode#test_turn_on_airplane_mode", "historyId": "db1dca9aabe4b3e03e7003d17cf3fc9c", "time": {"start": 1755494240437, "stop": 1755494261161, "duration": 20724}, "description": "turn on airplane mode", "descriptionHtml": "<p>turn on airplane mode</p>\n", "status": "failed", "statusMessage": "AssertionError: 初始=None, 最终=None, 响应='['turn on airplane mode', 'Airplane Mode is turned on now.', 'Airplane Mode', '']'\nassert None", "statusTrace": "self = <testcases.test_ella.system_coupling.test_turn_on_airplane_mode.TestEllaTurnAirplaneMode object at 0x0000020BD2C415D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD4F9A210>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_turn_on_airplane_mode(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=None, 最终=None, 响应='['turn on airplane mode', 'Airplane Mode is turned on now.', 'Airplane Mode', '']'\nE           assert None\n\ntestcases\\test_ella\\system_coupling\\test_turn_on_airplane_mode.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755494227168, "stop": 1755494240435, "duration": 13267}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755494240435, "stop": 1755494240435, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "turn on airplane mode", "status": "failed", "statusMessage": "AssertionError: 初始=None, 最终=None, 响应='['turn on airplane mode', 'Airplane Mode is turned on now.', 'Airplane Mode', '']'\nassert None", "statusTrace": "self = <testcases.test_ella.system_coupling.test_turn_on_airplane_mode.TestEllaTurnAirplaneMode object at 0x0000020BD2C415D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD4F9A210>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_turn_on_airplane_mode(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=None, 最终=None, 响应='['turn on airplane mode', 'Airplane Mode is turned on now.', 'Airplane Mode', '']'\nE           assert None\n\ntestcases\\test_ella\\system_coupling\\test_turn_on_airplane_mode.py:36: AssertionError", "steps": [{"name": "执行命令: turn on airplane mode", "time": {"start": 1755494240437, "stop": 1755494261157, "duration": 20720}, "status": "passed", "steps": [{"name": "执行命令: turn on airplane mode", "time": {"start": 1755494240437, "stop": 1755494260925, "duration": 20488}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494260925, "stop": 1755494261156, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "37ad35903f1e55b8", "name": "测试总结", "source": "37ad35903f1e55b8.txt", "type": "text/plain", "size": 208}, {"uid": "7acde9e0d906fe0d", "name": "test_completed", "source": "7acde9e0d906fe0d.png", "type": "image/png", "size": 154449}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755494261157, "stop": 1755494261160, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证应用已打开", "time": {"start": 1755494261160, "stop": 1755494261160, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: 初始=None, 最终=None, 响应='['turn on airplane mode', 'Airplane Mode is turned on now.', 'Airplane Mode', '']'\nassert None\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_turn_on_airplane_mode.py\", line 36, in test_turn_on_airplane_mode\n    assert final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 0, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "b66950c726b676f3", "name": "stdout", "source": "b66950c726b676f3.txt", "type": "text/plain", "size": 12082}], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 3, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755494261167, "stop": 1755494261376, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "5eb41f5589ba70cd", "name": "失败截图-TestEllaTurnAirplaneMode", "source": "5eb41f5589ba70cd.png", "type": "image/png", "size": 154834}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755494261377, "stop": 1755494262755, "duration": 1378}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_airplane_mode"}, {"name": "subSuite", "value": "TestEllaTurnAirplaneMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_airplane_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "fea69f0980519c29.json", "parameterValues": []}