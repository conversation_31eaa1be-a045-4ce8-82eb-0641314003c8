{"uid": "e67a7663ced8295b", "name": "测试turn off flashlight能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_flashlight.TestEllaTurnOffFlashlight#test_turn_off_flashlight", "historyId": "c190fe929896ea57ed1e33f8bc5bf113", "time": {"start": 1755494062809, "stop": 1755494085699, "duration": 22890}, "description": "turn off flashlight", "descriptionHtml": "<p>turn off flashlight</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755494049780, "stop": 1755494062807, "duration": 13027}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755494062808, "stop": 1755494062808, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "turn off flashlight", "status": "passed", "steps": [{"name": "执行命令: turn off flashlight", "time": {"start": 1755494062810, "stop": 1755494085484, "duration": 22674}, "status": "passed", "steps": [{"name": "执行命令: turn off flashlight", "time": {"start": 1755494062810, "stop": 1755494085227, "duration": 22417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494085227, "stop": 1755494085484, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "900496378583cb", "name": "测试总结", "source": "900496378583cb.txt", "type": "text/plain", "size": 201}, {"uid": "28b08df28fa5eeb4", "name": "test_completed", "source": "28b08df28fa5eeb4.png", "type": "image/png", "size": 163632}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755494085484, "stop": 1755494085487, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证应用已打开", "time": {"start": 1755494085487, "stop": 1755494085487, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494085487, "stop": 1755494085698, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "b26cef302b8ab4df", "name": "测试总结", "source": "b26cef302b8ab4df.txt", "type": "text/plain", "size": 201}, {"uid": "dfd6f4ce081226e4", "name": "test_completed", "source": "dfd6f4ce081226e4.png", "type": "image/png", "size": 162798}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "bd0ea6eb62c51889", "name": "stdout", "source": "bd0ea6eb62c51889.txt", "type": "text/plain", "size": 12039}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 6, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755494085700, "stop": 1755494085700, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755494085704, "stop": 1755494087104, "duration": 1400}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_flashlight"}, {"name": "subSuite", "value": "TestEllaTurnOffFlashlight"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_flashlight"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e67a7663ced8295b.json", "parameterValues": []}