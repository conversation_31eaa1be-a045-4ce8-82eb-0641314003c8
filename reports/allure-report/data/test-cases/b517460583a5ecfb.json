{"uid": "b517460583a5ecfb", "name": "测试turn off smart reminder能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_smart_reminder.TestEllaTurnOffSmartReminder#test_turn_off_smart_reminder", "historyId": "e8f9ac327bc5f166a4c4a14509f365bf", "time": {"start": 1755494169748, "stop": 1755494190392, "duration": 20644}, "description": "turn off smart reminder", "descriptionHtml": "<p>turn off smart reminder</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755494156557, "stop": 1755494169746, "duration": 13189}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755494169746, "stop": 1755494169746, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "turn off smart reminder", "status": "passed", "steps": [{"name": "执行命令: turn off smart reminder", "time": {"start": 1755494169748, "stop": 1755494190156, "duration": 20408}, "status": "passed", "steps": [{"name": "执行命令: turn off smart reminder", "time": {"start": 1755494169748, "stop": 1755494189931, "duration": 20183}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494189931, "stop": 1755494190155, "duration": 224}, "status": "passed", "steps": [], "attachments": [{"uid": "491a15820b84b4e", "name": "测试总结", "source": "491a15820b84b4e.txt", "type": "text/plain", "size": 215}, {"uid": "39b882708e7b0c4b", "name": "test_completed", "source": "39b882708e7b0c4b.png", "type": "image/png", "size": 172753}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755494190156, "stop": 1755494190159, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证应用已打开", "time": {"start": 1755494190159, "stop": 1755494190159, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494190159, "stop": 1755494190391, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "7edeab4dfa46198a", "name": "测试总结", "source": "7edeab4dfa46198a.txt", "type": "text/plain", "size": 215}, {"uid": "161fcd5c3ea86ecd", "name": "test_completed", "source": "161fcd5c3ea86ecd.png", "type": "image/png", "size": 172466}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "798d1d9728e0364d", "name": "stdout", "source": "798d1d9728e0364d.txt", "type": "text/plain", "size": 11864}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 6, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755494190392, "stop": 1755494190392, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755494190396, "stop": 1755494191745, "duration": 1349}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_smart_reminder"}, {"name": "subSuite", "value": "TestEllaTurnOffSmartReminder"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_smart_reminder"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b517460583a5ecfb.json", "parameterValues": []}