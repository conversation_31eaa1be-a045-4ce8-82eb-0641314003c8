{"uid": "63058376ece98827", "name": "测试close wifi能正常执行", "fullName": "testcases.test_ella.system_coupling.test_close_wifi.TestEllaCloseWifi#test_close_wifi", "historyId": "0d9f0969c1336e077b7ada5963a2516a", "time": {"start": 1755491332233, "stop": 1755491332233, "duration": 0}, "description": "close wifi", "descriptionHtml": "<p>close wifi</p>\n", "status": "skipped", "statusMessage": "Skipped: 该脚本较特殊，先跳过", "statusTrace": "('D:\\\\aigc\\\\app_test\\\\testcases\\\\test_ella\\\\system_coupling\\\\test_close_wifi.py', 17, 'Skipped: 该脚本较特殊，先跳过')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755491345082, "stop": 1755491345082, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755491372428, "stop": 1755491372662, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "7921c103e4580250", "name": "失败截图-TestEllaCountdownMin", "source": "7921c103e4580250.png", "type": "image/png", "size": 180513}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "tag", "value": "@pytest.mark.skip(reason='该脚本较特殊，先跳过')"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_close_wifi"}, {"name": "subSuite", "value": "TestEllaCloseWifi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_close_wifi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["@pytest.mark.skip(reason='该脚本较特殊，先跳过')", "smoke"]}, "source": "63058376ece98827.json", "parameterValues": []}