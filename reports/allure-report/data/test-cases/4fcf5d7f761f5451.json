{"uid": "4fcf5d7f761f5451", "name": "测试how to say i love you in french能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french.TestEllaHowSayILoveYouFrench#test_how_to_say_i_love_you_in_french", "historyId": "78de5607a6208f59723ba6cf4fcf09c4", "time": {"start": 1755487781800, "stop": 1755487804342, "duration": 22542}, "description": "how to say i love you in french", "descriptionHtml": "<p>how to say i love you in french</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755487768071, "stop": 1755487781797, "duration": 13726}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755487781797, "stop": 1755487781797, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "how to say i love you in french", "status": "passed", "steps": [{"name": "执行命令: how to say i love you in french", "time": {"start": 1755487781800, "stop": 1755487804131, "duration": 22331}, "status": "passed", "steps": [{"name": "执行命令: how to say i love you in french", "time": {"start": 1755487781800, "stop": 1755487803893, "duration": 22093}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755487803893, "stop": 1755487804129, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "76b7564d99999c02", "name": "测试总结", "source": "76b7564d99999c02.txt", "type": "text/plain", "size": 188}, {"uid": "66890799ef982e0a", "name": "test_completed", "source": "66890799ef982e0a.png", "type": "image/png", "size": 164241}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755487804131, "stop": 1755487804136, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755487804136, "stop": 1755487804341, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "940388982b2958c5", "name": "测试总结", "source": "940388982b2958c5.txt", "type": "text/plain", "size": 188}, {"uid": "4133075f3beede8b", "name": "test_completed", "source": "4133075f3beede8b.png", "type": "image/png", "size": 163589}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "2d557af14c5e4b29", "name": "stdout", "source": "2d557af14c5e4b29.txt", "type": "text/plain", "size": 11476}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755487804344, "stop": 1755487804344, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755487804346, "stop": 1755487805763, "duration": 1417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_to_say_i_love_you_in_french"}, {"name": "subSuite", "value": "TestEllaHowSayILoveYouFrench"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4fcf5d7f761f5451.json", "parameterValues": []}