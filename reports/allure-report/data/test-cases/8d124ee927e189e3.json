{"uid": "8d124ee927e189e3", "name": "测试set my alarm volume to 50%", "fullName": "testcases.test_ella.system_coupling.test_set_my_alarm_volume_to.TestEllaOpenClock#test_set_my_alarm_volume_to", "historyId": "489e5c631d3a3ce20f77bce4c7c6632a", "time": {"start": 1755492690620, "stop": 1755492720206, "duration": 29586}, "description": "测试set my alarm volume to 50%指令", "descriptionHtml": "<p>测试set my alarm volume to 50%指令</p>\n", "status": "failed", "statusMessage": "AssertionError: clock: 初始=False, 最终=False, 响应='['set my alarm volume to 50%', 'Alarm volume has been set to 50.', '', '']'\nassert False == 7", "statusTrace": "self = <testcases.test_ella.system_coupling.test_set_my_alarm_volume_to.TestEllaOpenClock object at 0x0000020BD2AB2B90>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD4EE8710>\n\n    @allure.title(\"测试set my alarm volume to 50%\")\n    @allure.description(\"测试set my alarm volume to 50%指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_set_my_alarm_volume_to(self, ella_app):\n        \"\"\"测试set my alarm volume to 50%命令\"\"\"\n        command = \"set my alarm volume to 50%\"\n        expected_text = ['Alarm volume has been set to 50']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证clock已打开\"):\n>           assert final_status==7, f\"clock: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: clock: 初始=False, 最终=False, 响应='['set my alarm volume to 50%', 'Alarm volume has been set to 50.', '', '']'\nE           assert False == 7\n\ntestcases\\test_ella\\system_coupling\\test_set_my_alarm_volume_to.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755492677609, "stop": 1755492690618, "duration": 13009}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755492690618, "stop": 1755492690619, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "测试set my alarm volume to 50%指令", "status": "failed", "statusMessage": "AssertionError: clock: 初始=False, 最终=False, 响应='['set my alarm volume to 50%', 'Alarm volume has been set to 50.', '', '']'\nassert False == 7", "statusTrace": "self = <testcases.test_ella.system_coupling.test_set_my_alarm_volume_to.TestEllaOpenClock object at 0x0000020BD2AB2B90>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD4EE8710>\n\n    @allure.title(\"测试set my alarm volume to 50%\")\n    @allure.description(\"测试set my alarm volume to 50%指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_set_my_alarm_volume_to(self, ella_app):\n        \"\"\"测试set my alarm volume to 50%命令\"\"\"\n        command = \"set my alarm volume to 50%\"\n        expected_text = ['Alarm volume has been set to 50']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证clock已打开\"):\n>           assert final_status==7, f\"clock: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: clock: 初始=False, 最终=False, 响应='['set my alarm volume to 50%', 'Alarm volume has been set to 50.', '', '']'\nE           assert False == 7\n\ntestcases\\test_ella\\system_coupling\\test_set_my_alarm_volume_to.py:33: AssertionError", "steps": [{"name": "执行命令: set my alarm volume to 50%", "time": {"start": 1755492690620, "stop": 1755492720201, "duration": 29581}, "status": "passed", "steps": [{"name": "执行命令: set my alarm volume to 50%", "time": {"start": 1755492690620, "stop": 1755492719925, "duration": 29305}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755492719925, "stop": 1755492720201, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "e2da3361675587a4", "name": "测试总结", "source": "e2da3361675587a4.txt", "type": "text/plain", "size": 208}, {"uid": "2e05b21ab0ea8a01", "name": "test_completed", "source": "2e05b21ab0ea8a01.png", "type": "image/png", "size": 187237}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755492720201, "stop": 1755492720204, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证clock已打开", "time": {"start": 1755492720204, "stop": 1755492720204, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: clock: 初始=False, 最终=False, 响应='['set my alarm volume to 50%', 'Alarm volume has been set to 50.', '', '']'\nassert False == 7\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_set_my_alarm_volume_to.py\", line 33, in test_set_my_alarm_volume_to\n    assert final_status==7, f\"clock: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 0, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "3551dbb0b52db968", "name": "stdout", "source": "3551dbb0b52db968.txt", "type": "text/plain", "size": 12226}], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 3, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755492720213, "stop": 1755492720460, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "8a0ecbe300d36e25", "name": "失败截图-TestEllaOpenClock", "source": "8a0ecbe300d36e25.png", "type": "image/png", "size": 187810}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755492720462, "stop": 1755492721865, "duration": 1403}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_my_alarm_volume_to"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_my_alarm_volume_to"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "8d124ee927e189e3.json", "parameterValues": []}