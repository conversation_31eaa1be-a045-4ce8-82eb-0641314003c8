{"uid": "7972db2b2bb137e3", "name": "测试turn on bluetooth能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_bluetooth.TestEllaTurnBluetooth#test_turn_on_bluetooth", "historyId": "aff947fee562ec2636c3ce68a270b88d", "time": {"start": 1755494310879, "stop": 1755494330672, "duration": 19793}, "description": "turn on bluetooth", "descriptionHtml": "<p>turn on bluetooth</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755494297925, "stop": 1755494310877, "duration": 12952}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755494310877, "stop": 1755494310878, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "turn on bluetooth", "status": "passed", "steps": [{"name": "执行命令: turn on bluetooth", "time": {"start": 1755494310880, "stop": 1755494330424, "duration": 19544}, "status": "passed", "steps": [{"name": "执行命令: turn on bluetooth", "time": {"start": 1755494310880, "stop": 1755494330181, "duration": 19301}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494330181, "stop": 1755494330423, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "cf9adad9f077db54", "name": "测试总结", "source": "cf9adad9f077db54.txt", "type": "text/plain", "size": 193}, {"uid": "dedcbbd0f19bc833", "name": "test_completed", "source": "dedcbbd0f19bc833.png", "type": "image/png", "size": 176887}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755494330424, "stop": 1755494330426, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证应用已打开", "time": {"start": 1755494330426, "stop": 1755494330426, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494330426, "stop": 1755494330672, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "299386ef7c00dbfd", "name": "测试总结", "source": "299386ef7c00dbfd.txt", "type": "text/plain", "size": 193}, {"uid": "a864e8c2c14a534b", "name": "test_completed", "source": "a864e8c2c14a534b.png", "type": "image/png", "size": 176887}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "401b550a0b2d3566", "name": "stdout", "source": "401b550a0b2d3566.txt", "type": "text/plain", "size": 12655}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 6, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755494330673, "stop": 1755494330673, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755494330687, "stop": 1755494332035, "duration": 1348}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_bluetooth"}, {"name": "subSuite", "value": "TestEllaTurnBluetooth"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_bluetooth"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7972db2b2bb137e3.json", "parameterValues": []}