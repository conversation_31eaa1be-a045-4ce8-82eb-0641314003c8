{"uid": "4204334de6610ed", "name": "测试set split-screen apps返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps.TestEllaSetSplitScreenApps#test_set_split_screen_apps", "historyId": "b5e1711cce3102fc710ff74e18bf9129", "time": {"start": 1755502483424, "stop": 1755502503269, "duration": 19845}, "description": "验证set split-screen apps指令返回预期的不支持响应", "descriptionHtml": "<p>验证set split-screen apps指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755502470359, "stop": 1755502483422, "duration": 13063}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755502483423, "stop": 1755502483423, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "验证set split-screen apps指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set split-screen apps", "time": {"start": 1755502483424, "stop": 1755502503062, "duration": 19638}, "status": "passed", "steps": [{"name": "执行命令: set split-screen apps", "time": {"start": 1755502483424, "stop": 1755502502833, "duration": 19409}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755502502833, "stop": 1755502503061, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "2db768df91f2be0b", "name": "测试总结", "source": "2db768df91f2be0b.txt", "type": "text/plain", "size": 331}, {"uid": "fe91731c16ea332e", "name": "test_completed", "source": "fe91731c16ea332e.png", "type": "image/png", "size": 211776}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1755502503062, "stop": 1755502503065, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755502503065, "stop": 1755502503268, "duration": 203}, "status": "passed", "steps": [], "attachments": [{"uid": "4f179ad6282f2e9c", "name": "测试总结", "source": "4f179ad6282f2e9c.txt", "type": "text/plain", "size": 331}, {"uid": "396d78b8a4d05976", "name": "test_completed", "source": "396d78b8a4d05976.png", "type": "image/png", "size": 211776}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "d88d8c6b9ff1e237", "name": "stdout", "source": "d88d8c6b9ff1e237.txt", "type": "text/plain", "size": 11751}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755502503270, "stop": 1755502503270, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755502503272, "stop": 1755502504742, "duration": 1470}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_split_screen_apps"}, {"name": "subSuite", "value": "TestEllaSetSplitScreenApps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "4204334de6610ed.json", "parameterValues": []}