{"uid": "1907c176f4ae5832", "name": "测试open dialer能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_dialer.TestEllaCommandConcise#test_open_dialer", "historyId": "936ae2bf6db744b69d4acf28b22f7646", "time": {"start": 1755485928184, "stop": 1755485960809, "duration": 32625}, "description": "open dialer", "descriptionHtml": "<p>open dialer</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755485915133, "stop": 1755485928183, "duration": 13050}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755485928183, "stop": 1755485928183, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "open dialer", "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1755485928184, "stop": 1755485960561, "duration": 32377}, "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1755485928184, "stop": 1755485960306, "duration": 32122}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755485960306, "stop": 1755485960560, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "93dcccd64b56f257", "name": "测试总结", "source": "93dcccd64b56f257.txt", "type": "text/plain", "size": 211}, {"uid": "1dc7d2a380d75c5c", "name": "test_completed", "source": "1dc7d2a380d75c5c.png", "type": "image/png", "size": 187108}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含在期望中", "time": {"start": 1755485960561, "stop": 1755485960564, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755485960564, "stop": 1755485960808, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "2b9a22e0bd74af03", "name": "测试总结", "source": "2b9a22e0bd74af03.txt", "type": "text/plain", "size": 211}, {"uid": "381e70e43b8c4b5b", "name": "test_completed", "source": "381e70e43b8c4b5b.png", "type": "image/png", "size": 187584}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "c2fb6c6c8bbcdc9c", "name": "stdout", "source": "c2fb6c6c8bbcdc9c.txt", "type": "text/plain", "size": 14839}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755485960810, "stop": 1755485960810, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755485960813, "stop": 1755485962234, "duration": 1421}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_dialer"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_dialer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1907c176f4ae5832.json", "parameterValues": []}