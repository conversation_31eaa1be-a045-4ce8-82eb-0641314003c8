{"uid": "584b2a81c0df7d9", "name": "测试open contact命令", "fullName": "testcases.test_ella.component_coupling.test_open_phone.TestEllaContactCommandConcise#test_open_phone", "historyId": "acdb323f998d6127fbebbc545f6e8a59", "time": {"start": 1755486047168, "stop": 1755486079242, "duration": 32074}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "failed", "statusMessage": "AssertionError: Dalier应用未打开: 初始=True, 最终=None, 响应='['open phone', 'Done!', '', '', '[com.sh.smart.caller页面内容] 通话记录 | 联系人']'\nassert None", "statusTrace": "self = <testcases.test_ella.component_coupling.test_open_phone.TestEllaContactCommandConcise object at 0x0000020BD2352A50>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD3942810>\n\n    @allure.title(\"测试open contact命令\")\n    @allure.description(\"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_phone(self, ella_app):\n        \"\"\"测试open contact命令 - 简洁版本\"\"\"\n        command = \"open phone\"\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n        #\n        with allure.step(\"验证响应包含Done\"):\n            result = self.verify_expected_in_response(\"done\", response_text)\n            assert result, f\"响应文本应包含'Done'，实际响应: '{response_text}'\"\n    \n        with allure.step(\"验证Dalier应用已打开\"):\n>           assert final_status, f\"Dalier应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: Dalier应用未打开: 初始=True, 最终=None, 响应='['open phone', 'Done!', '', '', '[com.sh.smart.caller页面内容] 通话记录 | 联系人']'\nE           assert None\n\ntestcases\\test_ella\\component_coupling\\test_open_phone.py:35: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755486034300, "stop": 1755486047166, "duration": 12866}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755486047167, "stop": 1755486047167, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "failed", "statusMessage": "AssertionError: Dalier应用未打开: 初始=True, 最终=None, 响应='['open phone', 'Done!', '', '', '[com.sh.smart.caller页面内容] 通话记录 | 联系人']'\nassert None", "statusTrace": "self = <testcases.test_ella.component_coupling.test_open_phone.TestEllaContactCommandConcise object at 0x0000020BD2352A50>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD3942810>\n\n    @allure.title(\"测试open contact命令\")\n    @allure.description(\"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_phone(self, ella_app):\n        \"\"\"测试open contact命令 - 简洁版本\"\"\"\n        command = \"open phone\"\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n        #\n        with allure.step(\"验证响应包含Done\"):\n            result = self.verify_expected_in_response(\"done\", response_text)\n            assert result, f\"响应文本应包含'Done'，实际响应: '{response_text}'\"\n    \n        with allure.step(\"验证Dalier应用已打开\"):\n>           assert final_status, f\"Dalier应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: Dalier应用未打开: 初始=True, 最终=None, 响应='['open phone', 'Done!', '', '', '[com.sh.smart.caller页面内容] 通话记录 | 联系人']'\nE           assert None\n\ntestcases\\test_ella\\component_coupling\\test_open_phone.py:35: AssertionError", "steps": [{"name": "执行命令: open phone", "time": {"start": 1755486047169, "stop": 1755486079237, "duration": 32068}, "status": "passed", "steps": [{"name": "执行命令: open phone", "time": {"start": 1755486047169, "stop": 1755486078955, "duration": 31786}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755486078955, "stop": 1755486079236, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "f0ed74e48d39f4b1", "name": "测试总结", "source": "f0ed74e48d39f4b1.txt", "type": "text/plain", "size": 209}, {"uid": "63df7e92cf80fa42", "name": "test_completed", "source": "63df7e92cf80fa42.png", "type": "image/png", "size": 186166}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含Done", "time": {"start": 1755486079237, "stop": 1755486079240, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证Dalier应用已打开", "time": {"start": 1755486079240, "stop": 1755486079240, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: Dalier应用未打开: 初始=True, 最终=None, 响应='['open phone', 'Done!', '', '', '[com.sh.smart.caller页面内容] 通话记录 | 联系人']'\nassert None\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\component_coupling\\test_open_phone.py\", line 35, in test_open_phone\n    assert final_status, f\"Dalier应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 0, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "83d68d8415686bad", "name": "stdout", "source": "83d68d8415686bad.txt", "type": "text/plain", "size": 15093}], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 3, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755486079249, "stop": 1755486079487, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "a74afd0dc784f78f", "name": "失败截图-TestEllaContactCommandConcise", "source": "a74afd0dc784f78f.png", "type": "image/png", "size": 186087}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755486079489, "stop": 1755486080872, "duration": 1383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "story", "value": "联系人控制命令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_phone"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "584b2a81c0df7d9.json", "parameterValues": []}