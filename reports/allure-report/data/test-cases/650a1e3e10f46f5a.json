{"uid": "650a1e3e10f46f5a", "name": "stop  screen recording能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record.TestEllaTurnScreenRecord#test_stop_screen_recording", "historyId": "ebd9dc4871a5e78e68997fd53d4e9d06", "time": {"start": 1755494703631, "stop": 1755494728665, "duration": 25034}, "description": "stop  screen recording", "descriptionHtml": "<p>stop  screen recording</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755494690584, "stop": 1755494703630, "duration": 13046}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755494703630, "stop": 1755494703630, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "stop  screen recording", "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1755494703632, "stop": 1755494728432, "duration": 24800}, "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1755494703632, "stop": 1755494728219, "duration": 24587}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494728219, "stop": 1755494728431, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "7b2983e1de4fd3b9", "name": "测试总结", "source": "7b2983e1de4fd3b9.txt", "type": "text/plain", "size": 191}, {"uid": "a2c347a4fc5d822f", "name": "test_completed", "source": "a2c347a4fc5d822f.png", "type": "image/png", "size": 165019}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755494728432, "stop": 1755494728434, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证已打开", "time": {"start": 1755494728434, "stop": 1755494728434, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证文件存在", "time": {"start": 1755494728434, "stop": 1755494728434, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755494728434, "stop": 1755494728665, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "d55ff5032c28b5e1", "name": "测试总结", "source": "d55ff5032c28b5e1.txt", "type": "text/plain", "size": 191}, {"uid": "f944b9f21d4506b0", "name": "test_completed", "source": "f944b9f21d4506b0.png", "type": "image/png", "size": 165019}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "f64fff2e4803d099", "name": "stdout", "source": "f64fff2e4803d099.txt", "type": "text/plain", "size": 12930}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 7, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755494728666, "stop": 1755494728666, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755494728668, "stop": 1755494730011, "duration": 1343}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_screen_record"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "650a1e3e10f46f5a.json", "parameterValues": []}