{"uid": "6a3c95959cdeda8f", "name": "测试set ringtone volume to 50能正常执行", "fullName": "testcases.test_ella.system_coupling.test_set_ringtone_volume_to.TestEllaSetRingtoneVolume#test_set_ringtone_volume_to", "historyId": "d9e62135fb3d98a8cadd206c651db3d7", "time": {"start": 1755492769756, "stop": 1755492790590, "duration": 20834}, "description": "set ringtone volume to 50", "descriptionHtml": "<p>set ringtone volume to 50</p>\n", "status": "failed", "statusMessage": "AssertionError: 初始=None, 最终=None, 响应='['set ringtone volume to 50', 'Ringtone volume has been set to 50.', '', '']'\nassert None == 7", "statusTrace": "self = <testcases.test_ella.system_coupling.test_set_ringtone_volume_to.TestEllaSetRingtoneVolume object at 0x0000020BD2AC7250>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD6246910>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_set_ringtone_volume_to(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert final_status==7, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=None, 最终=None, 响应='['set ringtone volume to 50', 'Ringtone volume has been set to 50.', '', '']'\nE           assert None == 7\n\ntestcases\\test_ella\\system_coupling\\test_set_ringtone_volume_to.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755492756662, "stop": 1755492769754, "duration": 13092}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755492769754, "stop": 1755492769754, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "set ringtone volume to 50", "status": "failed", "statusMessage": "AssertionError: 初始=None, 最终=None, 响应='['set ringtone volume to 50', 'Ringtone volume has been set to 50.', '', '']'\nassert None == 7", "statusTrace": "self = <testcases.test_ella.system_coupling.test_set_ringtone_volume_to.TestEllaSetRingtoneVolume object at 0x0000020BD2AC7250>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000020BD6246910>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_set_ringtone_volume_to(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert final_status==7, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=None, 最终=None, 响应='['set ringtone volume to 50', 'Ringtone volume has been set to 50.', '', '']'\nE           assert None == 7\n\ntestcases\\test_ella\\system_coupling\\test_set_ringtone_volume_to.py:36: AssertionError", "steps": [{"name": "执行命令: set ringtone volume to 50", "time": {"start": 1755492769756, "stop": 1755492790585, "duration": 20829}, "status": "passed", "steps": [{"name": "执行命令: set ringtone volume to 50", "time": {"start": 1755492769756, "stop": 1755492790345, "duration": 20589}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755492790345, "stop": 1755492790583, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "975d9c2723b1b8a3", "name": "测试总结", "source": "975d9c2723b1b8a3.txt", "type": "text/plain", "size": 207}, {"uid": "2fe8e0ba07c284d3", "name": "test_completed", "source": "2fe8e0ba07c284d3.png", "type": "image/png", "size": 190871}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755492790585, "stop": 1755492790587, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证应用已打开", "time": {"start": 1755492790588, "stop": 1755492790588, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: 初始=None, 最终=None, 响应='['set ringtone volume to 50', 'Ringtone volume has been set to 50.', '', '']'\nassert None == 7\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_set_ringtone_volume_to.py\", line 36, in test_set_ringtone_volume_to\n    assert final_status==7, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 0, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "a687efda818388bf", "name": "stdout", "source": "a687efda818388bf.txt", "type": "text/plain", "size": 11850}], "parameters": [], "shouldDisplayMessage": true, "attachmentsCount": 3, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755492790595, "stop": 1755492790844, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "2c40bed7e6e2a002", "name": "失败截图-TestEllaSetRingtoneVolume", "source": "2c40bed7e6e2a002.png", "type": "image/png", "size": 190864}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755492790846, "stop": 1755492792188, "duration": 1342}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_ringtone_volume_to"}, {"name": "subSuite", "value": "TestEllaSetRingtoneVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_ringtone_volume_to"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "6a3c95959cdeda8f.json", "parameterValues": []}