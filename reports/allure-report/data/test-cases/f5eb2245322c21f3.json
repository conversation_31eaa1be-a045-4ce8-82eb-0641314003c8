{"uid": "f5eb2245322c21f3", "name": "测试set color style返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_color_style.TestEllaSetColorStyle#test_set_color_style", "historyId": "e2beda2a0bda4155b33d47f14bdcb9ed", "time": {"start": 1755501398160, "stop": 1755501418314, "duration": 20154}, "description": "验证set color style指令返回预期的不支持响应", "descriptionHtml": "<p>验证set color style指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755501385199, "stop": 1755501398158, "duration": 12959}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755501398158, "stop": 1755501398158, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "验证set color style指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set color style", "time": {"start": 1755501398160, "stop": 1755501418069, "duration": 19909}, "status": "passed", "steps": [{"name": "执行命令: set color style", "time": {"start": 1755501398160, "stop": 1755501417808, "duration": 19648}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755501417808, "stop": 1755501418067, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "4d059769985c5a6", "name": "测试总结", "source": "4d059769985c5a6.txt", "type": "text/plain", "size": 313}, {"uid": "5afd7a9a6098217e", "name": "test_completed", "source": "5afd7a9a6098217e.png", "type": "image/png", "size": 211740}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1755501418069, "stop": 1755501418072, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755501418072, "stop": 1755501418313, "duration": 241}, "status": "passed", "steps": [], "attachments": [{"uid": "3c4d52713378f723", "name": "测试总结", "source": "3c4d52713378f723.txt", "type": "text/plain", "size": 313}, {"uid": "ecc1a7457a30609a", "name": "test_completed", "source": "ecc1a7457a30609a.png", "type": "image/png", "size": 211758}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "37e598af2c6ce39a", "name": "stdout", "source": "37e598af2c6ce39a.txt", "type": "text/plain", "size": 11669}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755501418315, "stop": 1755501418316, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755501418319, "stop": 1755501419787, "duration": 1468}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_color_style"}, {"name": "subSuite", "value": "TestEllaSetColorStyle"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_color_style"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f5eb2245322c21f3.json", "parameterValues": []}