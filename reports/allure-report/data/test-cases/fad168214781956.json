{"uid": "fad168214781956", "name": "测试set date & time返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_date_time.TestEllaSetDateTime#test_set_date_time", "historyId": "92e2909ea81e82011e43342b4fc06c3b", "time": {"start": 1755501536289, "stop": 1755501556322, "duration": 20033}, "description": "验证set date & time指令返回预期的不支持响应", "descriptionHtml": "<p>验证set date &amp; time指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755501523591, "stop": 1755501536287, "duration": 12696}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755501536287, "stop": 1755501536287, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "验证set date & time指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set date & time", "time": {"start": 1755501536289, "stop": 1755501556074, "duration": 19785}, "status": "passed", "steps": [{"name": "执行命令: set date & time", "time": {"start": 1755501536289, "stop": 1755501555808, "duration": 19519}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755501555808, "stop": 1755501556074, "duration": 266}, "status": "passed", "steps": [], "attachments": [{"uid": "fad4a08754aa7c8b", "name": "测试总结", "source": "fad4a08754aa7c8b.txt", "type": "text/plain", "size": 313}, {"uid": "2852fdda70478a51", "name": "test_completed", "source": "2852fdda70478a51.png", "type": "image/png", "size": 219939}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1755501556074, "stop": 1755501556079, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755501556079, "stop": 1755501556321, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "7de38f2104cbb6fb", "name": "测试总结", "source": "7de38f2104cbb6fb.txt", "type": "text/plain", "size": 313}, {"uid": "cc85c2601fe8d743", "name": "test_completed", "source": "cc85c2601fe8d743.png", "type": "image/png", "size": 219949}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "7d915e41321fc25b", "name": "stdout", "source": "7d915e41321fc25b.txt", "type": "text/plain", "size": 11665}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755501556323, "stop": 1755501556323, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755501556339, "stop": 1755501557753, "duration": 1414}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_date_time"}, {"name": "subSuite", "value": "TestEllaSetDateTime"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_date_time"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fad168214781956.json", "parameterValues": []}