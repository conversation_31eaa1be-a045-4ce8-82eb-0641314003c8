{"uid": "6db26062c8375bcf", "name": "测试phone boost能正常执行", "fullName": "testcases.test_ella.component_coupling.test_phone_boost.TestEllaPhoneBoost#test_phone_boost", "historyId": "762b1ab748e39965c1484eb7fe38bfe4", "time": {"start": 1755486201398, "stop": 1755486222360, "duration": 20962}, "description": "phone boost", "descriptionHtml": "<p>phone boost</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755486187904, "stop": 1755486201396, "duration": 13492}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755486201397, "stop": 1755486201397, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "phone boost", "status": "passed", "steps": [{"name": "执行命令: phone boost", "time": {"start": 1755486201398, "stop": 1755486222093, "duration": 20695}, "status": "passed", "steps": [{"name": "执行命令: phone boost", "time": {"start": 1755486201398, "stop": 1755486221837, "duration": 20439}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755486221837, "stop": 1755486222093, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "d93a1fbf6e17acfa", "name": "测试总结", "source": "d93a1fbf6e17acfa.txt", "type": "text/plain", "size": 149}, {"uid": "d1cc90edb5df1bd2", "name": "test_completed", "source": "d1cc90edb5df1bd2.png", "type": "image/png", "size": 180071}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755486222093, "stop": 1755486222095, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755486222095, "stop": 1755486222358, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "1ddb33a9102dc15b", "name": "测试总结", "source": "1ddb33a9102dc15b.txt", "type": "text/plain", "size": 149}, {"uid": "537025665e853fff", "name": "test_completed", "source": "537025665e853fff.png", "type": "image/png", "size": 180468}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "b48df899831083a6", "name": "stdout", "source": "b48df899831083a6.txt", "type": "text/plain", "size": 11262}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755486222361, "stop": 1755486222361, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755486222363, "stop": 1755486223726, "duration": 1363}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_phone_boost"}, {"name": "subSuite", "value": "TestEllaPhoneBoost"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_phone_boost"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6db26062c8375bcf.json", "parameterValues": []}