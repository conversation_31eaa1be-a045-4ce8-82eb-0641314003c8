{"uid": "289f152ae316d34e", "name": "测试set phone number返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_phone_number.TestEllaSetPhoneNumber#test_set_phone_number", "historyId": "8e367b8da758818b9a0fe21deca7ec48", "time": {"start": 1755502137097, "stop": 1755502156982, "duration": 19885}, "description": "验证set phone number指令返回预期的不支持响应", "descriptionHtml": "<p>验证set phone number指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755502124324, "stop": 1755502137096, "duration": 12772}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755502137096, "stop": 1755502137096, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "验证set phone number指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set phone number", "time": {"start": 1755502137097, "stop": 1755502156734, "duration": 19637}, "status": "passed", "steps": [{"name": "执行命令: set phone number", "time": {"start": 1755502137097, "stop": 1755502156501, "duration": 19404}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755502156501, "stop": 1755502156733, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "b6e899da4fb013a2", "name": "测试总结", "source": "b6e899da4fb013a2.txt", "type": "text/plain", "size": 321}, {"uid": "4642029b0f4ef9ab", "name": "test_completed", "source": "4642029b0f4ef9ab.png", "type": "image/png", "size": 215948}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1755502156734, "stop": 1755502156738, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755502156738, "stop": 1755502156980, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "b8e06ac42bb1093a", "name": "测试总结", "source": "b8e06ac42bb1093a.txt", "type": "text/plain", "size": 321}, {"uid": "af567bd7217e7334", "name": "test_completed", "source": "af567bd7217e7334.png", "type": "image/png", "size": 215948}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "e6dc97b0d8899579", "name": "stdout", "source": "e6dc97b0d8899579.txt", "type": "text/plain", "size": 11698}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755502156983, "stop": 1755502156983, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755502156985, "stop": 1755502158501, "duration": 1516}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_phone_number"}, {"name": "subSuite", "value": "TestEllaSetPhoneNumber"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_phone_number"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "289f152ae316d34e.json", "parameterValues": []}