{"uid": "7c21262394c1c3b2", "name": "测试open camera能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_camera.TestEllaCommandConcise#test_open_camera", "historyId": "ae0ee984c3712fd05ea04b52289e14fe", "time": {"start": 1755485747784, "stop": 1755485777990, "duration": 30206}, "description": "open camera", "descriptionHtml": "<p>open camera</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755485301554, "stop": 1755485301556, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755485734776, "stop": 1755485747781, "duration": 13005}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755485747781, "stop": 1755485747782, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "open camera", "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1755485747784, "stop": 1755485777757, "duration": 29973}, "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1755485747784, "stop": 1755485777485, "duration": 29701}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755485777485, "stop": 1755485777756, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "ddc291df47b9ba7a", "name": "测试总结", "source": "ddc291df47b9ba7a.txt", "type": "text/plain", "size": 255}, {"uid": "a6f5c7caa8e41763", "name": "test_completed", "source": "a6f5c7caa8e41763.png", "type": "image/png", "size": 177798}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含在期望中", "time": {"start": 1755485777757, "stop": 1755485777759, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "验证已打开", "time": {"start": 1755485777759, "stop": 1755485777759, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755485777759, "stop": 1755485777989, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "5cd474bf7ddc6242", "name": "测试总结", "source": "5cd474bf7ddc6242.txt", "type": "text/plain", "size": 255}, {"uid": "f2b52e6ead013546", "name": "test_completed", "source": "f2b52e6ead013546.png", "type": "image/png", "size": 177964}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "82055402f2fb165b", "name": "stdout", "source": "82055402f2fb165b.txt", "type": "text/plain", "size": 14418}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "hasContent": true, "stepsCount": 6, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755485777991, "stop": 1755485777991, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755485777992, "stop": 1755485779403, "duration": 1411}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755503966032, "stop": 1755503966035, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "27036-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_camera"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7c21262394c1c3b2.json", "parameterValues": []}