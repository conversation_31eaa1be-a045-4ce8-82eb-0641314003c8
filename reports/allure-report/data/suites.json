{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "testcases.test_ella.component_coupling", "children": [{"name": "test_close_aivana", "children": [{"name": "TestEllaCloseAivana", "children": [{"name": "测试close aivana能正常执行", "uid": "47ae2654f354a092", "parentUid": "dc766e0abf993d1226d15b68c77f4f69", "status": "passed", "time": {"start": 1755485314449, "stop": 1755485348812, "duration": 34363}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dc766e0abf993d1226d15b68c77f4f69"}], "uid": "2326186ebd37334731865ec8b5679891"}, {"name": "test_close_ella", "children": [{"name": "TestEllaClose<PERSON>lla", "children": [{"name": "测试close ella能正常执行", "uid": "9af44badf1488f71", "parentUid": "cec0309765c155f4f8740ed45c580c80", "status": "passed", "time": {"start": 1755485363283, "stop": 1755485397730, "duration": 34447}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cec0309765c155f4f8740ed45c580c80"}], "uid": "989d936277ca7c8b51dc874f7afac7c1"}, {"name": "test_close_folax", "children": [{"name": "TestEllaCloseFolax", "children": [{"name": "测试close folax能正常执行", "uid": "7197877879ecb7dd", "parentUid": "7ae1e31d59d51e63408ae29d2c8c926e", "status": "passed", "time": {"start": 1755485412162, "stop": 1755485446416, "duration": 34254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7ae1e31d59d51e63408ae29d2c8c926e"}], "uid": "d7b55da797c782b6c2cc729406440a10"}, {"name": "test_close_phonemaster", "children": [{"name": "TestEllaClosePhonemaster", "children": [{"name": "测试close phonemaster能正常执行", "uid": "9006abb6710c0f91", "parentUid": "976102e81bb88d3807a1e25c3ddeca37", "status": "passed", "time": {"start": 1755485460649, "stop": 1755485480746, "duration": 20097}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "976102e81bb88d3807a1e25c3ddeca37"}], "uid": "e1a42dca75ede54847ef42fc018b38d7"}, {"name": "test_continue_music", "children": [{"name": "TestEllaContinueMusic", "children": [{"name": "测试continue music能正常执行", "uid": "1db1a12e38c8a7be", "parentUid": "1006aa648a1d87005804dfc1cb937d40", "status": "passed", "time": {"start": 1755485494854, "stop": 1755485514733, "duration": 19879}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1006aa648a1d87005804dfc1cb937d40"}], "uid": "091d4456268247868ef8ba9cc951a6e9"}, {"name": "test_create_a_metting_schedule_at_tomorrow", "children": [{"name": "TestEllaCreateMettingScheduleTomorrow", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "3775db10c32d8b4d", "parentUid": "2723183c861afd907653f50d6bfc622c", "status": "failed", "time": {"start": 1755485529159, "stop": 1755485555322, "duration": 26163}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2723183c861afd907653f50d6bfc622c"}], "uid": "e4b3f0da2dd18af340a32e173acf4400"}, {"name": "test_delete_the_8_o_clock_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "cdc954c986749540", "parentUid": "75d733f64143d69107fc406cc5a4bb5d", "status": "passed", "time": {"start": 1755485569625, "stop": 1755485589782, "duration": 20157}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "75d733f64143d69107fc406cc5a4bb5d"}], "uid": "5ac49ae4fb77ab653eda0b303f9fd551"}, {"name": "test_disable_call_on_hold", "children": [{"name": "TestEllaDisableCallHold", "children": [{"name": "测试disable call on hold返回正确的不支持响应", "uid": "64622074bee7fe1f", "parentUid": "da1ee3b782a3ae934466a10ab45d1e54", "status": "failed", "time": {"start": 1755485604078, "stop": 1755485629027, "duration": 24949}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "da1ee3b782a3ae934466a10ab45d1e54"}], "uid": "8b37e9595815ca84fbf6e36410b63b3a"}, {"name": "test_display_the_route_go_company", "children": [{"name": "TestEllaOpenMaps", "children": [{"name": "测试display the route go company", "uid": "ad34dd261ee40182", "parentUid": "fae47b3031d4ccce961b95f3d652edcf", "status": "failed", "time": {"start": 1755485643465, "stop": 1755485664990, "duration": 21525}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fae47b3031d4ccce961b95f3d652edcf"}], "uid": "a2b2fbfd344f33b51bd68e5977e57dae"}, {"name": "test_my_phone_is_too_slow", "children": [{"name": "TestEllaMyPhoneIsTooSlow", "children": [{"name": "测试my phone is too slow能正常执行", "uid": "1d40808d468eab6e", "parentUid": "a0a6853a187713f8a5ac0e869d619853", "status": "passed", "time": {"start": 1755485679396, "stop": 1755485699552, "duration": 20156}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0a6853a187713f8a5ac0e869d619853"}], "uid": "1a3ac4d4348e23076891e9f62ee93107"}, {"name": "test_next_channel", "children": [{"name": "TestEllaNextChannel", "children": [{"name": "测试next channel能正常执行", "uid": "e0eaa952e2440198", "parentUid": "79fe0adb48b7a7e540c509bbc071945d", "status": "passed", "time": {"start": 1755485713876, "stop": 1755485733399, "duration": 19523}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79fe0adb48b7a7e540c509bbc071945d"}], "uid": "bf525e696763b8e3f2fb093dc5260aa2"}, {"name": "test_open_camera", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open camera能正常执行", "uid": "7c21262394c1c3b2", "parentUid": "e03396d1fe1c4deef0c8cf91f821a468", "status": "passed", "time": {"start": 1755485747784, "stop": 1755485777990, "duration": 30206}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e03396d1fe1c4deef0c8cf91f821a468"}], "uid": "92e88ab08159dfaf49a78312925760b7"}, {"name": "test_open_clock", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "open clock", "uid": "9961206a47431e4b", "parentUid": "b2210d96f4ac8230585a75115cd1d8c2", "status": "passed", "time": {"start": 1755485794635, "stop": 1755485827601, "duration": 32966}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2210d96f4ac8230585a75115cd1d8c2"}], "uid": "d636d9848e1c7419e6f84e7e49c07171"}, {"name": "test_open_contact", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "3ff715dddf18a033", "parentUid": "39d05c6acdceec56c3c12fd23896f56f", "status": "failed", "time": {"start": 1755485842173, "stop": 1755485877670, "duration": 35497}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "39d05c6acdceec56c3c12fd23896f56f"}], "uid": "cd82497cee692a7c9a65a6184e27c221"}, {"name": "test_open_countdown", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open countdown能正常执行", "uid": "cdf5fdcbd094781a", "parentUid": "e10c6770504fedd71bbf3e383f94dc48", "status": "failed", "time": {"start": 1755485892416, "stop": 1755485913499, "duration": 21083}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e10c6770504fedd71bbf3e383f94dc48"}], "uid": "f0d78da692838ddb314b12cc4febc20b"}, {"name": "test_open_dialer", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open dialer能正常执行", "uid": "1907c176f4ae5832", "parentUid": "068eb91b79bee6accc0bcab4a6367e76", "status": "passed", "time": {"start": 1755485928184, "stop": 1755485960809, "duration": 32625}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "068eb91b79bee6accc0bcab4a6367e76"}], "uid": "3c3dd50d3fd335b243796d4c0c9bfe64"}, {"name": "test_open_ella", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "b56253c07e5ce123", "parentUid": "907f87c6914ea345d20478681f484e8d", "status": "passed", "time": {"start": 1755485975177, "stop": 1755485996442, "duration": 21265}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "907f87c6914ea345d20478681f484e8d"}], "uid": "6b30479a0405c27782687611167a8e3d"}, {"name": "test_open_folax", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open folax能正常执行", "uid": "fa879c77558497aa", "parentUid": "031c2cd337cd7647fd7b60d0d75ba16e", "status": "passed", "time": {"start": 1755486010922, "stop": 1755486032810, "duration": 21888}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "031c2cd337cd7647fd7b60d0d75ba16e"}], "uid": "fb1e65e2e60ee7c63b5831633cb5005c"}, {"name": "test_open_phone", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "584b2a81c0df7d9", "parentUid": "147ffaef9b3cdea572ba5e4caea69dbe", "status": "failed", "time": {"start": 1755486047168, "stop": 1755486079242, "duration": 32074}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "147ffaef9b3cdea572ba5e4caea69dbe"}], "uid": "75ded0b626adc3c7c8dbefcba1c0b5f0"}, {"name": "test_pause_fm", "children": [{"name": "TestEllaPauseFm", "children": [{"name": "测试pause fm能正常执行", "uid": "cd06fc08aaa2d6c8", "parentUid": "88ca4f005094f55c66457c720f6cbb47", "status": "passed", "time": {"start": 1755486093731, "stop": 1755486115097, "duration": 21366}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "88ca4f005094f55c66457c720f6cbb47"}], "uid": "d9c43a396815c7e853000c20e47b8fa8"}, {"name": "test_pause_music", "children": [{"name": "TestEllaPauseMusic", "children": [{"name": "测试pause music能正常执行", "uid": "cb998d564c571bbb", "parentUid": "1f79feb59e2eb0bf0fb7e1387903bfee", "status": "passed", "time": {"start": 1755486129429, "stop": 1755486150805, "duration": 21376}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1f79feb59e2eb0bf0fb7e1387903bfee"}], "uid": "8a3b16d13b6ccc8492638853c9ba04aa"}, {"name": "test_pause_song", "children": [{"name": "TestEllaPauseSong", "children": [{"name": "测试pause song能正常执行", "uid": "17d3915d67aefacf", "parentUid": "a3c6e1d0398474d5bb90c22ff0ad7930", "status": "failed", "time": {"start": 1755486165222, "stop": 1755486186256, "duration": 21034}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3c6e1d0398474d5bb90c22ff0ad7930"}], "uid": "6c009013e1160a80039c4a7e2e07d104"}, {"name": "test_phone_boost", "children": [{"name": "TestEllaPhoneBoost", "children": [{"name": "测试phone boost能正常执行", "uid": "6db26062c8375bcf", "parentUid": "64e4d2ec8ce2a83bbdd5f37990e1b292", "status": "passed", "time": {"start": 1755486201398, "stop": 1755486222360, "duration": 20962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "64e4d2ec8ce2a83bbdd5f37990e1b292"}], "uid": "b327c70b1579c29f98ae28c9e3ffe844"}, {"name": "test_play_afro_strut", "children": [{"name": "TestEllaOpenPlayAfroStrut", "children": [{"name": "测试play afro strut", "uid": "9efd7f5987a69bbb", "parentUid": "9988b4042f5d7026e75fc8fcb98f5519", "status": "passed", "time": {"start": 1755486236762, "stop": 1755486277722, "duration": 40960}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9988b4042f5d7026e75fc8fcb98f5519"}], "uid": "3aef4f1ab476b7c11ee7ec75e072c264"}, {"name": "test_play_jay_chou_s_music", "children": [{"name": "TestEllaOpenMusic", "children": [{"name": "测试play jay chou's music", "uid": "acb7103c5318ed9", "parentUid": "a2f6d87f760b2cc31c5c6aad0f136dd1", "status": "passed", "time": {"start": 1755486292615, "stop": 1755486331803, "duration": 39188}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a2f6d87f760b2cc31c5c6aad0f136dd1"}], "uid": "5ac2a1940b596719fc1fe3160a450962"}, {"name": "test_play_jay_chou_s_music_by_spotify", "children": [{"name": "TestEllaOpenMusic", "children": [{"name": "测试play jay chou's music by spotify", "uid": "e1fc245a4c62b942", "parentUid": "c8aebf1f28528d18214bd1a9e19bfca7", "status": "passed", "time": {"start": 1755486346554, "stop": 1755486369835, "duration": 23281}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c8aebf1f28528d18214bd1a9e19bfca7"}], "uid": "4c3a9736ba488ccfdfd13dbb9608ebde"}, {"name": "test_play_music", "children": [{"name": "TestEllaOpen<PERSON>a", "children": [{"name": "测试play music", "uid": "a2b459cf2f08ab6c", "parentUid": "de473794f9027034dfc0e767cb0a1fc8", "status": "passed", "time": {"start": 1755486384494, "stop": 1755486422610, "duration": 38116}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de473794f9027034dfc0e767cb0a1fc8"}], "uid": "755e0b08ce2f0d843df6c102fcc9d49b"}, {"name": "test_play_rock_music", "children": [{"name": "TestEllaOpen<PERSON>a", "children": [{"name": "测试play rock music", "uid": "1347de287338b45d", "parentUid": "b0f90c45c8c8e64fb5c2a98f923bc6fa", "status": "passed", "time": {"start": 1755486437646, "stop": 1755486475589, "duration": 37943}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0f90c45c8c8e64fb5c2a98f923bc6fa"}], "uid": "735fc1adab6496548b5a97d70b865131"}, {"name": "test_play_sun_be_song_of_jide_chord", "children": [{"name": "TestEllaOpenPlaySunBeSongOfJideChord", "children": [{"name": "测试play sun be song of jide chord", "uid": "be1705873140635c", "parentUid": "5c987d2d0d756e70bc56eda09e92e854", "status": "passed", "time": {"start": 1755486490554, "stop": 1755486529925, "duration": 39371}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5c987d2d0d756e70bc56eda09e92e854"}], "uid": "020b88bee0045afa929d84639829645d"}, {"name": "test_previous_music", "children": [{"name": "TestEllaPreviousMusic", "children": [{"name": "测试previous music能正常执行", "uid": "8b09801babb8c73c", "parentUid": "da7caa7ac31745b37f41cb283803912e", "status": "failed", "time": {"start": 1755486544815, "stop": 1755486565826, "duration": 21011}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "da7caa7ac31745b37f41cb283803912e"}], "uid": "bc6fa07388e696170fffb17281da4c7f"}, {"name": "test_record_audio_for_seconds", "children": [{"name": "TestEllaRecordAudioSeconds", "children": [{"name": "测试record audio for 5 seconds能正常执行", "uid": "8c4d1ad56423ecf1", "parentUid": "964e536f897371e5579180ce760d9610", "status": "failed", "time": {"start": 1755486580698, "stop": 1755486602875, "duration": 22177}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "964e536f897371e5579180ce760d9610"}], "uid": "b622f700f99c575c72cb1944c3e3c02e"}, {"name": "test_resume_music", "children": [{"name": "TestEllaResumeMusic", "children": [{"name": "测试resume music能正常执行", "uid": "376b4abb06a154cb", "parentUid": "cd0e4afc91d2a02372d847be235bede4", "status": "passed", "time": {"start": 1755486618179, "stop": 1755486639288, "duration": 21109}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd0e4afc91d2a02372d847be235bede4"}], "uid": "b281dddb4da708a18c1bdad0e1402ec1"}, {"name": "test_set_an_alarm_at_8_am", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set an alarm at 8 am", "uid": "6f28d5f0698b5193", "parentUid": "33bfbdda545b50afa47fc22456e93557", "status": "failed", "time": {"start": 1755486654099, "stop": 1755486684975, "duration": 30876}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33bfbdda545b50afa47fc22456e93557"}], "uid": "a3607188923688aad3a2ecec54f9a7c1"}, {"name": "test_stop_playing", "children": [{"name": "TestEllaOpenYoutube", "children": [{"name": "测试stop playing", "uid": "cbacf406046ab12b", "parentUid": "04a0cd56790b683054ed72365f59d2e7", "status": "passed", "time": {"start": 1755486700223, "stop": 1755486722902, "duration": 22679}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04a0cd56790b683054ed72365f59d2e7"}], "uid": "8960b75623e553fca190959e7f507f14"}, {"name": "test_take_a_screenshot", "children": [{"name": "TestEllaTakeScreenshot", "children": [{"name": "测试take a screenshot能正常执行", "uid": "39cda43f623f1b61", "parentUid": "ac5545075b0e4292eed508b41e08ab8c", "status": "failed", "time": {"start": 1755486737335, "stop": 1755486761234, "duration": 23899}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac5545075b0e4292eed508b41e08ab8c"}], "uid": "0daa64e90efed9ce03db463cca6bb5b1"}, {"name": "test_turn_off_the_7_am_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn off the 7AM alarm", "uid": "45cf25b25529ab14", "parentUid": "7badc55fb60a0a664ac693ed5106cf16", "status": "passed", "time": {"start": 1755486776260, "stop": 1755486804710, "duration": 28450}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7badc55fb60a0a664ac693ed5106cf16"}], "uid": "ca54947974933cdcc156785ae159284e"}, {"name": "test_turn_off_the_8_am_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn off the 8 am alarm", "uid": "6fdc15bc54a2fd9a", "parentUid": "b239508423aac2a95c8e0b7b6e63f430", "status": "passed", "time": {"start": 1755486819466, "stop": 1755486851150, "duration": 31684}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b239508423aac2a95c8e0b7b6e63f430"}], "uid": "a4642e1a3f32fdb6af62d6449783bb94"}, {"name": "test_turn_on_the_alarm_at_8_am", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn on the alarm at 8 am", "uid": "895da84e7cca2494", "parentUid": "d228b7163521a3d21a5179f5d0599453", "status": "passed", "time": {"start": 1755486865908, "stop": 1755486896025, "duration": 30117}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d228b7163521a3d21a5179f5d0599453"}], "uid": "836d9647d7fb73692aca73e413ef3024"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "1efae155b7e6807e", "parentUid": "de146ab7aca490ae6653437938175de4", "status": "passed", "time": {"start": 1755486910782, "stop": 1755486938129, "duration": 27347}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de146ab7aca490ae6653437938175de4"}], "uid": "4a586ea49df80d8628d524f64c639079"}], "uid": "5948c7c27387d214d4b5e1b876d4cb27"}, {"name": "testcases.test_ella.dialogue", "children": [{"name": "test_appeler_maman", "children": [{"name": "Test<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试appeler maman能正常执行", "uid": "baae95d2ae9dceda", "parentUid": "f8fcd4a1c0a83d1c3db334295a60b72e", "status": "passed", "time": {"start": 1755486953193, "stop": 1755486975296, "duration": 22103}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8fcd4a1c0a83d1c3db334295a60b72e"}], "uid": "97ecf501f8b24237ae07b2178fff9b3f"}, {"name": "test_book_a_flight_to_paris", "children": [{"name": "TestEllaBookFlightParis", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "fe831e92f9edd09d", "parentUid": "d9246a10b1546e33724912523509ec95", "status": "broken", "time": {"start": 1755486990352, "stop": 1755487013471, "duration": 23119}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9246a10b1546e33724912523509ec95"}], "uid": "09531ecb34a029327b70c5fe7633b7f3"}, {"name": "test_call_mom_through_whatsapp", "children": [{"name": "TestEllaCallMomThroughWhatsapp", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "15f6f632a70c44dc", "parentUid": "b2bb86980dd84d36c5c8df2b8200871f", "status": "passed", "time": {"start": 1755487028916, "stop": 1755487056335, "duration": 27419}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2bb86980dd84d36c5c8df2b8200871f"}], "uid": "b093744c9f16724e1a2928dcb99b362b"}, {"name": "test_can_you_give_me_a_coin", "children": [{"name": "TestEllaCanYouGiveMeCoin", "children": [{"name": "测试can you give me a coin能正常执行", "uid": "f706113c78495e8c", "parentUid": "4c5d38b6a82090208170044db256ae60", "status": "passed", "time": {"start": 1755487071226, "stop": 1755487095199, "duration": 23973}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c5d38b6a82090208170044db256ae60"}], "uid": "804d60782c903cc7f780a62383e53a73"}, {"name": "test_cannot_login_in_google_email_box", "children": [{"name": "TestEllaCannotLoginGoogleEmailBox", "children": [{"name": "测试cannot login in google email box能正常执行", "uid": "9a9a4f0cdad8c1f8", "parentUid": "9d85ac6932670633773061ce5b33ae53", "status": "failed", "time": {"start": 1755487110032, "stop": 1755487131021, "duration": 20989}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d85ac6932670633773061ce5b33ae53"}], "uid": "4a7c1215b1a8ca8a7a3c6f82e99131c2"}, {"name": "test_check_status_updates_on_whatsapp", "children": [{"name": "TestEllaCheckStatusUpdatesWhatsapp", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "6b94bd4c67704947", "parentUid": "8e354564384b35c7b587be597ea49fad", "status": "passed", "time": {"start": 1755487146098, "stop": 1755487167886, "duration": 21788}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8e354564384b35c7b587be597ea49fad"}], "uid": "5ecd3e5cb8ea680501b5eea40dc4f984"}, {"name": "test_close_whatsapp", "children": [{"name": "TestEllaCloseWhatsapp", "children": [{"name": "测试close whatsapp能正常执行", "uid": "156d2dae48c170a2", "parentUid": "c32b137a79c22ae5d9f6dcb3c7a455e4", "status": "passed", "time": {"start": 1755487182764, "stop": 1755487204092, "duration": 21328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c32b137a79c22ae5d9f6dcb3c7a455e4"}], "uid": "9ab207a1a50592cfc97f93c48ff061e9"}, {"name": "test_continue_playing", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试continue playing能正常执行", "uid": "110a39c49361d59d", "parentUid": "6dc89c2239281851458ce4bffbd1dcb3", "status": "passed", "time": {"start": 1755487219100, "stop": 1755487243072, "duration": 23972}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6dc89c2239281851458ce4bffbd1dcb3"}], "uid": "d85510160425cbac31cbff0430da7a08"}, {"name": "test_could_you_please_search_an_for_me", "children": [{"name": "TestEllaCouldYouPleaseSearchAnMe", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "d42f16b5a33dd6ac", "parentUid": "05954a2ceae7fd66d1757bbd59b47ed6", "status": "failed", "time": {"start": 1755487257584, "stop": 1755487282091, "duration": 24507}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05954a2ceae7fd66d1757bbd59b47ed6"}], "uid": "18eeff7a50c6893d6191e5b7834fb912"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "253bfbce04bb2258", "parentUid": "130a6b251dee0788fb7cea69ff620c68", "status": "passed", "time": {"start": 1755487297563, "stop": 1755487318009, "duration": 20446}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "130a6b251dee0788fb7cea69ff620c68"}], "uid": "d6678d1bee0dacb44840d09dee8fdbd2"}, {"name": "test_give_me_some_money", "children": [{"name": "TestEllaGiveMeSomeMoney", "children": [{"name": "测试give me some money能正常执行", "uid": "a2427cf826f1db8f", "parentUid": "112c6b7b50b46672fc16704c6fc8c8ea", "status": "passed", "time": {"start": 1755487332893, "stop": 1755487359566, "duration": 26673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "112c6b7b50b46672fc16704c6fc8c8ea"}], "uid": "cef13fbaa76291ab7ffe428fd9b3ff0a"}, {"name": "test_global_gdp_trends", "children": [{"name": "TestEllaGlobalGdpTrends", "children": [{"name": "测试global gdp trends能正常执行", "uid": "9e7b08554d99612b", "parentUid": "831198a93942962c147b23029d2ab9c8", "status": "passed", "time": {"start": 1755487374544, "stop": 1755487402701, "duration": 28157}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "831198a93942962c147b23029d2ab9c8"}], "uid": "fff3da9e7aaa932288c845a77dbb22ae"}, {"name": "test_go_on_playing_fm", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试go on playing fm能正常执行", "uid": "7bccc690a6e3cd8f", "parentUid": "7d9d8dc2f60ad69d4996d898c7652245", "status": "passed", "time": {"start": 1755487417680, "stop": 1755487439502, "duration": 21822}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d9d8dc2f60ad69d4996d898c7652245"}], "uid": "84a31fbfbc1186641548d4ba45111773"}, {"name": "test_hello_hello", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试hello hello能正常执行", "uid": "c92ac4ae9a3abff4", "parentUid": "ba652584bc7b97ea54da02ced4645ba5", "status": "passed", "time": {"start": 1755487454549, "stop": 1755487482328, "duration": 27779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ba652584bc7b97ea54da02ced4645ba5"}], "uid": "ce7aabb9954dd4026695310de1d225b1"}, {"name": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "children": [{"name": "TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit", "children": [{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "9f59d47c1b9d0499", "parentUid": "f60519e8aeef426bc3a93755c572d6fa", "status": "passed", "time": {"start": 1755487497054, "stop": 1755487520197, "duration": 23143}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f60519e8aeef426bc3a93755c572d6fa"}], "uid": "e821be54d27a42f20fe55cd5d6203630"}, {"name": "test_hi", "children": [{"name": "TestEllaHi", "children": [{"name": "测试hi能正常执行", "uid": "ee36fc1f2dcef2d9", "parentUid": "1223273ba8f6a08b5ed65920246c9e1b", "status": "passed", "time": {"start": 1755487534969, "stop": 1755487558541, "duration": 23572}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1223273ba8f6a08b5ed65920246c9e1b"}], "uid": "29e62e7f369af137801d22a9b259e4ec"}, {"name": "test_how_is_the_weather_today", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试how is the weather today能正常执行", "uid": "95447eece93e8f08", "parentUid": "34ffa36d6317bc520ea4d2709c2ddf11", "status": "passed", "time": {"start": 1755487573336, "stop": 1755487604887, "duration": 31551}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34ffa36d6317bc520ea4d2709c2ddf11"}], "uid": "9cf284f12ed28b2464e982b240e1a734"}, {"name": "test_how_is_the_wheather_today", "children": [{"name": "TestEllaHowIsWheatherToday", "children": [{"name": "测试how is the wheather today能正常执行", "uid": "18748d8f6cbd34e5", "parentUid": "d29828ee15804f262c19aa832269e66a", "status": "passed", "time": {"start": 1755487619607, "stop": 1755487641791, "duration": 22184}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d29828ee15804f262c19aa832269e66a"}], "uid": "23a13ba389e295288e7459ad4f05d4d2"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "TestEllaHowSWeatherToday", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "76fa23815119f185", "parentUid": "862e57348213f2baefa0f1dca30e6cac", "status": "passed", "time": {"start": 1755487656973, "stop": 1755487687499, "duration": 30526}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "862e57348213f2baefa0f1dca30e6cac"}], "uid": "e9f1cfec1579f5ee85e18450ed232a94"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试how's the weather today in shanghai能正常执行", "uid": "2394ed6d3cea5a91", "parentUid": "6278997b6a5cd9b65afdbc6961c1f923", "status": "passed", "time": {"start": 1755487702026, "stop": 1755487731533, "duration": 29507}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6278997b6a5cd9b65afdbc6961c1f923"}], "uid": "0de8d8ddf42930e480591e30731da5d4"}, {"name": "test_how_to_say_hello_in_french", "children": [{"name": "TestEllaHowSayHelloFrench", "children": [{"name": "测试how to say hello in french能正常执行", "uid": "259b9154fa8999e4", "parentUid": "5667c881ac1997f398af2c0cc0dc170c", "status": "passed", "time": {"start": 1755487746333, "stop": 1755487766680, "duration": 20347}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5667c881ac1997f398af2c0cc0dc170c"}], "uid": "594bdcb49fa434818b399bc139ed191f"}, {"name": "test_how_to_say_i_love_you_in_french", "children": [{"name": "TestEllaHowSayILoveYouFrench", "children": [{"name": "测试how to say i love you in french能正常执行", "uid": "4fcf5d7f761f5451", "parentUid": "9966bad905bebb015e3efc2cbe564d9a", "status": "passed", "time": {"start": 1755487781800, "stop": 1755487804342, "duration": 22542}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9966bad905bebb015e3efc2cbe564d9a"}], "uid": "403c67e2055acbe3d575b2a0bb5e3e20"}, {"name": "test_i_wanna_be_rich", "children": [{"name": "TestEllaIWannaBeRich", "children": [{"name": "测试i wanna be rich能正常执行", "uid": "6e18d4a6a8575a63", "parentUid": "c7de20f7cd05ebdbc63478eed16102dc", "status": "passed", "time": {"start": 1755487819150, "stop": 1755487842355, "duration": 23205}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7de20f7cd05ebdbc63478eed16102dc"}], "uid": "ec547ced2ed20bbbff5a2043b9e6319c"}, {"name": "test_i_want_to_listen_to_fm", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试i want to listen to fm能正常执行", "uid": "da6c5c69e299f37a", "parentUid": "3e6dd10d8cde8c1c1a361ba19f1905ce", "status": "passed", "time": {"start": 1755487857560, "stop": 1755487879012, "duration": 21452}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e6dd10d8cde8c1c1a361ba19f1905ce"}], "uid": "f8aa7190b362d9bc914b26a879cedfbb"}, {"name": "test_i_want_to_make_a_call", "children": [{"name": "TestEllaIWantMakeCall", "children": [{"name": "测试i want to make a call能正常执行", "uid": "d09f5306f4c399f0", "parentUid": "503f251f10534ae24ea758298ae39419", "status": "passed", "time": {"start": 1755487893953, "stop": 1755487919771, "duration": 25818}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "503f251f10534ae24ea758298ae39419"}], "uid": "9643231b759e5225f7987ba2484e3f41"}, {"name": "test_i_want_to_watch_fireworks", "children": [{"name": "TestEllaIWantWatchFireworks", "children": [{"name": "测试i want to watch fireworks能正常执行", "uid": "1c4e2274bc0bb8ee", "parentUid": "a62423648b24979e9d41076cc7fc4719", "status": "passed", "time": {"start": 1755487934474, "stop": 1755487957803, "duration": 23329}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a62423648b24979e9d41076cc7fc4719"}], "uid": "0c36eef7e897f644d53145d2fdea584f"}, {"name": "test_introduce_yourself", "children": [{"name": "TestEllaIntroduceYourself", "children": [{"name": "测试introduce yourself能正常执行", "uid": "2f9ea5aad1c33b10", "parentUid": "af550fbfe44b78ae5ecace3f2e6c80de", "status": "passed", "time": {"start": 1755487972861, "stop": 1755487998989, "duration": 26128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "af550fbfe44b78ae5ecace3f2e6c80de"}], "uid": "fd2fbebbb7b47e097121b9ff95e07e74"}, {"name": "test_last_channel", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试last channel能正常执行", "uid": "383bffd77d6bb1f1", "parentUid": "820d0da6e05c44863004684d1091514c", "status": "passed", "time": {"start": 1755488013650, "stop": 1755488035054, "duration": 21404}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "820d0da6e05c44863004684d1091514c"}], "uid": "5cda476bfd122ec7b145d2d536137e78"}, {"name": "test_listen_to_fm", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试listen to fm能正常执行", "uid": "4b776d21ba83ca4b", "parentUid": "4d5c54fd2e90484a9fcaed24f986c1c5", "status": "passed", "time": {"start": 1755488049679, "stop": 1755488069821, "duration": 20142}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d5c54fd2e90484a9fcaed24f986c1c5"}], "uid": "3c594eadada6406a212e1248d8069376"}, {"name": "test_make_a_call", "children": [{"name": "TestEllaMakeCall", "children": [{"name": "测试make a call能正常执行", "uid": "9e35b7d224c3c195", "parentUid": "41b39c88fd91c3d3a8968837a65fbbfa", "status": "passed", "time": {"start": 1755488085038, "stop": 1755488114447, "duration": 29409}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41b39c88fd91c3d3a8968837a65fbbfa"}], "uid": "4c53e2e35617ddca6d4c7ed877b594a7"}, {"name": "test_measure_blood_oxygen", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试measure blood oxygen", "uid": "56a494d5439cced1", "parentUid": "669b561dda357e0a78cd474e478b760f", "status": "passed", "time": {"start": 1755488129410, "stop": 1755488152644, "duration": 23234}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "669b561dda357e0a78cd474e478b760f"}], "uid": "9a1d8b891ab1c921e9aeae6a1fc9e016"}, {"name": "test_measure_heart_rate", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试measure heart rate", "uid": "6310c03642591f6d", "parentUid": "e711cb3a8726ee47c816f06a51c76226", "status": "passed", "time": {"start": 1755488167616, "stop": 1755488191340, "duration": 23724}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e711cb3a8726ee47c816f06a51c76226"}], "uid": "6f248a0bd06ee9fb1f7f8eaff5658509"}, {"name": "test_next_music", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试next music能正常执行", "uid": "ef6e4ba0245ab44e", "parentUid": "0f4777a16fd1c10ac7e9b4ff4f5776d7", "status": "passed", "time": {"start": 1755488206196, "stop": 1755488227458, "duration": 21262}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f4777a16fd1c10ac7e9b4ff4f5776d7"}], "uid": "442354186439d371c056740d44f525a3"}, {"name": "test_next_song", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试next song能正常执行", "uid": "653a0d3ede0b9bfe", "parentUid": "fefbb85a772569566b1a005564b72b95", "status": "passed", "time": {"start": 1755488242373, "stop": 1755488263523, "duration": 21150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fefbb85a772569566b1a005564b72b95"}], "uid": "b3d0a29f48948dec64e6435937c72d4c"}, {"name": "test_open_app", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "508a8d82009e707f", "parentUid": "ef90c8fa468d5ca1174d3ecae675d779", "status": "passed", "time": {"start": 1755488278178, "stop": 1755488298157, "duration": 19979}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef90c8fa468d5ca1174d3ecae675d779"}], "uid": "13e5ce3e437fc3eba1f54c55b67226eb"}, {"name": "test_pause_music", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试pause music能正常执行", "uid": "f1e757e84c48e869", "parentUid": "3c6298ced9b1444e00c409d35e70b995", "status": "passed", "time": {"start": 1755488313386, "stop": 1755488336741, "duration": 23355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3c6298ced9b1444e00c409d35e70b995"}], "uid": "868e42a1d27583a59bfb8781ef8cc13a"}, {"name": "test_play_music_by_VLC", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by VLC", "uid": "8bc16052575dc591", "parentUid": "892ac35f77572784870fff8535230a0f", "status": "passed", "time": {"start": 1755488351959, "stop": 1755488378215, "duration": 26256}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "892ac35f77572784870fff8535230a0f"}], "uid": "ce168084d3cfe53cd0aeab22b5c369bb"}, {"name": "test_play_music_by_boomplay", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by boomplay", "uid": "27f16104520ecaf3", "parentUid": "b9d798156a0215216bcff8fb425c0fe5", "status": "passed", "time": {"start": 1755488393202, "stop": 1755488418887, "duration": 25685}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9d798156a0215216bcff8fb425c0fe5"}], "uid": "1597ecf68cb611082af48367238cc813"}, {"name": "test_play_music_by_visha", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by visha", "uid": "6adf98475880470c", "parentUid": "5ed7cbc3b1c131f5e24ae47db3cf43fe", "status": "failed", "time": {"start": 1755488433728, "stop": 1755488473304, "duration": 39576}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5ed7cbc3b1c131f5e24ae47db3cf43fe"}], "uid": "cf9a003f99c4b7fca315a507d1a23660"}, {"name": "test_play_music_by_yandex_music", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by yandex music", "uid": "9651d4c2f67e969e", "parentUid": "983892385ee3abf2e42876954c93858c", "status": "passed", "time": {"start": 1755488488227, "stop": 1755488511266, "duration": 23039}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "983892385ee3abf2e42876954c93858c"}], "uid": "df27d434403a0bfe582afda97c08ae38"}, {"name": "test_play_music_on_boomplayer", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music on boomplayer", "uid": "d0c3faa56381cbd6", "parentUid": "b9d6ebc551e5cf29ef1d89dac024c76a", "status": "passed", "time": {"start": 1755488526223, "stop": 1755488549501, "duration": 23278}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9d6ebc551e5cf29ef1d89dac024c76a"}], "uid": "a1ee256ae2705c004dbfbed36b6ab06c"}, {"name": "test_play_music_on_visha", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music on visha", "uid": "5dc251e239065bf5", "parentUid": "aa97f2bc6462853bc5721f2b9ce55fb7", "status": "failed", "time": {"start": 1755488564197, "stop": 1755488603757, "duration": 39560}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "aa97f2bc6462853bc5721f2b9ce55fb7"}], "uid": "ec00708c40596fda8b236ff83f5f3feb"}, {"name": "test_play_news", "children": [{"name": "TestEllaOpenPlayNews", "children": [{"name": "测试play news", "uid": "60eae10d83a48064", "parentUid": "bfe747e7270f83431a268436bcbe9627", "status": "passed", "time": {"start": 1755488618787, "stop": 1755488643152, "duration": 24365}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfe747e7270f83431a268436bcbe9627"}], "uid": "f2cee0c061bba4cacfb9848756bf2374"}, {"name": "test_play_political_news", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play political news", "uid": "588791b1af25ce2f", "parentUid": "31db74418c8cacb31ff13ffbb519e314", "status": "passed", "time": {"start": 1755488657796, "stop": 1755488681820, "duration": 24024}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "31db74418c8cacb31ff13ffbb519e314"}], "uid": "f61bac7c6bed9fe60b44871a005ea908"}, {"name": "test_previous_song", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试previous song能正常执行", "uid": "bd6b00d48fc75e9c", "parentUid": "931f06154cdf36555acbb1e7e387caf6", "status": "passed", "time": {"start": 1755488696656, "stop": 1755488719424, "duration": 22768}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "931f06154cdf36555acbb1e7e387caf6"}], "uid": "6df81e48e262e31ae2ce464fc5aebc7c"}, {"name": "test_remove_alarms", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试remove alarms能正常执行", "uid": "98c96ed1e0215951", "parentUid": "b15b476338ea94639ce72d4b1f19e87e", "status": "failed", "time": {"start": 1755488734617, "stop": 1755488774867, "duration": 40250}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b15b476338ea94639ce72d4b1f19e87e"}], "uid": "55977b9d46dafd95f435ed2c0dd6a4ac"}, {"name": "test_say_hello", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试say hello能正常执行", "uid": "67dbb54ba8b43268", "parentUid": "312e23dd2efc18a08c69b14303b1e4e8", "status": "passed", "time": {"start": 1755488789643, "stop": 1755488813409, "duration": 23766}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "312e23dd2efc18a08c69b14303b1e4e8"}], "uid": "32d4a645d938aa29017eb210eb4b611b"}, {"name": "test_search_my_gallery_for_food_pictures", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试search my gallery for food pictures能正常执行", "uid": "1afa938cea03331f", "parentUid": "351228ce341740c9a7f065b5bb66198e", "status": "passed", "time": {"start": 1755488827629, "stop": 1755488852084, "duration": 24455}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "351228ce341740c9a7f065b5bb66198e"}], "uid": "37e30a573eb6d007bff3c6e1f5c62993"}, {"name": "test_search_picture_in_my_gallery", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试search picture in my gallery能正常执行", "uid": "37dfbcf9e6e18077", "parentUid": "ffc611bb71ff2cff78f6e52f11d9cfc3", "status": "passed", "time": {"start": 1755488866540, "stop": 1755488895299, "duration": 28759}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ffc611bb71ff2cff78f6e52f11d9cfc3"}], "uid": "42fc1b71f54e6ca4c0d8e10a76fd5b80"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "TestEllaSearchingForAMethodOfViolentMurder", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "3350a9687471e643", "parentUid": "cbc32a4217884f390f94d0aa2f25e4bf", "status": "broken", "time": {"start": 1755488909793, "stop": 1755488933402, "duration": 23609}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cbc32a4217884f390f94d0aa2f25e4bf"}], "uid": "190f97aef35915433ff6090f9e1e18d1"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "aa90c0c998f79e87", "parentUid": "0d050c8fb5d5c2955b5389a59f902029", "status": "passed", "time": {"start": 1755488948288, "stop": 1755488971810, "duration": 23522}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d050c8fb5d5c2955b5389a59f902029"}], "uid": "42e4c350f9cb5b725ff9a0629fd707eb"}, {"name": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking", "children": [{"name": "TestEllaShowMePremierLeaguageGoalRanking", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "149ef15a425e15e8", "parentUid": "b676a8b132f27f862cb2d5f96e1e6e5b", "status": "failed", "time": {"start": 1755488986658, "stop": 1755489015120, "duration": 28462}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b676a8b132f27f862cb2d5f96e1e6e5b"}], "uid": "0d9eb6607628afa8c2895d0b987c95d9"}, {"name": "test_show_my_all_alarms", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试show my all alarms能正常执行", "uid": "c00d5166e751ea4a", "parentUid": "c5b8fe7c6e09f019c22231f0e6a06757", "status": "failed", "time": {"start": 1755489029671, "stop": 1755489060129, "duration": 30458}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5b8fe7c6e09f019c22231f0e6a06757"}], "uid": "f9e1a37a4c722b7aab5749aa2b0d21b4"}, {"name": "test_show_scores_between_livepool_and_manchester_city", "children": [{"name": "TestEllaShowScoresBetweenLivepoolManchesterCity", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "7a3c8a9940a0b583", "parentUid": "00794312e310e31963198853503abb78", "status": "passed", "time": {"start": 1755489074855, "stop": 1755489099765, "duration": 24910}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "00794312e310e31963198853503abb78"}], "uid": "5f0fdb9ab1509aa4e4013922ca24fdb8"}, {"name": "test_start_countdown", "children": [{"name": "TestEllaHi", "children": [{"name": "测试start countdown能正常执行", "uid": "791f368cc19ac887", "parentUid": "cc0330d4f66a9464a33eb658046490d0", "status": "passed", "time": {"start": 1755489113664, "stop": 1755489134798, "duration": 21134}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cc0330d4f66a9464a33eb658046490d0"}], "uid": "0f695cc69bb59fc95b2585b4047bcf13"}, {"name": "test_stop_music", "children": [{"name": "TestEllaStopMusic", "children": [{"name": "测试stop music能正常执行", "uid": "e002d2408e9ba989", "parentUid": "7b0d317bdb65f00d77c3ccf77052f972", "status": "passed", "time": {"start": 1755489148984, "stop": 1755489170291, "duration": 21307}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0d317bdb65f00d77c3ccf77052f972"}], "uid": "92323b24a83f37088059a15dd2e70612"}, {"name": "test_stop_run", "children": [{"name": "TestEllaStopRun", "children": [{"name": "测试stop run能正常执行", "uid": "7ed72d7e314cdad5", "parentUid": "c3342d9dce68675b0ffe24e49dc3ccbb", "status": "passed", "time": {"start": 1755489184518, "stop": 1755489204319, "duration": 19801}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c3342d9dce68675b0ffe24e49dc3ccbb"}], "uid": "224850ea60208f7ca8a6e6ce15d4c0eb"}, {"name": "test_stop_workout", "children": [{"name": "TestEllaStopWorkout", "children": [{"name": "测试stop workout能正常执行", "uid": "a7f088139b634a59", "parentUid": "d09dcb3a4549f5f7f1d86fc4e386f4bf", "status": "passed", "time": {"start": 1755489218571, "stop": 1755489241732, "duration": 23161}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d09dcb3a4549f5f7f1d86fc4e386f4bf"}], "uid": "d886488db4cad9621ae720cb85d9a863"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "TestEllaSummarizeContentThisPage", "children": [{"name": "测试summarize content on this page能正常执行", "uid": "4ed0911ea04026c5", "parentUid": "98b21f71a48feae9b99edfcfa187ba23", "status": "passed", "time": {"start": 1755489256403, "stop": 1755489277589, "duration": 21186}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98b21f71a48feae9b99edfcfa187ba23"}], "uid": "8ca3f16dcd6c82e9d2735b11b5d0cef8"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "TestEllaSummarizeWhatIMReading", "children": [{"name": "测试summarize what i'm reading能正常执行", "uid": "6560b59b368cba08", "parentUid": "83af552ea5df666561ceadf107b37508", "status": "passed", "time": {"start": 1755489292164, "stop": 1755489313431, "duration": 21267}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83af552ea5df666561ceadf107b37508"}], "uid": "34ae398183d59ded56dd80b7bf08775a"}, {"name": "test_take_a_joke", "children": [{"name": "TestEllaTakeJoke", "children": [{"name": "测试take a joke能正常执行", "uid": "4cb65b029de52b72", "parentUid": "3acb54c6ceb30b54183a40536a4da62b", "status": "passed", "time": {"start": 1755489328127, "stop": 1755489355410, "duration": 27283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3acb54c6ceb30b54183a40536a4da62b"}], "uid": "c6397f27fa8b05537072fa113b8c3fc8"}, {"name": "test_take_a_note_on_how_to_build_a_treehouse", "children": [{"name": "TestEllaTakeNoteHowBuildTreehouse", "children": [{"name": "测试take a note on how to build a treehouse能正常执行", "uid": "4fc9d1ce458d51a0", "parentUid": "36906abdedd73fbaf2f1118b2b081f40", "status": "passed", "time": {"start": 1755489369956, "stop": 1755489391123, "duration": 21167}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "36906abdedd73fbaf2f1118b2b081f40"}], "uid": "dd58ab9d8bbd4cb87632d02b4aaab257"}, {"name": "test_take_notes_on_how_to_build_a_treehouse", "children": [{"name": "TestEllaTakeNotesHowBuildTreehouse", "children": [{"name": "测试take notes on how to build a treehouse能正常执行", "uid": "3b3589ad2946f7f9", "parentUid": "521cf47a0da1175d33dabbff6602fa38", "status": "passed", "time": {"start": 1755489405539, "stop": 1755489433993, "duration": 28454}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "521cf47a0da1175d33dabbff6602fa38"}], "uid": "e2b5971fa35ed9c2de1613d7fffb9817"}, {"name": "test_tell_me_a_joke", "children": [{"name": "TestEllaTellMeJoke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "b8228732f7da085b", "parentUid": "f4f36a0e5ab72d1f1ffe809d3354f0d7", "status": "failed", "time": {"start": 1755489448500, "stop": 1755489474709, "duration": 26209}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f4f36a0e5ab72d1f1ffe809d3354f0d7"}], "uid": "9dabb9d428e867eabdc2d3a69afdd429"}, {"name": "test_unset_alarms", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试unset alarms能正常执行", "uid": "d12df20c75c0fc6e", "parentUid": "2bb2ca28bbb6a8470a2b76f57b47259d", "status": "failed", "time": {"start": 1755489489557, "stop": 1755489523149, "duration": 33592}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2bb2ca28bbb6a8470a2b76f57b47259d"}], "uid": "36e1bffd7d50842f447419e500c5fb5d"}, {"name": "test_video_call_mom_through_whatsapp", "children": [{"name": "TestEllaVideoCallMomThroughWhatsapp", "children": [{"name": "测试video call mom through whatsapp能正常执行", "uid": "f0fd43da9bcfbf0f", "parentUid": "e9a671f0e7f7a5d9dd84e36b17c5b9d6", "status": "passed", "time": {"start": 1755489537426, "stop": 1755489562571, "duration": 25145}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9a671f0e7f7a5d9dd84e36b17c5b9d6"}], "uid": "52150962482f63ecf4ad855e891df94e"}, {"name": "test_view_recent_alarms", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试view recent alarms能正常执行", "uid": "88e23c874fd42d8d", "parentUid": "01d2934b3f58619250c2716961902d1f", "status": "failed", "time": {"start": 1755489577196, "stop": 1755489610377, "duration": 33181}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "01d2934b3f58619250c2716961902d1f"}], "uid": "c6528f9d3299aa359273418bb42a94e1"}, {"name": "test_what_is_apec", "children": [{"name": "TestEllaWhatIsApec", "children": [{"name": "测试what is apec?能正常执行", "uid": "361a771981e36dd1", "parentUid": "1e37b5c5f74c73b4b983a5a9a1939ee9", "status": "passed", "time": {"start": 1755489624877, "stop": 1755489650766, "duration": 25889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e37b5c5f74c73b4b983a5a9a1939ee9"}], "uid": "c36e05a3608c90521de87174b028a2f3"}, {"name": "test_what_languages_do_you_support", "children": [{"name": "TestEllaWhatLanguagesDoYouSupport", "children": [{"name": "测试What languages do you support能正常执行", "uid": "8e605cf9348c1ee4", "parentUid": "0aa09f1aa9e12279998425a7031b2afb", "status": "passed", "time": {"start": 1755489665145, "stop": 1755489685068, "duration": 19923}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0aa09f1aa9e12279998425a7031b2afb"}], "uid": "53246026784adbe982eaaaa893c66882"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试what's the weather like in shanghai today能正常执行", "uid": "3fc16b4876c03a0a", "parentUid": "abdf90845d6ece7e59853b1633b70976", "status": "passed", "time": {"start": 1755489699531, "stop": 1755489728689, "duration": 29158}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "abdf90845d6ece7e59853b1633b70976"}], "uid": "8d57bd429c231f391c68fd5f296df48d"}, {"name": "test_what_s_the_weather_like_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试What's the weather like today能正常执行", "uid": "c770dab2829aa3e5", "parentUid": "c1e6d14957f08c2bf40b1a53d83db987", "status": "passed", "time": {"start": 1755489742870, "stop": 1755489774812, "duration": 31942}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c1e6d14957f08c2bf40b1a53d83db987"}], "uid": "3e13836d43915e9a2f4320f8166c2226"}, {"name": "test_what_s_the_weather_today", "children": [{"name": "TestEllaWhatSWeatherToday", "children": [{"name": "测试what·s the weather today？能正常执行", "uid": "527bdb5696f0b1d7", "parentUid": "1aa11a1add75d7b42e4f5f940b55235c", "status": "passed", "time": {"start": 1755489789590, "stop": 1755489820435, "duration": 30845}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1aa11a1add75d7b42e4f5f940b55235c"}], "uid": "e75c3dcc6971391aa4cea847d0c3df26"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "TestEllaWhatSWheatherToday", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "bf1ac4cb68f2f679", "parentUid": "b0d78f07238a99d14c63a09a132b62ac", "status": "failed", "time": {"start": 1755489835403, "stop": 1755489856515, "duration": 21112}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0d78f07238a99d14c63a09a132b62ac"}], "uid": "df9827022acfcacb6cb91f3eade022ae"}, {"name": "test_what_s_your_name", "children": [{"name": "TestEllaWhatSYourName", "children": [{"name": "测试what's your name？能正常执行", "uid": "9ce970b1f233022b", "parentUid": "3d8e08ff740579156ca205ed22a4756d", "status": "failed", "time": {"start": 1755489871371, "stop": 1755489892166, "duration": 20795}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3d8e08ff740579156ca205ed22a4756d"}], "uid": "5920d33b40456d715947fca7a27a7717"}, {"name": "test_what_time_is_it_now", "children": [{"name": "TestEllaWhatTimeIsItNow", "children": [{"name": "测试what time is it now能正常执行", "uid": "70f46be2f8439194", "parentUid": "164a179950ff8bd6ebd07029c9858807", "status": "passed", "time": {"start": 1755489906620, "stop": 1755489927585, "duration": 20965}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "164a179950ff8bd6ebd07029c9858807"}], "uid": "809dcfb95d068444fb46fbf13d07f70d"}, {"name": "test_whats_the_weather_today", "children": [{"name": "TestEllaWhatsWeatherToday", "children": [{"name": "测试what's the weather today?能正常执行", "uid": "dffa0f0e3f9337b4", "parentUid": "6cc45efb201d9209b1a6f8794f17a6ed", "status": "passed", "time": {"start": 1755489941935, "stop": 1755489973923, "duration": 31988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6cc45efb201d9209b1a6f8794f17a6ed"}], "uid": "82328665eb7ef47d6e8c2fd0919234af"}, {"name": "test_who_is_harry_potter", "children": [{"name": "TestEllaWhoIsHarry<PERSON>otter", "children": [{"name": "测试who is harry potter能正常执行", "uid": "5cb6e78030f32b51", "parentUid": "b5f5061dbe131241d2697c1336ee4d0f", "status": "passed", "time": {"start": 1755489988144, "stop": 1755490016220, "duration": 28076}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5f5061dbe131241d2697c1336ee4d0f"}], "uid": "acd61d814ff9a72ba57d12465068a2b4"}, {"name": "test_who_is_j_k_rowling", "children": [{"name": "TestEllaWhoIsJKRowling", "children": [{"name": "测试who is j k rowling能正常执行", "uid": "ca1ef5fb16629bf2", "parentUid": "bfb9c688e10f1ae6ddcd570a4f050325", "status": "passed", "time": {"start": 1755490030557, "stop": 1755490053158, "duration": 22601}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfb9c688e10f1ae6ddcd570a4f050325"}], "uid": "cb98f3056a0e33f548dde9b60e938b6a"}, {"name": "test_why_is_my_phone_not_ringing_on_incoming_calls", "children": [{"name": "TestEllaWhyIsMyPhoneNotRingingIncomingCalls", "children": [{"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "99cb87ce20d5cb25", "parentUid": "cdaa83f7b575bd82cf726da90db761c8", "status": "passed", "time": {"start": 1755490067733, "stop": 1755490096057, "duration": 28324}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cdaa83f7b575bd82cf726da90db761c8"}], "uid": "a302ef9f7c0c93aaaa0b2857fe578fbe"}, {"name": "test_why_my_charging_is_so_slow", "children": [{"name": "TestEllaWhyMyChargingIsSoSlow", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "6276d226895c70b7", "parentUid": "3750969afddcdbdadfd545f67aca7706", "status": "passed", "time": {"start": 1755490110747, "stop": 1755490131503, "duration": 20756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3750969afddcdbdadfd545f67aca7706"}], "uid": "48e6e701074c76202e2d7a86eeb1f71a"}], "uid": "0a5f897bb744ec2f8b960fc5954cddf6"}, {"name": "testcases.test_ella.self_function", "children": [{"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "TestEllaCuteLittleBoyIsSkiing", "children": [{"name": "测试A cute little boy is skiing 能正常执行", "uid": "e4aa8ed0c268c28a", "parentUid": "8b7eccb5b26545d2641b4c794efeb980", "status": "passed", "time": {"start": 1755490146175, "stop": 1755490225279, "duration": 79104}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8b7eccb5b26545d2641b4c794efeb980"}], "uid": "dad689645015ed2d5ac2514ee1b823bb"}, {"name": "test_change_the_style_of_this_image_to_d_cartoon", "children": [{"name": "TestEllaChangeStyleThisImageDCartoon", "children": [{"name": "测试Change the style of this image to 3D cartoon能正常执行", "uid": "6b805288adc04f20", "parentUid": "4cdd1d689204fd988f2c4e45e88e274d", "status": "failed", "time": {"start": 1755490239551, "stop": 1755490353944, "duration": 114393}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4cdd1d689204fd988f2c4e45e88e274d"}], "uid": "c51be51f26dac6191d7b7bec924dbca4"}, {"name": "test_document_summary", "children": [{"name": "TestEllaDocumentSummary", "children": [{"name": "测试document summary能正常执行", "uid": "c24284a89688e8f0", "parentUid": "38db111dde52e5d691ca0001e26407d7", "status": "failed", "time": {"start": 1755490368695, "stop": 1755490418920, "duration": 50225}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "38db111dde52e5d691ca0001e26407d7"}], "uid": "24c3e091078dc08ed0cb30a88205a81e"}, {"name": "test_extend_the_image", "children": [{"name": "TestEllaExtendImage", "children": [{"name": "测试extend the image能正常执行", "uid": "5b2a3a710c3ce6e8", "parentUid": "393e2cd81bc0c83b103ae791cbd77bf4", "status": "passed", "time": {"start": 1755490433645, "stop": 1755490548883, "duration": 115238}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "393e2cd81bc0c83b103ae791cbd77bf4"}], "uid": "0a3a2ae80d848c57f0eca83639cb57fe"}, {"name": "test_puppy", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试puppy能正常执行", "uid": "80a4a14aa96684fa", "parentUid": "f3f07d4eec9290abbf25643b1318f67a", "status": "passed", "time": {"start": 1755490563243, "stop": 1755490639997, "duration": 76754}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3f07d4eec9290abbf25643b1318f67a"}], "uid": "ddd2933fb4f8621056e25a89de15770f"}, {"name": "test_scan_the_qr_code_in_the_image", "children": [{"name": "TestEllaScanQrCodeImage", "children": [{"name": "测试Scan the QR code in the image 能正常执行", "uid": "29ff9d063c63e76e", "parentUid": "dd63d92d440de060fa558548d17f855d", "status": "failed", "time": {"start": 1755490654075, "stop": 1755490767973, "duration": 113898}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dd63d92d440de060fa558548d17f855d"}], "uid": "822a5cff1d71f7beae87c86f53bd9220"}, {"name": "test_scan_this_qr_code", "children": [{"name": "TestEllaScanThisQrCode", "children": [{"name": "测试Scan this QR code 能正常执行", "uid": "9d90e5d81b7acbb", "parentUid": "3821a44c9e742d9050eeaed89fca1a9e", "status": "failed", "time": {"start": 1755490782714, "stop": 1755490896767, "duration": 114053}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3821a44c9e742d9050eeaed89fca1a9e"}], "uid": "683eedb3fa7e1e082c303276578efc64"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "TestEllaSummarizeWhatIMReading", "children": [{"name": "测试Summarize what I'm reading能正常执行", "uid": "a146b3901c7ec634", "parentUid": "1ce8f55a5f12c803cac6a4b96093cd37", "status": "failed", "time": {"start": 1755490911418, "stop": 1755490961512, "duration": 50094}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ce8f55a5f12c803cac6a4b96093cd37"}], "uid": "0c6575e4e78c0e82d37a47d342aacd43"}], "uid": "6dc11cb9bb372733a2ed6f167a12d612"}, {"name": "testcases.test_ella.system_coupling", "children": [{"name": "test_adjustment_the_brightness_to", "children": [{"name": "TestEllaAdjustmentBrightness", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "6589c9a71513424a", "parentUid": "13406d3249e45c3f0440290f62a0a585", "status": "failed", "time": {"start": 1755490976350, "stop": 1755490998528, "duration": 22178}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "13406d3249e45c3f0440290f62a0a585"}], "uid": "169492529a068768aa4c236be730e885"}, {"name": "test_adjustment_the_brightness_to_maximun", "children": [{"name": "TestEllaAdjustmentBrightnessMaximun", "children": [{"name": "测试adjustment the brightness to maximun能正常执行", "uid": "d9c4e23c7973b52e", "parentUid": "dcbaa6c5820b02de9b2fd1d448c5f7f3", "status": "passed", "time": {"start": 1755491013583, "stop": 1755491033995, "duration": 20412}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dcbaa6c5820b02de9b2fd1d448c5f7f3"}], "uid": "2e3c0b2f2c6cb9b8fa763e80760a9173"}, {"name": "test_adjustment_the_brightness_to_minimun", "children": [{"name": "TestEllaAdjustmentBrightnessMinimun", "children": [{"name": "测试adjustment the brightness to minimun能正常执行", "uid": "498f84666e902962", "parentUid": "b826f277275519963f6e7584e506c202", "status": "passed", "time": {"start": 1755491048611, "stop": 1755491071357, "duration": 22746}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b826f277275519963f6e7584e506c202"}], "uid": "da1995b460c3e095258501543cdbf23c"}, {"name": "test_boost_phone", "children": [{"name": "TestEllaBoostPhone", "children": [{"name": "测试boost phone能正常执行", "uid": "ba6ba48f138d3670", "parentUid": "6b73e18bf77dcabfceaad2f004e2aecf", "status": "passed", "time": {"start": 1755491085520, "stop": 1755491105073, "duration": 19553}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6b73e18bf77dcabfceaad2f004e2aecf"}], "uid": "5b2341e5eca7df187d07a4073eb501b0"}, {"name": "test_change_your_language", "children": [{"name": "TestEllaChangeYourLanguage", "children": [{"name": "测试change your language能正常执行", "uid": "3e428b7187a8cf95", "parentUid": "9a038213fb4c0ae8418e40345d9669a6", "status": "passed", "time": {"start": 1755491119384, "stop": 1755491143604, "duration": 24220}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a038213fb4c0ae8418e40345d9669a6"}], "uid": "a00975935553052f546cc957e4a83754"}, {"name": "test_change_your_language_to_chinese", "children": [{"name": "TestEllaChangeYourLanguageChinese", "children": [{"name": "测试change your language to chinese能正常执行", "uid": "e4cfd384aca8dfd1", "parentUid": "667250dfa9bb5f1cdb6758ac0cf296c4", "status": "skipped", "time": {"start": 1755491144977, "stop": 1755491144977, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='语言设置为中文，影响别的Case，先跳过')"]}], "uid": "667250dfa9bb5f1cdb6758ac0cf296c4"}], "uid": "1d67b1adb2c056e5340bbadd4c0bffaf"}, {"name": "test_check_front_camera_information", "children": [{"name": "TestEllaCheckFrontCameraInformation", "children": [{"name": "测试check front camera information能正常执行", "uid": "8786240d94a2dca6", "parentUid": "12ceb3f2ca52b0e705dbde45ff23637e", "status": "passed", "time": {"start": 1755491157581, "stop": 1755491180503, "duration": 22922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12ceb3f2ca52b0e705dbde45ff23637e"}], "uid": "900d85dd19a0727e9711d6cd307efe61"}, {"name": "test_clear_junk_files", "children": [{"name": "TestEllaClearJunkFiles", "children": [{"name": "测试clear junk files命令", "uid": "d91c0ef56e885c23", "parentUid": "d96f4287167e7edbf6a73f9393984a7d", "status": "passed", "time": {"start": 1755491194871, "stop": 1755491225162, "duration": 30291}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d96f4287167e7edbf6a73f9393984a7d"}], "uid": "4c6f4bc8bf308c30c67ce68d84340b5b"}, {"name": "test_close_airplane", "children": [{"name": "TestEllaCloseAirplane", "children": [{"name": "测试close airplane能正常执行", "uid": "ae5c1a8bfa978084", "parentUid": "012094858349fac0311a156164204d4c", "status": "passed", "time": {"start": 1755491239106, "stop": 1755491259829, "duration": 20723}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "012094858349fac0311a156164204d4c"}], "uid": "ff15b32bcba80e71ea15e866e1d66db6"}, {"name": "test_close_bluetooth", "children": [{"name": "TestEllaCloseBluetooth", "children": [{"name": "测试close bluetooth能正常执行", "uid": "99fe32ddc0a0735c", "parentUid": "1d722ccfa222e03cef528f38b7d2fac2", "status": "passed", "time": {"start": 1755491274030, "stop": 1755491293750, "duration": 19720}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1d722ccfa222e03cef528f38b7d2fac2"}], "uid": "aa4164fbbe23d76eaa9669ca854678b0"}, {"name": "test_close_flashlight", "children": [{"name": "TestEllaCloseFlashlight", "children": [{"name": "测试close flashlight能正常执行", "uid": "50d71690f48a3a47", "parentUid": "d82496a02dca69564564d1692d50a7e6", "status": "passed", "time": {"start": 1755491308350, "stop": 1755491330893, "duration": 22543}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d82496a02dca69564564d1692d50a7e6"}], "uid": "82ec45c00676757f05c50c06f422bf20"}, {"name": "test_close_wifi", "children": [{"name": "TestEllaCloseWifi", "children": [{"name": "测试close wifi能正常执行", "uid": "63058376ece98827", "parentUid": "008777689d082e9a2e48620bded15bc5", "status": "skipped", "time": {"start": 1755491332233, "stop": 1755491332233, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='该脚本较特殊，先跳过')", "smoke"]}], "uid": "008777689d082e9a2e48620bded15bc5"}], "uid": "eee3cdf5a14de75128923fc136f73cc1"}, {"name": "test_countdown_min", "children": [{"name": "TestEllaCountdownMin", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "ac48c98400f9cccd", "parentUid": "45aee82a630fdec483043c1cf3f74bf1", "status": "failed", "time": {"start": 1755491345083, "stop": 1755491372424, "duration": 27341}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "45aee82a630fdec483043c1cf3f74bf1"}], "uid": "f6b105e104089e68c280ff5c4fa1d393"}, {"name": "test_decrease_the_brightness", "children": [{"name": "TestEllaDecreaseBrightness", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "806430326f721111", "parentUid": "375849c4ecfe2bbf01bcecaaef84c8db", "status": "failed", "time": {"start": 1755491386782, "stop": 1755491408118, "duration": 21336}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "375849c4ecfe2bbf01bcecaaef84c8db"}], "uid": "c7d1062159fa1a8529d01de2304b5ef9"}, {"name": "test_decrease_the_volume_to_the_minimun", "children": [{"name": "TestEllaDecreaseVolumeMinimun", "children": [{"name": "测试decrease the volume to the minimun能正常执行", "uid": "dc13bcd8c8f14a21", "parentUid": "fb9368bd1cdcd8464c201f9558237a29", "status": "failed", "time": {"start": 1755491422791, "stop": 1755491444034, "duration": 21243}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb9368bd1cdcd8464c201f9558237a29"}], "uid": "0ad50ed8c4a10470ce5f7a0972d06d5a"}, {"name": "test_end_screen_recording", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "83097c2bd2ac1ce0", "parentUid": "cb8ba3420a3f42d8dc07018eb937d89c", "status": "passed", "time": {"start": 1755491458817, "stop": 1755491484210, "duration": 25393}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "11bc3ac871a42678", "parentUid": "cb8ba3420a3f42d8dc07018eb937d89c", "status": "passed", "time": {"start": 1755491498462, "stop": 1755491524849, "duration": 26387}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb8ba3420a3f42d8dc07018eb937d89c"}], "uid": "80f3221929ed1bf33f33827613bd2441"}, {"name": "test_help_me_take_a_long_screenshot", "children": [{"name": "TestEllaHelpMeTakeLongScreenshot", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "837a7a74c23590a8", "parentUid": "eddf309b25a6b37f2ead731622b025cd", "status": "passed", "time": {"start": 1755491539243, "stop": 1755491563996, "duration": 24753}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eddf309b25a6b37f2ead731622b025cd"}], "uid": "2f4262c05e172e74fc2c7093e52387d3"}, {"name": "test_help_me_take_a_screenshot", "children": [{"name": "TestEllaHelpMeTakeScreenshot", "children": [{"name": "测试help me take a screenshot能正常执行", "uid": "fb9a425653523825", "parentUid": "1b701ed7fafa2b81e3b507e55819c327", "status": "passed", "time": {"start": 1755491577983, "stop": 1755491601550, "duration": 23567}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b701ed7fafa2b81e3b507e55819c327"}], "uid": "b0951018c5f4d74c567ddd6d37b5afe7"}, {"name": "test_increase_notification_volume", "children": [{"name": "TestEllaIncreaseNotificationVolume", "children": [{"name": "测试increase notification volume能正常执行", "uid": "13bdbf09737f718", "parentUid": "b8eb5e6497f608e189101bb3275b16a9", "status": "broken", "time": {"start": 1755491616048, "stop": 1755491637028, "duration": 20980}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b8eb5e6497f608e189101bb3275b16a9"}], "uid": "a34132ba9f18618817a83bb865ca94b7"}, {"name": "test_increase_screen_brightness", "children": [{"name": "TestEllaIncreaseScreenBrightness", "children": [{"name": "测试increase screen brightness能正常执行", "uid": "3ec5cbee09598edc", "parentUid": "20aa873f0c4c4f60bbde51da6793cb2b", "status": "passed", "time": {"start": 1755491651675, "stop": 1755491672337, "duration": 20662}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20aa873f0c4c4f60bbde51da6793cb2b"}], "uid": "b6d21924bec515195c4bf8171560ed46"}, {"name": "test_increase_the_brightness", "children": [{"name": "TestEllaIncreaseBrightness", "children": [{"name": "测试increase the brightness能正常执行", "uid": "37117e96ddab6ea7", "parentUid": "47c106fcb2b9a99b4c62d22fd64bd89f", "status": "passed", "time": {"start": 1755491686786, "stop": 1755491706411, "duration": 19625}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47c106fcb2b9a99b4c62d22fd64bd89f"}], "uid": "042bd08c4053292eed72c00fd96da26e"}, {"name": "test_increase_the_volume_to_the_maximun", "children": [{"name": "TestEllaIncreaseVolumeMaximun", "children": [{"name": "测试increase the volume to the maximun能正常执行", "uid": "59429721dd0dace4", "parentUid": "52503ef39d7d87f8636e660ee112471c", "status": "failed", "time": {"start": 1755491720776, "stop": 1755491740415, "duration": 19639}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "52503ef39d7d87f8636e660ee112471c"}], "uid": "f7dccece38a47e884bb63ca829b64be5"}, {"name": "test_long_screenshot", "children": [{"name": "TestEllaLongScreenshot", "children": [{"name": "测试long screenshot能正常执行", "uid": "8b9bdfa7542f060d", "parentUid": "10001a71b5e68735481b40decacdb66b", "status": "passed", "time": {"start": 1755491755037, "stop": 1755491777929, "duration": 22892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "10001a71b5e68735481b40decacdb66b"}], "uid": "fd90b40ba6ffaaec61bd1bf8a6dfb743"}, {"name": "test_make_the_phone_mute", "children": [{"name": "TestEllaMakePhoneMute", "children": [{"name": "测试make the phone mute能正常执行", "uid": "ce8763c3cc105dc3", "parentUid": "24476fe437d96ddcbc62c3ef9aa047c0", "status": "failed", "time": {"start": 1755491792028, "stop": 1755491812856, "duration": 20828}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "24476fe437d96ddcbc62c3ef9aa047c0"}], "uid": "84c9aca5a3baaa9491cac0f7fee81412"}, {"name": "test_max_alarm_clock_volume", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试max alarm clock volume", "uid": "b53378e78d39c315", "parentUid": "09f98beca814f51aed5348f6b0f44ddd", "status": "failed", "time": {"start": 1755491827304, "stop": 1755491855258, "duration": 27954}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "09f98beca814f51aed5348f6b0f44ddd"}], "uid": "fb857614c7c76db2865687d37b77f473"}, {"name": "test_max_brightness", "children": [{"name": "TestEllaMaxBrightness", "children": [{"name": "测试max brightness能正常执行", "uid": "f869bb5568174dcd", "parentUid": "85b1136ebae2ee12d8ddb1e37288abe2", "status": "passed", "time": {"start": 1755491869555, "stop": 1755491889866, "duration": 20311}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85b1136ebae2ee12d8ddb1e37288abe2"}], "uid": "ca0f75c62fe53e418626f72cd27e0bd8"}, {"name": "test_max_notifications_volume", "children": [{"name": "TestEllaMaxNotificationsVolume", "children": [{"name": "测试max notifications volume能正常执行", "uid": "4a1cc7fd5ab62fa0", "parentUid": "09fd5737ec5c45df40b3262f8478b15f", "status": "failed", "time": {"start": 1755491904158, "stop": 1755491926567, "duration": 22409}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "09fd5737ec5c45df40b3262f8478b15f"}], "uid": "e837af21d36f3dc8abc576328735eb1d"}, {"name": "test_max_ring_volume", "children": [{"name": "TestEllaMaxRingVolume", "children": [{"name": "测试max ring volume能正常执行", "uid": "b65d69907df5275e", "parentUid": "e47bc1654595bd88ef8d8fb972840ed0", "status": "failed", "time": {"start": 1755491941481, "stop": 1755491962474, "duration": 20993}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e47bc1654595bd88ef8d8fb972840ed0"}], "uid": "b9169c2ed8415413e850ad292772e6db"}, {"name": "test_maximum_volume", "children": [{"name": "TestEllaMaximumVolume", "children": [{"name": "测试maximum volume能正常执行", "uid": "642f6aad5be1ce56", "parentUid": "6dba0e91979783ed4ec530b3d80f6368", "status": "failed", "time": {"start": 1755491977526, "stop": 1755492000401, "duration": 22875}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6dba0e91979783ed4ec530b3d80f6368"}], "uid": "42069d56eafe8c2317edc83b4e050135"}, {"name": "test_memory_cleanup", "children": [{"name": "TestEllaMemoryCleanup", "children": [{"name": "测试memory cleanup能正常执行", "uid": "afac8a8324ac4f58", "parentUid": "402a46f59edfad113bb75993c3b1ee0a", "status": "passed", "time": {"start": 1755492015277, "stop": 1755492061907, "duration": 46630}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "402a46f59edfad113bb75993c3b1ee0a"}], "uid": "b4111ca5d9e6c15c7f70fd341fca204a"}, {"name": "test_min_alarm_clock_volume", "children": [{"name": "TestEllaOpenAlarmVolume", "children": [{"name": "测试min alarm clock volume", "uid": "903bd91eec3dc183", "parentUid": "a92323db1e8c944d17469d9894b312dd", "status": "failed", "time": {"start": 1755492076119, "stop": 1755492104069, "duration": 27950}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a92323db1e8c944d17469d9894b312dd"}], "uid": "ecca1cd78ec86a6d53c57ce8a8bf6bef"}, {"name": "test_min_brightness", "children": [{"name": "Test<PERSON>lla<PERSON>inBrightness", "children": [{"name": "测试min brightness能正常执行", "uid": "200fe6271a9e4a58", "parentUid": "bec25736a60461a24b98fc9da33522c9", "status": "passed", "time": {"start": 1755492118698, "stop": 1755492140870, "duration": 22172}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bec25736a60461a24b98fc9da33522c9"}], "uid": "04bbd8f5fe17665026d8104311b75972"}, {"name": "test_min_notifications_volume", "children": [{"name": "TestEllaMinNotificationsVolume", "children": [{"name": "测试min notifications volume能正常执行", "uid": "e1865ec637991cf1", "parentUid": "1458feb1b583146892a2be31a0d06eb9", "status": "failed", "time": {"start": 1755492155024, "stop": 1755492174571, "duration": 19547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1458feb1b583146892a2be31a0d06eb9"}], "uid": "3570796246ad2cac35d5036ad895b355"}, {"name": "test_min_ring_volume", "children": [{"name": "TestEllaMinRingVolume", "children": [{"name": "测试min ring volume能正常执行", "uid": "c610d0415d4aadd9", "parentUid": "34fb0e05a5e14399111297f1a1bff473", "status": "failed", "time": {"start": 1755492189155, "stop": 1755492208910, "duration": 19755}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34fb0e05a5e14399111297f1a1bff473"}], "uid": "fc3585f82fec1f418ef995d0c22b7491"}, {"name": "test_minimum_volume", "children": [{"name": "TestEllaMinimumVolume", "children": [{"name": "测试minimum volume能正常执行", "uid": "68439b448a6afa57", "parentUid": "8ea78a3baf10b4390053ed171e147a99", "status": "failed", "time": {"start": 1755492223504, "stop": 1755492242921, "duration": 19417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8ea78a3baf10b4390053ed171e147a99"}], "uid": "5f5ae3dc5f88288d2dab05294be5ba53"}, {"name": "test_open_bluetooth", "children": [{"name": "TestEllaOpenBluetooth", "children": [{"name": "测试open bluetooth", "uid": "992b6c1c0b6fe0af", "parentUid": "ce97beca6f71d794ad4aa2488c2a51cc", "status": "passed", "time": {"start": 1755492257734, "stop": 1755492277893, "duration": 20159}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ce97beca6f71d794ad4aa2488c2a51cc"}], "uid": "ff64db128f121f9873fe4471e4dffdb3"}, {"name": "test_open_bt", "children": [{"name": "TestEllaOpenBluetooth", "children": [{"name": "测试open bt", "uid": "d0630ac4c0c281af", "parentUid": "cff090d0294d065259343d3c0be3e870", "status": "passed", "time": {"start": 1755492292331, "stop": 1755492312786, "duration": 20455}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cff090d0294d065259343d3c0be3e870"}], "uid": "889f90fb090c14dc6df34ad6387bda95"}, {"name": "test_open_flashlight", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open flashlight", "uid": "c5a8bb32aeabc6cc", "parentUid": "bc7827d0911898d37df7fcf78b433e01", "status": "passed", "time": {"start": 1755492327095, "stop": 1755492349610, "duration": 22515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc7827d0911898d37df7fcf78b433e01"}], "uid": "dbd0fd96c304d0311b6a78f5c9f6d7a2"}, {"name": "test_open_wifi", "children": [{"name": "TestEllaOpenWifi", "children": [{"name": "测试open wifi", "uid": "e490f1dc443a73e2", "parentUid": "4f8addb21351f5f5c147c6f32359e6f8", "status": "passed", "time": {"start": 1755492363961, "stop": 1755492385305, "duration": 21344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4f8addb21351f5f5c147c6f32359e6f8"}], "uid": "732e1a744ff2476f886f4f9095220be6"}, {"name": "test_power_off", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试power off能正常执行", "uid": "7c68f6256b732fcc", "parentUid": "00aeea3201bd0637a03b74b9daa8cd97", "status": "skipped", "time": {"start": 1755492386684, "stop": 1755492386684, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')", "smoke"]}], "uid": "00aeea3201bd0637a03b74b9daa8cd97"}], "uid": "6859f9353cbbcc7d8991c89b7481d6f6"}, {"name": "test_power_saving", "children": [{"name": "TestEllaPowerSaving", "children": [{"name": "测试power saving能正常执行", "uid": "8a35e737a53bfab0", "parentUid": "cca844da29af41e39df266a7ebd5b861", "status": "passed", "time": {"start": 1755492399786, "stop": 1755492427291, "duration": 27505}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cca844da29af41e39df266a7ebd5b861"}], "uid": "b3608e6af09c4f99081f13cadb0a5d44"}, {"name": "test_screen_record", "children": [{"name": "TestEllaScreenRecord", "children": [{"name": "测试screen record能正常执行", "uid": "5be804837474a0ad", "parentUid": "26f506cb56eae899b201e1de8c38bd6f", "status": "passed", "time": {"start": 1755492441441, "stop": 1755492466169, "duration": 24728}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "8819ae8dbd92792c", "parentUid": "26f506cb56eae899b201e1de8c38bd6f", "status": "passed", "time": {"start": 1755492480225, "stop": 1755492505070, "duration": 24845}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "26f506cb56eae899b201e1de8c38bd6f"}], "uid": "e1332233b220a65d2fdc0ae00a593bdc"}, {"name": "test_set_a_timer_for_minutes", "children": [{"name": "TestEllaSetTimerMinutes", "children": [{"name": "测试set a timer for 10 minutes能正常执行", "uid": "84cd193acbab3e9d", "parentUid": "db45051bfa0b770acce3d9a7a9b34c52", "status": "passed", "time": {"start": 1755492519120, "stop": 1755492552471, "duration": 33351}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "db45051bfa0b770acce3d9a7a9b34c52"}], "uid": "bea19f466921b3036054c922629fe91e"}, {"name": "test_set_alarm_for_10_o_clock", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set alarm for 10 o'clock", "uid": "afcefa8a3e8473ce", "parentUid": "a2954744eacf1d066c2b6177f8028ee2", "status": "failed", "time": {"start": 1755492566837, "stop": 1755492596225, "duration": 29388}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a2954744eacf1d066c2b6177f8028ee2"}], "uid": "5136d3ddad5af9fa04f4391a520c3b0d"}, {"name": "test_set_alarm_volume", "children": [{"name": "TestEllaOpenAlarmVolume", "children": [{"name": "测试set alarm volume 50", "uid": "ffe7bf6a0dfa964b", "parentUid": "535acae4f74c18b0779b5814e85551a0", "status": "failed", "time": {"start": 1755492611073, "stop": 1755492640588, "duration": 29515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "535acae4f74c18b0779b5814e85551a0"}], "uid": "0a72278cc105831b6f15d92180dbbb88"}, {"name": "test_set_battery_saver_setting", "children": [{"name": "TestEllaSetBatterySaverSetting", "children": [{"name": "测试set Battery Saver setting能正常执行", "uid": "b9026a212463de8f", "parentUid": "b61ff8d9b726452f92d852b371aa6a90", "status": "passed", "time": {"start": 1755492655050, "stop": 1755492676264, "duration": 21214}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b61ff8d9b726452f92d852b371aa6a90"}], "uid": "28b4581513f8589b378bc346015a9490"}, {"name": "test_set_my_alarm_volume_to", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set my alarm volume to 50%", "uid": "8d124ee927e189e3", "parentUid": "7bd8bf5fc2ce0d770e75c4bf3cc45a42", "status": "failed", "time": {"start": 1755492690620, "stop": 1755492720206, "duration": 29586}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7bd8bf5fc2ce0d770e75c4bf3cc45a42"}], "uid": "0379e8b63c35130d2bab51adda52041d"}, {"name": "test_set_notifications_volume_to", "children": [{"name": "TestEllaSetNotificationsVolume", "children": [{"name": "测试set notifications volume to 50能正常执行", "uid": "6b9d29ad9b68f533", "parentUid": "6f167c5f954c0926cc7ae79319b14e7f", "status": "failed", "time": {"start": 1755492734656, "stop": 1755492755099, "duration": 20443}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6f167c5f954c0926cc7ae79319b14e7f"}], "uid": "87989b97ae7186a62e6dca4f8cedbd9b"}, {"name": "test_set_ringtone_volume_to", "children": [{"name": "TestEllaSetRingtoneVolume", "children": [{"name": "测试set ringtone volume to 50能正常执行", "uid": "6a3c95959cdeda8f", "parentUid": "0203709156dd1c946a89769c23439df5", "status": "failed", "time": {"start": 1755492769756, "stop": 1755492790590, "duration": 20834}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0203709156dd1c946a89769c23439df5"}], "uid": "8f3477a6dc69416c1363c497ff0be6af"}, {"name": "test_set_screen_to_maximum_brightness", "children": [{"name": "TestEllaSetScreenMaximumBrightness", "children": [{"name": "测试set screen to maximum brightness能正常执行", "uid": "a7aa9e18a7a454b7", "parentUid": "812f1a5ab29405a0c012a793f3cdefdd", "status": "passed", "time": {"start": 1755492805464, "stop": 1755492826920, "duration": 21456}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "812f1a5ab29405a0c012a793f3cdefdd"}], "uid": "187df0f907fbbc3aa79736829f527dd8"}, {"name": "test_set_the_alarm_at_9_o_clock_on_weekends", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set the alarm at 9 o'clock on weekends", "uid": "520e6941e9933016", "parentUid": "c7ad33a108599c4ce5aa7f8ebd963f3e", "status": "failed", "time": {"start": 1755492841536, "stop": 1755492869129, "duration": 27593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7ad33a108599c4ce5aa7f8ebd963f3e"}], "uid": "aad6e9f36af0a7bd9c931bff6b3263e9"}, {"name": "test_smart_charge", "children": [{"name": "TestEllaSmartCharge", "children": [{"name": "测试smart charge能正常执行", "uid": "cff3b4cbfa4db88a", "parentUid": "41f573a06c0e20aa83d29445f512857c", "status": "failed", "time": {"start": 1755492884081, "stop": 1755492904872, "duration": 20791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41f573a06c0e20aa83d29445f512857c"}], "uid": "0669c1c9bf3fff5019832bc329d03550"}, {"name": "test_start_record", "children": [{"name": "TestEllaStartRecord", "children": [{"name": "测试start record能正常执行", "uid": "91de0985ae36a1fe", "parentUid": "fad9085499f3f4042c08d5be02eb6b0a", "status": "passed", "time": {"start": 1755492919728, "stop": 1755492948544, "duration": 28816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "b5dd074b2c9c41b6", "parentUid": "fad9085499f3f4042c08d5be02eb6b0a", "status": "failed", "time": {"start": 1755492963161, "stop": 1755492989408, "duration": 26247}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fad9085499f3f4042c08d5be02eb6b0a"}], "uid": "0d2acbf80a2bbd78f39946ab4d920635"}, {"name": "test_start_screen_recording", "children": [{"name": "TestEllaStartScreenRecording", "children": [{"name": "测试start screen recording能正常执行", "uid": "911ef56e93154a6e", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1755493004364, "stop": 1755493030148, "duration": 25784}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "729eed61791867c6", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1755493045101, "stop": 1755493068990, "duration": 23889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "350286206c706767", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1755493083377, "stop": 1755493109965, "duration": 26588}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "e141d666e22735b1", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1755493124347, "stop": 1755493150306, "duration": 25959}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "94605bd37b622971e2f6d1306d537ac9"}], "uid": "9210a41e638d65f3b50b4de9468577cb"}, {"name": "test_stop_recording", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "e3c404ed4bb57196", "parentUid": "6d9aa92a00ed0065f1758dce8f814a21", "status": "passed", "time": {"start": 1755493164991, "stop": 1755493194263, "duration": 29272}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "ea730b2eeebc920e", "parentUid": "6d9aa92a00ed0065f1758dce8f814a21", "status": "passed", "time": {"start": 1755493208731, "stop": 1755493231909, "duration": 23178}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d9aa92a00ed0065f1758dce8f814a21"}], "uid": "12d9ec9f3661e4a6cbb3393c17a142c0"}, {"name": "test_switch_charging_modes", "children": [{"name": "TestEllaSwitchChargingModes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "391ec1d93138b383", "parentUid": "9788e3a9971a6e6142c9edebcd8d6df5", "status": "failed", "time": {"start": 1755493246299, "stop": 1755493267414, "duration": 21115}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9788e3a9971a6e6142c9edebcd8d6df5"}], "uid": "9df29768698803a92c70ca28b324bcd6"}, {"name": "test_switch_magic_voice_to_grace", "children": [{"name": "TestEllaSwitchMagicVoiceGrace", "children": [{"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "1da11297c221aa88", "parentUid": "a68eab3c6aa1e1b791a77b31c94dd2f2", "status": "passed", "time": {"start": 1755493282358, "stop": 1755493304443, "duration": 22085}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a68eab3c6aa1e1b791a77b31c94dd2f2"}], "uid": "ee64f637be288a86beb1fb3a41d0f889"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "TestEllaSwitchMagicVoiceToMango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "79e6f5dd0a5829a6", "parentUid": "f890978c47c61f26513c6a80d7cd0497", "status": "passed", "time": {"start": 1755493318975, "stop": 1755493340374, "duration": 21399}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f890978c47c61f26513c6a80d7cd0497"}], "uid": "04bb98199ad164d25c90308e2b594563"}, {"name": "test_switch_to_barrage_notification", "children": [{"name": "TestEllaSwitchBarrageNotification", "children": [{"name": "测试Switch to Barrage Notification能正常执行", "uid": "cdbb62d38624064f", "parentUid": "4e744fe5421b1f85b2b48648f841c620", "status": "passed", "time": {"start": 1755493354937, "stop": 1755493377857, "duration": 22920}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4e744fe5421b1f85b2b48648f841c620"}], "uid": "81d75a279e51195a6bdcc1b7476061cc"}, {"name": "test_switch_to_default_mode", "children": [{"name": "TestEllaSwitchToDefaultMode", "children": [{"name": "测试switch to default mode能正常执行", "uid": "14b0ac6aa516cfc", "parentUid": "7b329a8fe9abef3ed9e84e7ba123812d", "status": "failed", "time": {"start": 1755493392051, "stop": 1755493413114, "duration": 21063}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b329a8fe9abef3ed9e84e7ba123812d"}], "uid": "4c06db8cd2466afbff036b96a7b29084"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchToEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "43ef1df42706b5f2", "parentUid": "76faa1b8f60ce1117990613102818bb9", "status": "passed", "time": {"start": 1755493427898, "stop": 1755493451072, "duration": 23174}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "76faa1b8f60ce1117990613102818bb9"}], "uid": "a631118d3a602f7957b9df1f61e89d8b"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "TestEllaSwitchToFlashNotification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "ad2c40aef0eb132d", "parentUid": "28cdac4aed197c9ad9daa4c4660b871b", "status": "failed", "time": {"start": 1755493465877, "stop": 1755493487673, "duration": 21796}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "28cdac4aed197c9ad9daa4c4660b871b"}], "uid": "199a594d9e7fb8316cc2c05c01918204"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "TestEllaSwitchToHyperCharge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "b8607aa0c2a301f6", "parentUid": "65ad6f571b805fa05f9b55669abf1de1", "status": "failed", "time": {"start": 1755493502668, "stop": 1755493524813, "duration": 22145}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "65ad6f571b805fa05f9b55669abf1de1"}], "uid": "231bfdf13683acaff012f9cc475bdd12"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "TestEllaSwitchToLowtempCharge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "c61ed480a6d5fb82", "parentUid": "c943e664fb1e3d8f2e7feabc9d26cf9b", "status": "failed", "time": {"start": 1755493539874, "stop": 1755493560957, "duration": 21083}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c943e664fb1e3d8f2e7feabc9d26cf9b"}], "uid": "f274d94636e10c97050c0bced04829d1"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchToPowerSavingMode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "209faffdc0cf9c36", "parentUid": "fa491a2fccbb92902b5fea6a0125d0dd", "status": "passed", "time": {"start": 1755493575787, "stop": 1755493599245, "duration": 23458}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fa491a2fccbb92902b5fea6a0125d0dd"}], "uid": "276d2d0724438fcbfe51444d46cecee8"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "TestEllaSwitchToSmartCharge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "a3149f6a5e3b5e87", "parentUid": "f0ac9e1d670781c7273288548b5e912c", "status": "failed", "time": {"start": 1755493613879, "stop": 1755493634903, "duration": 21024}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0ac9e1d670781c7273288548b5e912c"}], "uid": "5dbf89ce49106a865cad1e16016f18aa"}, {"name": "test_switched_to_data_mode", "children": [{"name": "TestEllaSwitchedDataMode", "children": [{"name": "测试switched to data mode能正常执行", "uid": "9563fc1b2d7ac310", "parentUid": "ee2a0ba313d9f55972191c0fc58a643b", "status": "passed", "time": {"start": 1755493649715, "stop": 1755493673397, "duration": 23682}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ee2a0ba313d9f55972191c0fc58a643b"}], "uid": "7f43f619728469165153ad2076591cb3"}, {"name": "test_take_a_photo", "children": [{"name": "TestEllaTakePhoto", "children": [{"name": "测试take a photo能正常执行", "uid": "71476eaab257c12", "parentUid": "491b8997a74c0d96f03949ae2b53cb5a", "status": "passed", "time": {"start": 1755493687630, "stop": 1755493729442, "duration": 41812}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "491b8997a74c0d96f03949ae2b53cb5a"}], "uid": "cf46440006651ab12729bd35922e17b6"}, {"name": "test_take_a_selfie", "children": [{"name": "TestEllaTakeSelfie", "children": [{"name": "测试take a selfie能正常执行", "uid": "c6578d0be4f581fc", "parentUid": "4605cd6537361d8c73216c0b79b6bdcd", "status": "passed", "time": {"start": 1755493743763, "stop": 1755493783282, "duration": 39519}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4605cd6537361d8c73216c0b79b6bdcd"}], "uid": "458be1e68e81f7e0a5f24b2b8e711037"}, {"name": "test_the_battery_of_the_mobile_phone_is_too_low", "children": [{"name": "TestEllaBatteryMobilePhoneIsTooLow", "children": [{"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "4488dc5233d49315", "parentUid": "e9e63c79cb2e897c6c2ba74546e55ab8", "status": "passed", "time": {"start": 1755493797832, "stop": 1755493823777, "duration": 25945}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9e63c79cb2e897c6c2ba74546e55ab8"}], "uid": "7fec87272bec0f0286d76fc5e499fde9"}, {"name": "test_turn_down_alarm_clock_volume", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn down alarm clock volume", "uid": "cba7776fbf23f8ef", "parentUid": "7e0c9461d23a06cd9aa28527904d1c88", "status": "passed", "time": {"start": 1755493838419, "stop": 1755493870842, "duration": 32423}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e0c9461d23a06cd9aa28527904d1c88"}], "uid": "588ec2ce29d18bbaaaa1a5253cb13e96"}, {"name": "test_turn_down_notifications_volume", "children": [{"name": "TestEllaTurnDownNotificationsVolume", "children": [{"name": "测试turn down notifications volume能正常执行", "uid": "f8882631a98768d3", "parentUid": "edf5af7735521ef7862883fb4a916d84", "status": "broken", "time": {"start": 1755493885157, "stop": 1755493906253, "duration": 21096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "edf5af7735521ef7862883fb4a916d84"}], "uid": "4cb442df6e9a3d0d1994106adb6fd6d7"}, {"name": "test_turn_down_ring_volume", "children": [{"name": "TestEllaTurnDownRingVolume", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "e67b14d97324c4e8", "parentUid": "c650a0dd4c418a43693046a1806d4660", "status": "passed", "time": {"start": 1755493921112, "stop": 1755493943194, "duration": 22082}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c650a0dd4c418a43693046a1806d4660"}], "uid": "b8d3945e930e3eeebfc2c83a67a45522"}, {"name": "test_turn_down_the_brightness_to_the_min", "children": [{"name": "TestEllaTurnDownBrightnessMin", "children": [{"name": "测试turn down the brightness to the min能正常执行", "uid": "3c79f2c5305979dc", "parentUid": "3f254857babc69f7678dda5d0e62e204", "status": "passed", "time": {"start": 1755493958081, "stop": 1755493978603, "duration": 20522}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f254857babc69f7678dda5d0e62e204"}], "uid": "d0625ffcef34d9d50a229be4c254d554"}, {"name": "test_turn_off_adaptive_brightness", "children": [{"name": "TestEllaTurnOffAdaptiveBrightness", "children": [{"name": "测试turn off adaptive brightness能正常执行", "uid": "5a173ed6b092c53", "parentUid": "d9e598c2a2b064d69495870cd847b2b3", "status": "passed", "time": {"start": 1755493992335, "stop": 1755494013150, "duration": 20815}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9e598c2a2b064d69495870cd847b2b3"}], "uid": "69e5db1a1760a63aafa8296f1d05bfa4"}, {"name": "test_turn_off_auto_rotate_screen", "children": [{"name": "TestEllaTurnOffAutoRotateScreen", "children": [{"name": "测试turn off auto rotate screen能正常执行", "uid": "1708b50a022925a5", "parentUid": "d1e2e0480b55d89e14304cac0cca577a", "status": "failed", "time": {"start": 1755494027598, "stop": 1755494048166, "duration": 20568}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d1e2e0480b55d89e14304cac0cca577a"}], "uid": "577eb2cd9ec851e31856e504e0bd1722"}, {"name": "test_turn_off_flashlight", "children": [{"name": "TestEllaTurnOffFlashlight", "children": [{"name": "测试turn off flashlight能正常执行", "uid": "e67a7663ced8295b", "parentUid": "11b19c6407e932f014698c24c4c16b7b", "status": "passed", "time": {"start": 1755494062809, "stop": 1755494085699, "duration": 22890}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11b19c6407e932f014698c24c4c16b7b"}], "uid": "83abfdb472ff182fcf9c9f70441da7db"}, {"name": "test_turn_off_light_theme", "children": [{"name": "TestEllaTurnOffLightTheme", "children": [{"name": "测试turn off light theme能正常执行", "uid": "5471e52cd0826090", "parentUid": "7d565ca90fb003f1876217d97fc59c20", "status": "passed", "time": {"start": 1755494100287, "stop": 1755494120235, "duration": 19948}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d565ca90fb003f1876217d97fc59c20"}], "uid": "2907e63ef40baa3266bf04ea46042c06"}, {"name": "test_turn_off_nfc", "children": [{"name": "TestEllaTurnOffNfc", "children": [{"name": "测试turn off nfc能正常执行", "uid": "fbc3d56dd9367d19", "parentUid": "140cc3d324fc5f756712663d6db76c28", "status": "passed", "time": {"start": 1755494134610, "stop": 1755494155212, "duration": 20602}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "140cc3d324fc5f756712663d6db76c28"}], "uid": "47184378bab69b86d9722f52ee466ee3"}, {"name": "test_turn_off_smart_reminder", "children": [{"name": "TestEllaTurnOffSmartReminder", "children": [{"name": "测试turn off smart reminder能正常执行", "uid": "b517460583a5ecfb", "parentUid": "e7b8db2dc50016e560cf1ae045e75639", "status": "passed", "time": {"start": 1755494169748, "stop": 1755494190392, "duration": 20644}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e7b8db2dc50016e560cf1ae045e75639"}], "uid": "9c2fb62f5a3a109c1d35edda86e2a528"}, {"name": "test_turn_on_adaptive_brightness", "children": [{"name": "TestEllaTurnAdaptiveBrightness", "children": [{"name": "测试turn on adaptive brightness能正常执行", "uid": "4fdcaea029954f3b", "parentUid": "18e349660100e06c9a3418f109d515b3", "status": "failed", "time": {"start": 1755494204923, "stop": 1755494225635, "duration": 20712}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "18e349660100e06c9a3418f109d515b3"}], "uid": "a2e8262a15b67dbb0d1c8ff085d2cea3"}, {"name": "test_turn_on_airplane_mode", "children": [{"name": "TestEllaTurnAirplaneMode", "children": [{"name": "测试turn on airplane mode能正常执行", "uid": "fea69f0980519c29", "parentUid": "3939d2640f0007e7c5a07e19c0047830", "status": "failed", "time": {"start": 1755494240437, "stop": 1755494261161, "duration": 20724}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3939d2640f0007e7c5a07e19c0047830"}], "uid": "d230c62813c90fd78a5c44bcf0813b36"}, {"name": "test_turn_on_auto_rotate_screen", "children": [{"name": "TestEllaTurnAutoRotateScreen", "children": [{"name": "测试turn on auto rotate screen能正常执行", "uid": "b9e7c23ccbc12dce", "parentUid": "6d818f22aed2045062e8e87f5e1a7455", "status": "failed", "time": {"start": 1755494275918, "stop": 1755494296230, "duration": 20312}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d818f22aed2045062e8e87f5e1a7455"}], "uid": "2e1501efc6a3c64c1060caa80eaa6607"}, {"name": "test_turn_on_bluetooth", "children": [{"name": "TestEllaTurnBluetooth", "children": [{"name": "测试turn on bluetooth能正常执行", "uid": "7972db2b2bb137e3", "parentUid": "50b13d10c6f48bcd3b710698be278545", "status": "passed", "time": {"start": 1755494310879, "stop": 1755494330672, "duration": 19793}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "50b13d10c6f48bcd3b710698be278545"}], "uid": "73521b2a406c05e4ea6775f8ac550b11"}, {"name": "test_turn_on_brightness_to_80", "children": [{"name": "TestEllaTurnBrightness", "children": [{"name": "测试turn on brightness to 80能正常执行", "uid": "2faa1eee8a3aa9d0", "parentUid": "34d72bbcd3a4fe638c7b4bc56fe3e3af", "status": "passed", "time": {"start": 1755494344905, "stop": 1755494364672, "duration": 19767}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34d72bbcd3a4fe638c7b4bc56fe3e3af"}], "uid": "be633c533b133d5847b41035b5320d37"}, {"name": "test_turn_on_do_not_disturb_mode", "children": [{"name": "TestEllaTurnDoNotDisturbMode", "children": [{"name": "测试turn on do not disturb mode能正常执行", "uid": "284c47e961fd5f29", "parentUid": "b24c19041d3913a347b45ddb7794d996", "status": "passed", "time": {"start": 1755494378817, "stop": 1755494399764, "duration": 20947}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b24c19041d3913a347b45ddb7794d996"}], "uid": "25300102bed6a3f35a171590779f5760"}, {"name": "test_turn_on_light_theme", "children": [{"name": "TestEllaTurnLightTheme", "children": [{"name": "测试turn on light theme能正常执行", "uid": "f6ab1c2ea88d0024", "parentUid": "33d9ea5586f4fc177aea2dcfffa56adf", "status": "failed", "time": {"start": 1755494413985, "stop": 1755494433523, "duration": 19538}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "ccbcb71e423ab04e", "parentUid": "33d9ea5586f4fc177aea2dcfffa56adf", "status": "passed", "time": {"start": 1755494448052, "stop": 1755494467621, "duration": 19569}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33d9ea5586f4fc177aea2dcfffa56adf"}], "uid": "491955b9c16082a7c444ca2d26b7637c"}, {"name": "test_turn_on_location_services", "children": [{"name": "TestEllaTurnLocationServices", "children": [{"name": "测试turn on location services能正常执行", "uid": "506746f07ec7c902", "parentUid": "b0fdf160b2ecb52332e6867a9d6becde", "status": "failed", "time": {"start": 1755494482071, "stop": 1755494501829, "duration": 19758}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0fdf160b2ecb52332e6867a9d6becde"}], "uid": "cf19f0ebd19888cf46f82f4e0bd4bfac"}, {"name": "test_turn_on_nfc", "children": [{"name": "TestEllaTurnNfc", "children": [{"name": "测试turn on nfc能正常执行", "uid": "2a20d290ca467f0d", "parentUid": "125b66f5b4fa0498985a1eb2ee60c6e7", "status": "failed", "time": {"start": 1755494516417, "stop": 1755494535770, "duration": 19353}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "125b66f5b4fa0498985a1eb2ee60c6e7"}], "uid": "5208de3eaa24aeb389307a172f7857eb"}, {"name": "test_turn_on_smart_reminder", "children": [{"name": "TestEllaTurnSmartReminder", "children": [{"name": "测试turn on smart reminder能正常执行", "uid": "fab7fcbbd2e8922", "parentUid": "5c2a01e86a7808eb62821ab670dc1654", "status": "failed", "time": {"start": 1755494550218, "stop": 1755494570448, "duration": 20230}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5c2a01e86a7808eb62821ab670dc1654"}], "uid": "d3a8bf8b5d3c108f49bd07343856e0a3"}, {"name": "test_turn_on_the_7am_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn on the 7AM alarm", "uid": "7728451f1dfab02c", "parentUid": "406e19372ea231bcfb9f9bf7eee96e74", "status": "passed", "time": {"start": 1755494585004, "stop": 1755494612849, "duration": 27845}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "406e19372ea231bcfb9f9bf7eee96e74"}], "uid": "ff509714e5ac2f6372d3841cb9423f82"}, {"name": "test_turn_on_the_flashlight", "children": [{"name": "TestEllaTurnFlashlight", "children": [{"name": "测试turn on the flashlight能正常执行", "uid": "e1e98f02734c9d61", "parentUid": "a46ae606b16354d85bcc730dcdcbe96e", "status": "passed", "time": {"start": 1755494627022, "stop": 1755494649704, "duration": 22682}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a46ae606b16354d85bcc730dcdcbe96e"}], "uid": "5ff8e41b3404a9b3f6cce0bee037eb96"}, {"name": "test_turn_on_the_screen_record", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "6b5024390c60d478", "parentUid": "678c4f3bd7c3cae1c7216386baae6d8b", "status": "passed", "time": {"start": 1755494664270, "stop": 1755494689073, "duration": 24803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "650a1e3e10f46f5a", "parentUid": "678c4f3bd7c3cae1c7216386baae6d8b", "status": "passed", "time": {"start": 1755494703631, "stop": 1755494728665, "duration": 25034}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "678c4f3bd7c3cae1c7216386baae6d8b"}], "uid": "62efa7d4bd969e35ac386aa7097579c5"}, {"name": "test_turn_on_wifi", "children": [{"name": "TestEllaTurnWifi", "children": [{"name": "测试turn on wifi能正常执行", "uid": "edb4f252b7f5ccca", "parentUid": "6628c4e31fae719ad94c679a246d743e", "status": "failed", "time": {"start": 1755494742747, "stop": 1755494763728, "duration": 20981}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6628c4e31fae719ad94c679a246d743e"}], "uid": "8a973098444298c6ff004df8ce42b561"}, {"name": "test_turn_up_alarm_clock_volume", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn up alarm clock volume", "uid": "9471c23f067d080c", "parentUid": "1fd57584780ed00e02e485b64ab749a0", "status": "failed", "time": {"start": 1755494778073, "stop": 1755494806657, "duration": 28584}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1fd57584780ed00e02e485b64ab749a0"}], "uid": "5550d272d18d8c3628b89d44f867786a"}, {"name": "test_turn_up_notifications_volume", "children": [{"name": "TestEllaTurnUpNotificationsVolume", "children": [{"name": "测试turn up notifications volume能正常执行", "uid": "1b1be6b94c7951b3", "parentUid": "a0f5108760ffcb32a71bed828c69ed5b", "status": "failed", "time": {"start": 1755494821239, "stop": 1755494840508, "duration": 19269}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0f5108760ffcb32a71bed828c69ed5b"}], "uid": "ad092db19588044c41fa1bcfe3390ec4"}, {"name": "test_turn_up_ring_volume", "children": [{"name": "TestEllaTurnUpRingVolume", "children": [{"name": "测试turn up ring volume能正常执行", "uid": "d1f779592775f55d", "parentUid": "a503cc046614ff4a66a4e07f8b13e370", "status": "failed", "time": {"start": 1755494855200, "stop": 1755494876035, "duration": 20835}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a503cc046614ff4a66a4e07f8b13e370"}], "uid": "a719872171e528ab034126de6c2a6628"}, {"name": "test_turn_up_the_brightness_to_the_max", "children": [{"name": "TestEllaTurnUpBrightnessMax", "children": [{"name": "测试turn up the brightness to the max能正常执行", "uid": "43411a23d2e7e0a2", "parentUid": "82bbd77195f12ce6b645cd49594ab92a", "status": "passed", "time": {"start": 1755494890601, "stop": 1755494910568, "duration": 19967}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "82bbd77195f12ce6b645cd49594ab92a"}], "uid": "22a701d2a2e514a3be7d5ed569e7a767"}, {"name": "test_turn_up_the_volume_to_the_max", "children": [{"name": "TestEllaTurnUpVolumeMax", "children": [{"name": "测试turn up the volume to the max能正常执行", "uid": "5840f511edbe2418", "parentUid": "dff7e9ecb9b310d1c8c562cc8438cb43", "status": "failed", "time": {"start": 1755494924832, "stop": 1755494944364, "duration": 19532}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dff7e9ecb9b310d1c8c562cc8438cb43"}], "uid": "ab410675942805398e84ee27463212f4"}, {"name": "test_wake_me_up_at_am_tomorrow", "children": [{"name": "TestEllaWakeMeUpAmTomorrow", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "7069d39903a118ae", "parentUid": "a95ef973249db47161b0248e62a09176", "status": "failed", "time": {"start": 1755494959142, "stop": 1755494978335, "duration": 19193}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a95ef973249db47161b0248e62a09176"}], "uid": "19346142b5a8f43c02ff8d5890db9067"}, {"name": "test_where_is_the_carlcare_service_outlet", "children": [{"name": "TestEllaWhereIsCarlcareServiceOutlet", "children": [{"name": "测试where is the carlcare service outlet能正常执行", "uid": "7564d1515ce0e54c", "parentUid": "ca0e45e31e627eb4f9d8c39d6a6bf138", "status": "failed", "time": {"start": 1755494992988, "stop": 1755495013872, "duration": 20884}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca0e45e31e627eb4f9d8c39d6a6bf138"}], "uid": "7d4ef22a8f804451209374dfe8bf64ad"}], "uid": "4159dc35ce06d1422bb1b7c5665d834a"}, {"name": "testcases.test_ella.third_coupling", "children": [{"name": "test_download_app", "children": [{"name": "TestEllaDownloadApp", "children": [{"name": "测试download app能正常执行", "uid": "ed36ea751cd0e957", "parentUid": "3437a290da1a678bac85f4666839d881", "status": "failed", "time": {"start": 1755495028615, "stop": 1755495050864, "duration": 22249}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3437a290da1a678bac85f4666839d881"}], "uid": "ddb6cf5770aedc759f08823b3e6d8b65"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball能正常执行", "uid": "c81f08c0cb37b1f7", "parentUid": "588cc0c6328e2e8a712130edc3aed897", "status": "failed", "time": {"start": 1755495065128, "stop": 1755495087257, "duration": 22129}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "588cc0c6328e2e8a712130edc3aed897"}], "uid": "1d4ceb2f76f4a7e0f4095f5c1f138613"}, {"name": "test_download_qq", "children": [{"name": "TestEllaDownloadQq", "children": [{"name": "测试download qq能正常执行", "uid": "fd332225564f8747", "parentUid": "510be9da11cf469597d4e78dac5a719b", "status": "failed", "time": {"start": 1755495102163, "stop": 1755495123208, "duration": 21045}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "510be9da11cf469597d4e78dac5a719b"}], "uid": "e47ca49712e495f1dd67541fd74682ac"}, {"name": "test_find_a_restaurant_near_me", "children": [{"name": "TestEllaFindRestaurantNearMe", "children": [{"name": "测试find a restaurant near me能正常执行", "uid": "912922ba8c35081a", "parentUid": "2a2a6349a33f526f9659f59faee83c46", "status": "failed", "time": {"start": 1755495138083, "stop": 1755495163575, "duration": 25492}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a2a6349a33f526f9659f59faee83c46"}], "uid": "6ecd9aa1e47da9fc316c168ef75730c9"}, {"name": "test_navigate_from_beijing_to_shanghai", "children": [{"name": "TestEllaNavigateFromBeijingShanghai", "children": [{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "b865efb985f8433d", "parentUid": "d7122eb175471ed23416d503cb147f88", "status": "failed", "time": {"start": 1755495178062, "stop": 1755495203841, "duration": 25779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7122eb175471ed23416d503cb147f88"}], "uid": "7b053180829d8e59af204f55867fbbbe"}, {"name": "test_navigate_from_to_red_square", "children": [{"name": "TestEllaNavigateFromRedSquare", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "8c4c3d95b1e7cab9", "parentUid": "e6c951f1c462cc23dddd03e98f8d3a27", "status": "failed", "time": {"start": 1755495218368, "stop": 1755495243930, "duration": 25562}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e6c951f1c462cc23dddd03e98f8d3a27"}], "uid": "f52e70464a63502b53d063b021cf198a"}, {"name": "test_navigate_to_shanghai_disneyland", "children": [{"name": "TestEllaNavigateShanghaiDisneyland", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "ddee4e71e5bec785", "parentUid": "5ae1d6975695489eb2d69ce256cf164f", "status": "failed", "time": {"start": 1755495258463, "stop": 1755495284372, "duration": 25909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5ae1d6975695489eb2d69ce256cf164f"}], "uid": "1dbef048a8c2c8a81b66d201495c6825"}, {"name": "test_navigation_to_the_lucky", "children": [{"name": "TestEllaNavigationToTheLucky", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "b664f8d7d1f71f7e", "parentUid": "719ca28cb458f784122e2efc4135056f", "status": "failed", "time": {"start": 1755495298831, "stop": 1755495324177, "duration": 25346}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "719ca28cb458f784122e2efc4135056f"}], "uid": "de0db7b40aca346332d2b0972719957a"}, {"name": "test_open_facebook", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open facebook能正常执行", "uid": "a0b415653780924f", "parentUid": "7b0b3aea4bca28c8189ca6a4be130237", "status": "passed", "time": {"start": 1755495338578, "stop": 1755495365164, "duration": 26586}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0b3aea4bca28c8189ca6a4be130237"}], "uid": "e4224d3534251a2780344938a85b3c06"}, {"name": "test_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试open whatsapp", "uid": "ac6e3c740d8f0174", "parentUid": "e65b3469b422d0d9909fee2c8bfa0960", "status": "failed", "time": {"start": 1755495379035, "stop": 1755495399598, "duration": 20563}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e65b3469b422d0d9909fee2c8bfa0960"}], "uid": "5aeebca39d474445d6f5ba2265081895"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试order a burger能正常执行", "uid": "b08b47535b38df0", "parentUid": "43a31fc74a106d981123a4cb94e40290", "status": "failed", "time": {"start": 1755495413969, "stop": 1755495434773, "duration": 20804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43a31fc74a106d981123a4cb94e40290"}], "uid": "3e2cc02d0935d06e8fb6f72bae4c8c83"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderATakeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "6698044fc356cdfd", "parentUid": "d9e81a960347bb8bb7bfbcc70b663c4b", "status": "failed", "time": {"start": 1755495449245, "stop": 1755495471155, "duration": 21910}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9e81a960347bb8bb7bfbcc70b663c4b"}], "uid": "24271799b32debb7d5f05bce65733a10"}, {"name": "test_pls_open_the_newest_whatsapp_activity", "children": [{"name": "TestEllaOpenPlsNewestWhatsappActivity", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "872544f9e9fd08ba", "parentUid": "5022c6b03e034f068f4ddbc7533f4c29", "status": "failed", "time": {"start": 1755495492039, "stop": 1755495512964, "duration": 20925}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5022c6b03e034f068f4ddbc7533f4c29"}], "uid": "9b7c89d46b9b721d197765453f1675de"}, {"name": "test_whatsapp", "children": [{"name": "TestEllaW<PERSON>sapp", "children": [{"name": "测试whatsapp能正常执行", "uid": "dc97a20445a73af", "parentUid": "edf22212c64c387b5f0b05436a17000b", "status": "passed", "time": {"start": 1755495527342, "stop": 1755495548491, "duration": 21149}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "edf22212c64c387b5f0b05436a17000b"}], "uid": "0438f804905f59a497b4803048f51319"}], "uid": "2660f6320a566ad526d6ea679fb2528f"}, {"name": "testcases.test_ella.unsupported_commands", "children": [{"name": "test_Add_the_images_and_text_on_the_screen_to_the_note", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "8f972b9e1b77bee1", "parentUid": "433b944b4500addcf62906018e50a6af", "status": "failed", "time": {"start": 1755495562726, "stop": 1755495581886, "duration": 19160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "433b944b4500addcf62906018e50a6af"}], "uid": "3d0102c3a98807edcea83109224019cc"}, {"name": "test_Language_List", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Language List", "uid": "e5b6ab6d55551330", "parentUid": "e250a4f9b681f22448fa4597aa4785c6", "status": "failed", "time": {"start": 1755495596462, "stop": 1755495615739, "duration": 19277}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e250a4f9b681f22448fa4597aa4785c6"}], "uid": "cd62e30e09154e5270f2e561d5a6d682"}, {"name": "test_a_clear_and_pink_crystal_necklace_in_the_water", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试a clear and pink crystal necklace in the water", "uid": "6a4c8523ed534ea7", "parentUid": "b04da96ca2c5ffb3e178b6044ed98663", "status": "failed", "time": {"start": 1755495630162, "stop": 1755495653322, "duration": 23160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b04da96ca2c5ffb3e178b6044ed98663"}], "uid": "dd8dabc47df69c7ff6925525834e6b5d"}, {"name": "test_a_clear_glass_cup", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试a clear glass cup", "uid": "67af36d833e6ea53", "parentUid": "a703f3440f4f5dc5b77cb183f8f6cc33", "status": "failed", "time": {"start": 1755495667937, "stop": 1755495689285, "duration": 21348}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a703f3440f4f5dc5b77cb183f8f6cc33"}], "uid": "9ecdd746f41031bcebbe8fcee2412b97"}, {"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A cute little boy is skiing", "uid": "ff9cf571c0e69633", "parentUid": "f895c42cd72820018e87de547b49f01b", "status": "failed", "time": {"start": 1755495703796, "stop": 1755495725963, "duration": 22167}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f895c42cd72820018e87de547b49f01b"}], "uid": "ad855646f44d184e26182c391db68429"}, {"name": "test_a_cute_little_girl_with_long_hair", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "uid": "2becd5b57d7a4c07", "parentUid": "792bccff1df1231fa08787f8c68c7def", "status": "passed", "time": {"start": 1755495740661, "stop": 1755495763344, "duration": 22683}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "792bccff1df1231fa08787f8c68c7def"}], "uid": "19cc2704894932f7b7b1883c73fe3eaf"}, {"name": "test_a_furry_little_monkey", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A furry little monkey", "uid": "4da3b33db15cae6e", "parentUid": "66f57b2040dff8d94d6ac334f412f4d6", "status": "passed", "time": {"start": 1755495777580, "stop": 1755495800781, "duration": 23201}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66f57b2040dff8d94d6ac334f412f4d6"}], "uid": "cea6f1c7ff4800312176bb95faa46b88"}, {"name": "test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "uid": "e66bce615188d1d4", "parentUid": "812414d61916005d6c107a49d6ad0d1c", "status": "passed", "time": {"start": 1755495815218, "stop": 1755495839049, "duration": 23831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "812414d61916005d6c107a49d6ad0d1c"}], "uid": "73eb730ca2fadb0d731cb550db1dcb8d"}, {"name": "test_a_little_raccoon_walks_on_a_forest_meadow", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "uid": "ffd81a84f61ca800", "parentUid": "440f43d7ca93715102a4152c367beb85", "status": "passed", "time": {"start": 1755495853365, "stop": 1755495876323, "duration": 22958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "440f43d7ca93715102a4152c367beb85"}], "uid": "762e5eeb5b55d5b1b3ad62536c184335"}, {"name": "test_a_photo_of_a_transparent_glass_cup", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A photo of a transparent glass cup ", "uid": "3499b9f6e42a76b", "parentUid": "f6966cfdf0f4a39d70c6a79f72fbd7a8", "status": "passed", "time": {"start": 1755495890875, "stop": 1755495912294, "duration": 21419}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f6966cfdf0f4a39d70c6a79f72fbd7a8"}], "uid": "1930ae6709e88717ab36f53803600401"}, {"name": "test_a_sports_car_is_parked_on_the_street_side", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A sports car is parked on the street side", "uid": "dcbdd9b25bac60dc", "parentUid": "82cf8bf878579c226c80fc451c912eb2", "status": "failed", "time": {"start": 1755495926808, "stop": 1755495949771, "duration": 22963}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "82cf8bf878579c226c80fc451c912eb2"}], "uid": "3d50309a5a87be1cb2c1d3d7f9fe365c"}, {"name": "test_call_mom", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试call mom", "uid": "6757ac4ac3bd0d5", "parentUid": "77b1d17992bf49b776313e0563c2514f", "status": "failed", "time": {"start": 1755495964254, "stop": 1755495989024, "duration": 24770}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77b1d17992bf49b776313e0563c2514f"}], "uid": "12a9c3dc55db996f79320d1f35dc1770"}, {"name": "test_call_number_by_whatsapp", "children": [{"name": "TestEllaCallNumberWhatsapp", "children": [{"name": "测试call number by whatsapp能正常执行", "uid": "b5d45a222a1bf25a", "parentUid": "6fc746e24aa09c4f4b982efdbdb53b0a", "status": "failed", "time": {"start": 1755496003786, "stop": 1755496028196, "duration": 24410}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6fc746e24aa09c4f4b982efdbdb53b0a"}], "uid": "d372067d12100f89f325e18e4635c766"}, {"name": "test_can_u_check_the_notebook", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试can u check the notebook", "uid": "4a26301ac4389f00", "parentUid": "25272118128dbe8d4265e3373095afa7", "status": "passed", "time": {"start": 1755496042787, "stop": 1755496075183, "duration": 32396}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25272118128dbe8d4265e3373095afa7"}], "uid": "1292c0e244c7c6fca5019f141d34ac19"}, {"name": "test_change_female_tone_name_voice", "children": [{"name": "TestEllaChangeFemaleToneNameVoice", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "c05ea50a96c6d6b7", "parentUid": "b565d06b86fa223901766faddeda207a", "status": "passed", "time": {"start": 1755496089361, "stop": 1755496109052, "duration": 19691}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b565d06b86fa223901766faddeda207a"}], "uid": "66562456c81152cc1fa9d09032ae884d"}, {"name": "test_change_man_voice", "children": [{"name": "TestEllaChangeManVoice", "children": [{"name": "测试change man voice能正常执行", "uid": "6a23b0c00399f863", "parentUid": "9211e5358ec84e06d14d678e7d922690", "status": "passed", "time": {"start": 1755496123463, "stop": 1755496143376, "duration": 19913}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9211e5358ec84e06d14d678e7d922690"}], "uid": "2d491290e2846a176599f1eae33a3ad3"}, {"name": "test_change_your_voice", "children": [{"name": "TestEllaChangeYourVoice", "children": [{"name": "测试change your voice能正常执行", "uid": "cdaf82c1cfc41208", "parentUid": "0d45916b91412c59944b54b906868410", "status": "passed", "time": {"start": 1755496157771, "stop": 1755496177801, "duration": 20030}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d45916b91412c59944b54b906868410"}], "uid": "99d1138eba2f3e2cb20c8824270684bb"}, {"name": "test_check_battery_information", "children": [{"name": "TestEllaCheckBatteryInformation", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "e1091f928abe2f4e", "parentUid": "d28e0e4ff0e4838a6027dc47e3b3a670", "status": "passed", "time": {"start": 1755496192237, "stop": 1755496212179, "duration": 19942}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d28e0e4ff0e4838a6027dc47e3b3a670"}], "uid": "4c906a8c1578240415aebe31f410f234"}, {"name": "test_check_contact", "children": [{"name": "TestEllaCheckContact", "children": [{"name": "测试check contact能正常执行", "uid": "5081078a491c2abe", "parentUid": "7840d5d29bd3ef68fd5883a60ba4d483", "status": "passed", "time": {"start": 1755496226465, "stop": 1755496251176, "duration": 24711}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7840d5d29bd3ef68fd5883a60ba4d483"}], "uid": "26722106422710fc718c70034be7a8c2"}, {"name": "test_check_contacts", "children": [{"name": "TestEllaCheckContacts", "children": [{"name": "测试check contacts能正常执行", "uid": "f0083b6882568b62", "parentUid": "7b0c7a58c89c6e54945f60f9f70c7cd2", "status": "passed", "time": {"start": 1755496265650, "stop": 1755496290580, "duration": 24930}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0c7a58c89c6e54945f60f9f70c7cd2"}], "uid": "52118859006a30586241bd2a4a26847c"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "TestEllaCheckMobileDataBalanceSim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "5de1b204ab8292d6", "parentUid": "2e50c8269684c4a73bb5b020614c2221", "status": "failed", "time": {"start": 1755496304718, "stop": 1755496324381, "duration": 19663}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2e50c8269684c4a73bb5b020614c2221"}], "uid": "ff56d23044bd5d499190e5ca173df4d3"}, {"name": "test_check_model_information", "children": [{"name": "TestEllaCheckModelInformation", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "f343337a7260b4dc", "parentUid": "f7e3ca8a9343a9ed054471fe3e0356b4", "status": "passed", "time": {"start": 1755496338662, "stop": 1755496358528, "duration": 19866}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f7e3ca8a9343a9ed054471fe3e0356b4"}], "uid": "bf2cc0fa355876e3be500d0c5ba7a5f1"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "TestEllaCheckMyBalanceSim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "40e869175e17f9c", "parentUid": "f9995b28dafd2b1ed8762fedcb92c132", "status": "passed", "time": {"start": 1755496372821, "stop": 1755496392201, "duration": 19380}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9995b28dafd2b1ed8762fedcb92c132"}], "uid": "ffd79a31b3c86841ac0cc6d5fa40f471"}, {"name": "test_check_my_to_do_list", "children": [{"name": "TestEllaCheckMyDoList", "children": [{"name": "测试check my to-do list能正常执行", "uid": "380eda55eb3e533d", "parentUid": "43310d5383f9f3391eb3ba14577fd4d7", "status": "passed", "time": {"start": 1755496406106, "stop": 1755496425612, "duration": 19506}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43310d5383f9f3391eb3ba14577fd4d7"}], "uid": "20383a2eb3e29a6c6ccd53f0bce07fe1"}, {"name": "test_check_ram_information", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试check ram information", "uid": "f291c191cd068630", "parentUid": "c46ca58f39fd573de3f84a18c7b2e1e9", "status": "failed", "time": {"start": 1755496439848, "stop": 1755496459351, "duration": 19503}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c46ca58f39fd573de3f84a18c7b2e1e9"}], "uid": "91aea13b936df1ab314bc65e1546585f"}, {"name": "test_check_rear_camera_information", "children": [{"name": "TestEllaCheckRearCameraInformation", "children": [{"name": "测试check rear camera information能正常执行", "uid": "604ad5d0d288c7a9", "parentUid": "89f05870118719c157701595fa592ca6", "status": "failed", "time": {"start": 1755496473969, "stop": 1755496493684, "duration": 19715}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "89f05870118719c157701595fa592ca6"}], "uid": "3e2d1f7b0da0ab237b35604822208008"}, {"name": "test_check_system_update", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试check system update", "uid": "b6bdba3a7fac2838", "parentUid": "6c6a21d7737144a2d795582971e816d5", "status": "failed", "time": {"start": 1755496508237, "stop": 1755496527656, "duration": 19419}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6c6a21d7737144a2d795582971e816d5"}], "uid": "68e3fa15aac7bb6f77f9d22e4bb16c45"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "TestEllaCloseEquilibriumMode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "7aa33dc6346e497d", "parentUid": "2ca6423e5b9b0595a667d74916b87ae8", "status": "passed", "time": {"start": 1755496542275, "stop": 1755496561944, "duration": 19669}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ca6423e5b9b0595a667d74916b87ae8"}], "uid": "cf0d3a245849cc3d2fb49b5919a2ca70"}, {"name": "test_close_performance_mode", "children": [{"name": "TestEllaClosePerformanceMode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "3dc6c9621080753b", "parentUid": "bfc98a3e31cf71b24d2c2664874d8f35", "status": "passed", "time": {"start": 1755496576199, "stop": 1755496595659, "duration": 19460}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfc98a3e31cf71b24d2c2664874d8f35"}], "uid": "921c03c2af17c0dd8bb300d05ebc1435"}, {"name": "test_close_power_saving_mode", "children": [{"name": "TestEllaClosePowerSavingMode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "b46a87de54c4347a", "parentUid": "d7c06ff8b4a17dd9ae62ae957d96f702", "status": "passed", "time": {"start": 1755496609728, "stop": 1755496629475, "duration": 19747}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7c06ff8b4a17dd9ae62ae957d96f702"}], "uid": "de4693d49200ef3897dfa7e2a5aa8dbc"}, {"name": "test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "uid": "89f7719a53a2bc63", "parentUid": "2d1979bb5d67b6016ab6089eae182085", "status": "failed", "time": {"start": 1755496643683, "stop": 1755496665302, "duration": 21619}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d1979bb5d67b6016ab6089eae182085"}], "uid": "d6f0130a0822a0d9e083c98b99701be9"}, {"name": "test_dial_the_number_on_the_screen", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Dial the number on the screen", "uid": "8be90e8c859e6876", "parentUid": "25b184d1594691843e5a3d7441f02e73", "status": "failed", "time": {"start": 1755496679686, "stop": 1755496699340, "duration": 19654}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25b184d1594691843e5a3d7441f02e73"}], "uid": "72e0dd751330ef0df21d89f181051bb1"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "TestEllaDisableAccelerateDialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "4e5427c48277aaae", "parentUid": "a12fb87a3ef6191f6c5649ce65173837", "status": "passed", "time": {"start": 1755496713774, "stop": 1755496733278, "duration": 19504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a12fb87a3ef6191f6c5649ce65173837"}], "uid": "afbbdecfafcd893c4f7104365ff8935e"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "TestEllaDisableAllAiMagicBoxFeatures", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "9887a04d8566e90d", "parentUid": "696197cde92ea407d7be5d0ee1a43b1a", "status": "passed", "time": {"start": 1755496747596, "stop": 1755496767129, "duration": 19533}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "696197cde92ea407d7be5d0ee1a43b1a"}], "uid": "2b54914538eb635e492ddf923151d357"}, {"name": "test_disable_auto_pickup", "children": [{"name": "TestEllaDisableAutoPickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "b01a284348dc6800", "parentUid": "c20ac19fa762a9db14606dd531e82b8f", "status": "passed", "time": {"start": 1755496781520, "stop": 1755496801351, "duration": 19831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c20ac19fa762a9db14606dd531e82b8f"}], "uid": "bf014ddab53633d98f8104639ce1bc1b"}, {"name": "test_disable_brightness_locking", "children": [{"name": "TestEllaDisableBrightnessLocking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "eb943ac598b62ae4", "parentUid": "858fef1ffc1503318aa70e9c72d7b438", "status": "failed", "time": {"start": 1755496815606, "stop": 1755496835096, "duration": 19490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "858fef1ffc1503318aa70e9c72d7b438"}], "uid": "66c800c4edcde7ca93c0c123e93d1914"}, {"name": "test_disable_call_rejection", "children": [{"name": "TestEllaDisableCallRejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "618f97bb7f63f3e2", "parentUid": "3f640e07903d6f89a09553a61d6690c3", "status": "passed", "time": {"start": 1755496849485, "stop": 1755496874693, "duration": 25208}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f640e07903d6f89a09553a61d6690c3"}], "uid": "021f51382efc25258ec8ca7ef3573495"}, {"name": "test_disable_hide_notifications", "children": [{"name": "TestEllaDisableHideNotifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "a1b74f21920cb042", "parentUid": "9f132b3849693b61c2ca479bcafef282", "status": "passed", "time": {"start": 1755496888877, "stop": 1755496908506, "duration": 19629}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9f132b3849693b61c2ca479bcafef282"}], "uid": "7030498aad7e0e3d901754313db42a5c"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "96dd49778d0c4c7", "parentUid": "04c072a77ad52d90193c2ec6bc53a021", "status": "passed", "time": {"start": 1755496922866, "stop": 1755496942576, "duration": 19710}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04c072a77ad52d90193c2ec6bc53a021"}], "uid": "6f0070b209221eadb3d6425a33ecbff1"}, {"name": "test_disable_network_enhancement", "children": [{"name": "TestEllaDisableNetworkEnhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "42454a177c3a8e30", "parentUid": "fe480ad7296777a27bc8f2d53aa0fccf", "status": "passed", "time": {"start": 1755496956788, "stop": 1755496976420, "duration": 19632}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fe480ad7296777a27bc8f2d53aa0fccf"}], "uid": "afc5376e9464fb8921d2fbcf9e0d3bbc"}, {"name": "test_disable_running_lock", "children": [{"name": "TestEllaDisableRunningLock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "b6a9aaccc0115e5b", "parentUid": "8fe90804f66731578ca4d4ba9283a64c", "status": "passed", "time": {"start": 1755496990678, "stop": 1755497011783, "duration": 21105}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fe90804f66731578ca4d4ba9283a64c"}], "uid": "ad391be6683f94da1f0d53eaeb12f8a3"}, {"name": "test_disable_touch_optimization", "children": [{"name": "TestEllaDisableTouchOptimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "938a4df873a1b318", "parentUid": "a61e1b3af122a2fe511826a7e99c2e1b", "status": "passed", "time": {"start": 1755497025963, "stop": 1755497045637, "duration": 19674}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a61e1b3af122a2fe511826a7e99c2e1b"}], "uid": "8d875ca58978089e7102b409d8215741"}, {"name": "test_disable_unfreeze", "children": [{"name": "TestEllaDisableUnfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "cf3c5480c5f7b9db", "parentUid": "1e21c84af5bdef90eb5dee6bd5cf2635", "status": "passed", "time": {"start": 1755497059913, "stop": 1755497079816, "duration": 19903}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e21c84af5bdef90eb5dee6bd5cf2635"}], "uid": "b1772b071366ab2c77148d9716c8dcd9"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "TestEllaDisableZonetouchMaster", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "f4641168eac1c411", "parentUid": "21d3787e184cbd6bdfc49c456af0ad04", "status": "passed", "time": {"start": 1755497094112, "stop": 1755497114205, "duration": 20093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "21d3787e184cbd6bdfc49c456af0ad04"}], "uid": "f5c774dd2222acf0e4aa8f7b201f12b3"}, {"name": "test_document_summary", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试document summary", "uid": "c58ae92034ab5c53", "parentUid": "55ecaf20bdfc214b3beb4115008bc1b0", "status": "failed", "time": {"start": 1755497128531, "stop": 1755497148001, "duration": 19470}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55ecaf20bdfc214b3beb4115008bc1b0"}], "uid": "5400494a044a2925f5ca183f72565139"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "e1e0e6ef5950a214", "parentUid": "98ae41f5cdd53a2d20bf8172efd26083", "status": "failed", "time": {"start": 1755497162511, "stop": 1755497183673, "duration": 21162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98ae41f5cdd53a2d20bf8172efd26083"}], "uid": "314bfe9c667cd809cb0cc507bfcd14fc"}, {"name": "test_download_in_play_store", "children": [{"name": "TestEllaOpenGooglePlaystore", "children": [{"name": "测试download in play store", "uid": "21240f257da35cf6", "parentUid": "aaf0d4f406e1ee7e1d9281b54bf64677", "status": "passed", "time": {"start": 1755497198137, "stop": 1755497222758, "duration": 24621}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "aaf0d4f406e1ee7e1d9281b54bf64677"}], "uid": "492783bc91711ae1967d7743b73a2b1c"}, {"name": "test_download_in_playstore", "children": [{"name": "TestEllaOpenGooglePlaystore", "children": [{"name": "测试download in playstore", "uid": "f5910b6312c64eeb", "parentUid": "a7ddb4033678917acec14cb846bb3cb9", "status": "passed", "time": {"start": 1755497237022, "stop": 1755497260561, "duration": 23539}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7ddb4033678917acec14cb846bb3cb9"}], "uid": "abd982c695d6752ee4dc9bc4239152c6"}, {"name": "test_download_whatsapp", "children": [{"name": "TestEllaDownloadWhatsapp", "children": [{"name": "测试download whatsapp能正常执行", "uid": "438b148c98987bcc", "parentUid": "798b400fa18fd45acb82f54257cf6c35", "status": "passed", "time": {"start": 1755497274834, "stop": 1755497300842, "duration": 26008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "798b400fa18fd45acb82f54257cf6c35"}], "uid": "9d36aebc605ca5348c0fe133f762df22"}, {"name": "test_driving_mode", "children": [{"name": "TestEllaDrivingMode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "ac6c261df16fa973", "parentUid": "ef0bd4110e3386c3ad0f74515d6f200e", "status": "failed", "time": {"start": 1755497314879, "stop": 1755497334572, "duration": 19693}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef0bd4110e3386c3ad0f74515d6f200e"}], "uid": "514aae0515740321638cc56a86d0bdc5"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "TestEllaEnableAccelerateDialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "a86f0c6331d66a91", "parentUid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7", "status": "passed", "time": {"start": 1755497349215, "stop": 1755497369252, "duration": 20037}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7"}], "uid": "8c56ee95835e673fa28b34154afb4628"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "TestEllaEnableAllAiMagicBoxFeatures", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "21edfa2d93a4ff06", "parentUid": "8a587c12d6caed0b409b8971d49025ab", "status": "passed", "time": {"start": 1755497383560, "stop": 1755497403725, "duration": 20165}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8a587c12d6caed0b409b8971d49025ab"}], "uid": "68d379dd7f74dd5275f913b7eee03079"}, {"name": "test_enable_auto_pickup", "children": [{"name": "TestEllaEnableAutoPickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "f9a03571405bfb36", "parentUid": "97d17abb9dc1ccc8f46a34e06a32340c", "status": "passed", "time": {"start": 1755497418549, "stop": 1755497438074, "duration": 19525}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "97d17abb9dc1ccc8f46a34e06a32340c"}], "uid": "8c4650098d6069190cfba3c757ba45db"}, {"name": "test_enable_brightness_locking", "children": [{"name": "TestEllaEnableBrightnessLocking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "78e25780691074a5", "parentUid": "c400a86ee772ae2bb87a4d299fcffde3", "status": "passed", "time": {"start": 1755497452537, "stop": 1755497472192, "duration": 19655}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c400a86ee772ae2bb87a4d299fcffde3"}], "uid": "0e14ad27fc644f4f62c72d254e5d2d00"}, {"name": "test_enable_call_on_hold", "children": [{"name": "TestEllaEnableCallHold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "47a97ffc4113f69d", "parentUid": "6d79c2b8de7e0682e35d7e5c727c1ff8", "status": "passed", "time": {"start": 1755497486619, "stop": 1755497511525, "duration": 24906}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d79c2b8de7e0682e35d7e5c727c1ff8"}], "uid": "de334526c374af0073eb9eb6ddb0997a"}, {"name": "test_enable_call_rejection", "children": [{"name": "TestEllaEnableCallRejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "fe288e058f9786e6", "parentUid": "e26b43428a954a94928f7a25e76abac9", "status": "passed", "time": {"start": 1755497525905, "stop": 1755497550632, "duration": 24727}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e26b43428a954a94928f7a25e76abac9"}], "uid": "366922e4cbf42ddefd029f732cdd77bf"}, {"name": "test_enable_network_enhancement", "children": [{"name": "TestEllaEnableNetworkEnhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "f1f1e91e929dcf70", "parentUid": "c12a817d493d41c56a647cb659a7bb8c", "status": "passed", "time": {"start": 1755497564872, "stop": 1755497584467, "duration": 19595}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c12a817d493d41c56a647cb659a7bb8c"}], "uid": "040b928633962c521baae26a6f7ac7c5"}, {"name": "test_enable_running_lock", "children": [{"name": "TestEllaEnableRunningLock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "9d12063ebb288a1d", "parentUid": "e1c78a96555072edad6f93b7ff33937c", "status": "passed", "time": {"start": 1755497598928, "stop": 1755497620227, "duration": 21299}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1c78a96555072edad6f93b7ff33937c"}], "uid": "26ee37ce6707d6284177cfa0f1cae12c"}, {"name": "test_enable_touch_optimization", "children": [{"name": "TestEllaEnableTouchOptimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "4ec6630e05337997", "parentUid": "d84bfce90dcde3b576c695692fca0fa8", "status": "passed", "time": {"start": 1755497634544, "stop": 1755497654370, "duration": 19826}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d84bfce90dcde3b576c695692fca0fa8"}], "uid": "76d94ee4c89c6674e70da8c74116bad6"}, {"name": "test_enable_unfreeze", "children": [{"name": "TestEllaEnableUnfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "51fc2408e1b48e72", "parentUid": "c801d69cec54af644c884ba704111151", "status": "passed", "time": {"start": 1755497668850, "stop": 1755497688364, "duration": 19514}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c801d69cec54af644c884ba704111151"}], "uid": "63e313e7547561b2fa987da4d6072437"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "TestEllaEnableZonetouchMaster", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "4d5db14a3cdbfbb1", "parentUid": "99a67c39634063a502862411f9efdb5c", "status": "passed", "time": {"start": 1755497702872, "stop": 1755497722716, "duration": 19844}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "99a67c39634063a502862411f9efdb5c"}], "uid": "796eef53680203e2ca4133277e86230b"}, {"name": "test_end_exercising", "children": [{"name": "TestEllaEndExercising", "children": [{"name": "测试end exercising能正常执行", "uid": "5b8f76f9ef2d973c", "parentUid": "115b3d23b2b26d5aeb94233ec9b50278", "status": "passed", "time": {"start": 1755497737069, "stop": 1755497756990, "duration": 19921}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "115b3d23b2b26d5aeb94233ec9b50278"}], "uid": "9e440169e9a1bdfad60618870de9c8fe"}, {"name": "test_extend_the_image", "children": [{"name": "TestEllaExtendImage", "children": [{"name": "测试extend the image能正常执行", "uid": "f2597eda9e19a1f8", "parentUid": "58d8f715c93e890833f08e611da6c0f8", "status": "failed", "time": {"start": 1755497771401, "stop": 1755497791206, "duration": 19805}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58d8f715c93e890833f08e611da6c0f8"}], "uid": "600af0308ad3a3f35324296fc9a30562"}, {"name": "test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "uid": "3c7fe7646472c23b", "parentUid": "0c92e72df5f2847a61bf235a8cbfb696", "status": "failed", "time": {"start": 1755497806064, "stop": 1755497825479, "duration": 19415}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0c92e72df5f2847a61bf235a8cbfb696"}], "uid": "e4095cc936373937b75adee961d8e38b"}, {"name": "test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "uid": "60c5c39af4e431bd", "parentUid": "7ba63c9f0831876e7868658118b379e0", "status": "failed", "time": {"start": 1755497843381, "stop": 1755497863106, "duration": 19725}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7ba63c9f0831876e7868658118b379e0"}], "uid": "59ed61d309053c170ff49b9a0b0a0cf9"}, {"name": "test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "uid": "15425d13f5f3e6ac", "parentUid": "94d579da046850e9a9f4099b86418446", "status": "failed", "time": {"start": 1755497877576, "stop": 1755497897111, "duration": 19535}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "94d579da046850e9a9f4099b86418446"}], "uid": "4cbd0eaf1466a2337d2988e40aa92879"}, {"name": "test_generate_a_landscape_painting_image_for_me", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a landscape painting image for me", "uid": "62ad3951ae903186", "parentUid": "ee0382687cbc2480f8fb56bfdc6988b5", "status": "failed", "time": {"start": 1755497911818, "stop": 1755497931402, "duration": 19584}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ee0382687cbc2480f8fb56bfdc6988b5"}], "uid": "4a89b2d6e05cb420b3e45d39e3e11873"}, {"name": "test_generate_a_picture_in_the_night_forest_for_me", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a picture in the night forest for me", "uid": "33d6f35532fb2f0e", "parentUid": "29bcbb379958b5ef79b813fb44a1326d", "status": "failed", "time": {"start": 1755497945622, "stop": 1755497965451, "duration": 19829}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29bcbb379958b5ef79b813fb44a1326d"}], "uid": "5539864d890598bd6045117e5164ff8c"}, {"name": "test_generate_a_picture_of_a_jungle_stream_for_me", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a picture of a jungle stream for me", "uid": "39524bbe77ff3663", "parentUid": "64efa8e92443733dc5129597edc54be6", "status": "failed", "time": {"start": 1755497980095, "stop": 1755497999632, "duration": 19537}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "64efa8e92443733dc5129597edc54be6"}], "uid": "f50105097da8bcb12b8f5dedaf0bad03"}, {"name": "test_generate_an_image_of_a_chubby_orange_cat_chef", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "uid": "9d6e041a4e052a6d", "parentUid": "4c964ed913dcaffe60107b228a13db00", "status": "failed", "time": {"start": 1755498014336, "stop": 1755498033977, "duration": 19641}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c964ed913dcaffe60107b228a13db00"}], "uid": "289f3b5c438a3754f1a28cd5c558a3e4"}, {"name": "test_go_home", "children": [{"name": "TestEllaGoHome", "children": [{"name": "测试go home能正常执行", "uid": "5fb4ac38ac8412ad", "parentUid": "0be9216ae99d7aa91cd2e638009124bc", "status": "passed", "time": {"start": 1755498049088, "stop": 1755498069323, "duration": 20235}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0be9216ae99d7aa91cd2e638009124bc"}], "uid": "16160dbe85fde9f730f84128fbb2c0e8"}, {"name": "test_go_to_office", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试go to office", "uid": "4a5ec0a66b60f2b9", "parentUid": "7028d2f3448cec282ca8f3105e4c8f33", "status": "passed", "time": {"start": 1755498084214, "stop": 1755498104387, "duration": 20173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7028d2f3448cec282ca8f3105e4c8f33"}], "uid": "504bbde356e3f9fa2a477910d2015eb0"}, {"name": "test_gold_coin_rain", "children": [{"name": "TestEllaGoldCoinRain", "children": [{"name": "测试gold coin rain能正常执行", "uid": "49ccb8ead21b239", "parentUid": "598fb3f60bb3b81f588b4e3deb106ffb", "status": "passed", "time": {"start": 1755498119012, "stop": 1755498141101, "duration": 22089}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "598fb3f60bb3b81f588b4e3deb106ffb"}], "uid": "1457f71492e606ab225ab159042c5411"}, {"name": "test_hamster_mascot", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "uid": "4d7c1112f9245cc0", "parentUid": "89e8e9868beeeb07fc0e0fbe2be4a9d0", "status": "failed", "time": {"start": 1755498155240, "stop": 1755498176860, "duration": 21620}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "89e8e9868beeeb07fc0e0fbe2be4a9d0"}], "uid": "d3bf8265accab30a46b7d75c686f2a31"}, {"name": "test_happy_new_year", "children": [{"name": "TestEllaHappyNewYear", "children": [{"name": "测试happy new year能正常执行", "uid": "88a4b61efd7b7383", "parentUid": "451251d367048f9c53b98847c2e447f2", "status": "passed", "time": {"start": 1755498191416, "stop": 1755498214653, "duration": 23237}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "451251d367048f9c53b98847c2e447f2"}], "uid": "20bc3f1296c161aa14a0b88cee103de3"}, {"name": "test_hello_hello", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试hello hello能正常执行", "uid": "281d46b2ea7e677a", "parentUid": "c4bd0dc493936c3118cffb708b15bf23", "status": "passed", "time": {"start": 1755498228770, "stop": 1755498250782, "duration": 22012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c4bd0dc493936c3118cffb708b15bf23"}], "uid": "1dcfccb9f26aec07115507bd73d282ca"}, {"name": "test_help_generate_a_picture_of_ancient_city", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A furry little monkey", "uid": "6f13be11e2084573", "parentUid": "617ef942b40bce41f954f46e9fb75b28", "status": "failed", "time": {"start": 1755498264880, "stop": 1755498287132, "duration": 22252}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "617ef942b40bce41f954f46e9fb75b28"}], "uid": "badc3ee28f810a31dc67eac99bd95563"}, {"name": "test_help_me_generate_a_3D_rendered_picture_of_a_Song_style_palace_surrounded_by_auspicious_clouds_and_with_delicate_colors", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Help me generate a 3D rendered picture of a Song-style palace surrounded by auspicious clouds and with delicate colors", "uid": "9bed2611f2f7371b", "parentUid": "a4f75e5d41a94e3d63ed48a31ea2fceb", "status": "failed", "time": {"start": 1755498301618, "stop": 1755498321272, "duration": 19654}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a4f75e5d41a94e3d63ed48a31ea2fceb"}], "uid": "c71bb8bf79c76ba6e92b8198dd78783b"}, {"name": "test_help_me_generate_a_photo_of_a_fully_nude_boy", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Help me generate a photo of a fully nude boy", "uid": "2889d3c9c873348d", "parentUid": "46eb01687f7194f0aa1ede4453b7af2f", "status": "passed", "time": {"start": 1755498335568, "stop": 1755498355453, "duration": 19885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "46eb01687f7194f0aa1ede4453b7af2f"}], "uid": "21455ed3a8678b7521db5034330e1df0"}, {"name": "test_help_me_generate_a_picture_of_a_bamboo_forest_stream", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of a bamboo forest stream", "uid": "e548f96f2718102", "parentUid": "7235901e69e3e99f7e0269602086d57b", "status": "failed", "time": {"start": 1755498369552, "stop": 1755498389207, "duration": 19655}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7235901e69e3e99f7e0269602086d57b"}], "uid": "432a7b9e3eb1e42d010197fa3a645d7d"}, {"name": "test_help_me_generate_a_picture_of_a_puppy", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of a puppy", "uid": "552c1620bb9cc1e6", "parentUid": "86e19d5e96892c826e84d8b4f165e88b", "status": "failed", "time": {"start": 1755498403678, "stop": 1755498422882, "duration": 19204}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "86e19d5e96892c826e84d8b4f165e88b"}], "uid": "74d2cae50a42f7145e4e4b11e8372631"}, {"name": "test_help_me_generate_a_picture_of_a_white_facial_cleanser", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of a white facial cleanser product advertisement", "uid": "33c969279ed591b6", "parentUid": "04904c74713fbbad0495c2bb13fcd7bc", "status": "failed", "time": {"start": 1755498437045, "stop": 1755498456665, "duration": 19620}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04904c74713fbbad0495c2bb13fcd7bc"}], "uid": "ed4a6606acb5889ffcaccac061b5ec70"}, {"name": "test_help_me_generate_a_picture_of_an_airplane", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of an airplane", "uid": "335c6b5fe0366e9f", "parentUid": "350cc6026a63aa12d21138a55d4a944e", "status": "failed", "time": {"start": 1755498471118, "stop": 1755498491007, "duration": 19889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "350cc6026a63aa12d21138a55d4a944e"}], "uid": "ce69731a5b7520de7d6d0d235ca73019"}, {"name": "test_help_me_generate_a_picture_of_an_elegant_girl", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of an elegant girl", "uid": "7d585244b5c6ebec", "parentUid": "c76f0161c593c58a77f29aa535b848f6", "status": "failed", "time": {"start": 1755498505560, "stop": 1755498525189, "duration": 19629}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c76f0161c593c58a77f29aa535b848f6"}], "uid": "8cfa3a7cbedd07867ed7e67051d61ea1"}, {"name": "test_help_me_generate_a_picture_of_blue_and_gold_landscape", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of blue and gold landscape", "uid": "2065d4815c11e1a2", "parentUid": "12bdb84f77f4cd269a0edb704bb2c41e", "status": "failed", "time": {"start": 1755498539605, "stop": 1755498559261, "duration": 19656}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12bdb84f77f4cd269a0edb704bb2c41e"}], "uid": "27911ac7aa937e69af3ae5cec3f495ad"}, {"name": "test_help_me_generate_a_picture_of_green_trees_in_shade_and_distant_mountains_in_a_hazy_state", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state", "uid": "7d9eec4fb5525906", "parentUid": "b95077add041816039776c9c79f08dab", "status": "failed", "time": {"start": 1755498573575, "stop": 1755498593110, "duration": 19535}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b95077add041816039776c9c79f08dab"}], "uid": "25729f449eba0009ff64c9a935b1d90a"}, {"name": "test_help_me_generate_an_image_of_the_shanghai_oriental_pearl_tower", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "uid": "b1ce2a37101255d3", "parentUid": "8cda819f631bec98cb0554769e15d776", "status": "failed", "time": {"start": 1755498607644, "stop": 1755498627395, "duration": 19751}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8cda819f631bec98cb0554769e15d776"}], "uid": "7f1f01d7c47311473fedb469caac8d5b"}, {"name": "test_help_me_write_an_email", "children": [{"name": "TestEllaHelpMeWriteAnEmail", "children": [{"name": "测试help me write an email能正常执行", "uid": "d4b74b5ecef1cd42", "parentUid": "649615446ede0148c5ff8678d523d0aa", "status": "passed", "time": {"start": 1755498641652, "stop": 1755498663735, "duration": 22083}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "649615446ede0148c5ff8678d523d0aa"}], "uid": "4c50426c37e2427cf10466a61f135134"}, {"name": "test_help_me_write_an_thanks_email", "children": [{"name": "TestEllaHelpMeWriteAnThanksEmail", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "b8a845bc7ae17991", "parentUid": "532261b0e09523c5a2a4e366e160f5fd", "status": "failed", "time": {"start": 1755498678124, "stop": 1755498700032, "duration": 21908}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "532261b0e09523c5a2a4e366e160f5fd"}], "uid": "e38c35bd53b03a201c55a53b3d55b4b0"}, {"name": "test_help_me_write_an_thanks_letter", "children": [{"name": "TestEllaHelpMeWriteAnThanksLetter", "children": [{"name": "测试help me write an thanks letter能正常执行", "uid": "bc302b2dd49b99c1", "parentUid": "3755340db0d802a9e3906271e22e82ca", "status": "failed", "time": {"start": 1755498714361, "stop": 1755498736158, "duration": 21797}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3755340db0d802a9e3906271e22e82ca"}], "uid": "407f54fb38fda3eea5b61bfdc186311a"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "TestEllaHowSetScreenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "2b071456a0f5b37e", "parentUid": "4862cd77fa6584ab1cd401dd18e576cf", "status": "passed", "time": {"start": 1755498750564, "stop": 1755498770495, "duration": 19931}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4862cd77fa6584ab1cd401dd18e576cf"}], "uid": "3af4e120133a0419124a1c704c7d3199"}, {"name": "test_i_am_your_voice_assistant", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试i am your voice assistant", "uid": "6eaaaf955dba0e22", "parentUid": "5d2ab4c795e8e992902489a882020682", "status": "passed", "time": {"start": 1755498784866, "stop": 1755498806913, "duration": 22047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d2ab4c795e8e992902489a882020682"}], "uid": "f320a30996f91af0099875f8919f7787"}, {"name": "test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up", "children": [{"name": "TestEllaIThinkScreenIsBitDarkNowCouldYouPleaseHelpMeBrightenItUp", "children": [{"name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "uid": "646f811621529e05", "parentUid": "a80246cd874d1d57e931db7753af0377", "status": "passed", "time": {"start": 1755498821100, "stop": 1755498841227, "duration": 20127}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a80246cd874d1d57e931db7753af0377"}], "uid": "fb4bb829272da3d339886c8bd1b5ae5e"}, {"name": "test_i_wanna_use_sim", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open settings", "uid": "5c144d97a0be73bb", "parentUid": "2a419ab3371bc38f5b2db4be2fd53c93", "status": "passed", "time": {"start": 1755498855312, "stop": 1755498882872, "duration": 27560}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a419ab3371bc38f5b2db4be2fd53c93"}], "uid": "fdf2c14fc9f40378813bb3e4136ca94e"}, {"name": "test_i_want_make_a_video_call_to", "children": [{"name": "TestEllaIWantMakeVideoCall", "children": [{"name": "测试i want make a video call to能正常执行", "uid": "a991d6f0d8c5ec1a", "parentUid": "3f01df61d110583696a88a3a43661649", "status": "failed", "time": {"start": 1755498896867, "stop": 1755498921404, "duration": 24537}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f01df61d110583696a88a3a43661649"}], "uid": "0c92e589d91f05a13b14f2396a36bdde"}, {"name": "test_i_want_to_hear_a_joke", "children": [{"name": "TestEllaIWantHearJoke", "children": [{"name": "测试i want to hear a joke能正常执行", "uid": "2ed067e7efa850d8", "parentUid": "83ddf078970bc87273baff7e50a7c081", "status": "passed", "time": {"start": 1755498935861, "stop": 1755498957822, "duration": 21961}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83ddf078970bc87273baff7e50a7c081"}], "uid": "ab7735228fa80d7187cbe6c8dc24d15e"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "TestEllaIncreaseSettingsSpecialFunctions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "72d70c2c9fdf6684", "parentUid": "47b335b99ed8a1ee8475ca4a6834e17a", "status": "passed", "time": {"start": 1755498971872, "stop": 1755498991682, "duration": 19810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47b335b99ed8a1ee8475ca4a6834e17a"}], "uid": "c3601014aaa999aff69c5872e32743b7"}, {"name": "test_install_whatsapp", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试install whatsapp", "uid": "5e11b33a5d2e52ff", "parentUid": "4bb5c66d7902d2096048251bb5599fd1", "status": "failed", "time": {"start": 1755499005649, "stop": 1755499032171, "duration": 26522}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4bb5c66d7902d2096048251bb5599fd1"}], "uid": "1050a857b984dfc8f5dd02e17ced12e6"}, {"name": "test_it_wears_a_red_leather_collar", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试it wears a red leather collar", "uid": "bac48bbae7e722ea", "parentUid": "a67d56cab2fca2f70165326d952b1b1f", "status": "failed", "time": {"start": 1755499046665, "stop": 1755499068421, "duration": 21756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a67d56cab2fca2f70165326d952b1b1f"}], "uid": "d0f916c130abce5deccb76c29a9cf970"}, {"name": "test_it_wears_a_yellow_leather_collar", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试it wears a yellow leather collar", "uid": "182235ca56db8a65", "parentUid": "0e4f5983194e64cacb58a2a70581c832", "status": "failed", "time": {"start": 1755499082851, "stop": 1755499104910, "duration": 22059}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0e4f5983194e64cacb58a2a70581c832"}], "uid": "288c4bf4d90e6c97e65f53a7b6e8ca68"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "TestEllaJumpAdaptiveBrightnessSettings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "1c639ed2f10e8347", "parentUid": "f92d66f2cfb1510e3614f32014b90cdf", "status": "passed", "time": {"start": 1755499119563, "stop": 1755499139697, "duration": 20134}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f92d66f2cfb1510e3614f32014b90cdf"}], "uid": "eda1255cba29f83ff1461094841f0f40"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "TestEllaJumpAiWallpaperGeneratorSettings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "9be58cb8aa969561", "parentUid": "508a56999eea40b3b6eae200090d9ef7", "status": "passed", "time": {"start": 1755499154075, "stop": 1755499173945, "duration": 19870}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "508a56999eea40b3b6eae200090d9ef7"}], "uid": "952ea4e1b05a29ce2f17032769862fd2"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "TestEllaJumpAutoRotateScreenSettings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "d4bb3fe8535c92ab", "parentUid": "923f4dfcc1ff430310299406df4a76ad", "status": "passed", "time": {"start": 1755499188546, "stop": 1755499208696, "duration": 20150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "923f4dfcc1ff430310299406df4a76ad"}], "uid": "2885405d4e301681acf1f9073b75196b"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "TestEllaJumpBatteryPowerSaving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "26ec988295aa927a", "parentUid": "7b0af410a8bc7729423b66dbd86394a1", "status": "passed", "time": {"start": 1755499222973, "stop": 1755499242838, "duration": 19865}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0af410a8bc7729423b66dbd86394a1"}], "uid": "5e4b9eee9fae8ca49a205b0e08671105"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "TestEllaJumpBatteryUsage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "e98db407de5b1b14", "parentUid": "e977b1d095b1f986aff6226776ded3c3", "status": "passed", "time": {"start": 1755499257348, "stop": 1755499277636, "duration": 20288}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e977b1d095b1f986aff6226776ded3c3"}], "uid": "379854b415d26ca0952c7565049546e2"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "TestEllaJumpCallNotifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "a77bf5e5bbe2c7b5", "parentUid": "e1d634c784c8a3eaf84c787e520a993c", "status": "passed", "time": {"start": 1755499291887, "stop": 1755499316921, "duration": 25034}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1d634c784c8a3eaf84c787e520a993c"}], "uid": "0d6d5e9fa27b112c5e4a5c892fa26625"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "TestEllaJumpHighBrightnessModeSettings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "9e04729e8f6d4112", "parentUid": "968c23cffad77650f73adb57ed35e44e", "status": "passed", "time": {"start": 1755499331264, "stop": 1755499351357, "duration": 20093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968c23cffad77650f73adb57ed35e44e"}], "uid": "f96efa82b36a271078aa933d212ff1e1"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "4bcc04d743d0b567", "parentUid": "6e6a65b648407dd3ea18fb96b915dace", "status": "passed", "time": {"start": 1755499365745, "stop": 1755499385908, "duration": 20163}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e6a65b648407dd3ea18fb96b915dace"}], "uid": "a57e12d4c6edf0c5f1e60f981492cd0f"}, {"name": "test_jump_to_nfc_settings", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试jump to nfc settings", "uid": "fe79bc89c4872fda", "parentUid": "400a6941e3389b1b48d77dfa263077fe", "status": "passed", "time": {"start": 1755499400306, "stop": 1755499425614, "duration": 25308}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "400a6941e3389b1b48d77dfa263077fe"}], "uid": "364aacfa906bc91f673d917736ae37e5"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "TestEllaJumpNotificationsStatusBarSettings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "96cbde4d10134ba1", "parentUid": "5891939ff40959b2d8e13bfa76678a88", "status": "passed", "time": {"start": 1755499440032, "stop": 1755499460063, "duration": 20031}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5891939ff40959b2d8e13bfa76678a88"}], "uid": "843f8e4d40a46af764e0186551ea2eab"}, {"name": "test_kill_whatsapp", "children": [{"name": "TestEllaKillWhatsapp", "children": [{"name": "测试kill whatsapp能正常执行", "uid": "3a2687a1b3501681", "parentUid": "1c8d4c3e4504945ca2c7aa8aa790b4ab", "status": "passed", "time": {"start": 1755499474555, "stop": 1755499496045, "duration": 21490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c8d4c3e4504945ca2c7aa8aa790b4ab"}], "uid": "f5b7dfc982ee4029db9ba755d4b0c849"}, {"name": "test_kinkaku_ji", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Kinkaku-ji", "uid": "3130376940e4019a", "parentUid": "8cf457f2ecb53ef87b31773bfc3fbc6b", "status": "failed", "time": {"start": 1755499510636, "stop": 1755499532273, "duration": 21637}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8cf457f2ecb53ef87b31773bfc3fbc6b"}], "uid": "b1bedc63289670232d638d0d405ce266"}, {"name": "test_make_a_call_by_whatsapp", "children": [{"name": "TestEllaMakeCallWhatsapp", "children": [{"name": "测试make a call by whatsapp能正常执行", "uid": "a406107bc0f5f0df", "parentUid": "6ae16d98ea4a978463257ee627b456ad", "status": "failed", "time": {"start": 1755499547024, "stop": 1755499571768, "duration": 24744}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6ae16d98ea4a978463257ee627b456ad"}], "uid": "6db9b6e7242da372327bdb47a2414f75"}, {"name": "test_make_a_call_on_whatsapp_to_a", "children": [{"name": "TestEllaMakeCallWhatsapp", "children": [{"name": "测试make a call on whatsapp to a能正常执行", "uid": "55db0b714c0ed415", "parentUid": "593d06fe46d94082261aada55e48e76f", "status": "failed", "time": {"start": 1755499586623, "stop": 1755499611252, "duration": 24629}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "593d06fe46d94082261aada55e48e76f"}], "uid": "f51881f7a51f8cf69974b186fe0415ac"}, {"name": "test_make_a_phone_call_to_17621905233", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试make a phone call to 17621905233", "uid": "3c1bd1aed8b1f3f3", "parentUid": "8d299e7d03e17d084f401cd981238376", "status": "failed", "time": {"start": 1755499625974, "stop": 1755499662647, "duration": 36673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8d299e7d03e17d084f401cd981238376"}], "uid": "1f67773c8fb683ce8f601ccdb67d2731"}, {"name": "test_merry_christmas", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试merry christmas", "uid": "8b33ad00ff2563f7", "parentUid": "47285da26e24914dcbdc0cc4da76f94b", "status": "failed", "time": {"start": 1755499677285, "stop": 1755499699849, "duration": 22564}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47285da26e24914dcbdc0cc4da76f94b"}], "uid": "830feedd6312aaf2ef31fc5a483ef07f"}, {"name": "test_modify_grape_timbre", "children": [{"name": "TestEllaEnableRunningLock", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "30fd85a9beb451aa", "parentUid": "79b5d194845308e1383e35ab63ba394b", "status": "passed", "time": {"start": 1755499725440, "stop": 1755499746593, "duration": 21153}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79b5d194845308e1383e35ab63ba394b"}], "uid": "ff7fe79e49919f1d7ccc98346b480d21"}, {"name": "test_more_settings", "children": [{"name": "TestEllaMoreSettings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "64afb45b4049dff2", "parentUid": "5158adca762a1833ee225b49be689b4b", "status": "failed", "time": {"start": 1755499765361, "stop": 1755499788231, "duration": 22870}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5158adca762a1833ee225b49be689b4b"}], "uid": "2678afdbcf3cd1c2f7304aab68dff13e"}, {"name": "test_navigate_to_the_address_on_the_screen", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Navigate to the address on the screen", "uid": "8b6bb38522a867", "parentUid": "923f005ebf7c4a5e973a745babc94137", "status": "failed", "time": {"start": 1755499802486, "stop": 1755499829878, "duration": 27392}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "923f005ebf7c4a5e973a745babc94137"}], "uid": "a2fb8e15f5cae8757959e8482bc4755d"}, {"name": "test_navigation_to_the_address_in_the_image", "children": [{"name": "TestEllaNavigationAddressTheImage", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "2971cd2a051395c6", "parentUid": "bdbc8f989cddecd7a3ff8b9229b6e0b7", "status": "failed", "time": {"start": 1755499844460, "stop": 1755499871815, "duration": 27355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdbc8f989cddecd7a3ff8b9229b6e0b7"}], "uid": "2ea8810e61f48227b2b12d8788c78cfa"}, {"name": "test_navigation_to_the_first_address_in_the_image", "children": [{"name": "TestEllaNavigationFirstAddressImage", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "c382d9a7c98e9700", "parentUid": "fc6a08afad859df9f5da8d1611e05a0e", "status": "broken", "time": {"start": 1755499886498, "stop": 1755499913812, "duration": 27314}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fc6a08afad859df9f5da8d1611e05a0e"}], "uid": "f052d105eebf03aacc6d59b029ca356c"}, {"name": "test_new_year_wishes", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试new year wishes", "uid": "668552dfe8255d0", "parentUid": "6d707f3e108bd7a51974e4e2397afde8", "status": "passed", "time": {"start": 1755499928693, "stop": 1755499951104, "duration": 22411}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d707f3e108bd7a51974e4e2397afde8"}], "uid": "961bda73d2be820987dfe720dbe06f68"}, {"name": "test_new_year_wishs", "children": [{"name": "TestEllaNewYearWishs", "children": [{"name": "测试new year wishs能正常执行", "uid": "fdefecb0e53024bd", "parentUid": "73768b6c43f36d7ad5edde91f8c1e415", "status": "passed", "time": {"start": 1755499965447, "stop": 1755499987851, "duration": 22404}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73768b6c43f36d7ad5edde91f8c1e415"}], "uid": "95ff601119aea5204c63d1095bfb4652"}, {"name": "test_open_camera", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open camera", "uid": "1325fb4910bba5dc", "parentUid": "1b9691270eaeab1dddf2a5166c7d6276", "status": "passed", "time": {"start": 1755500001861, "stop": 1755500029431, "duration": 27570}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b9691270eaeab1dddf2a5166c7d6276"}], "uid": "3d80518374ca8633acca9dc138f42448"}, {"name": "test_open_font_family_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "a0e7cec935f6313e", "parentUid": "3abee97ea9125b112c9768709b230d6c", "status": "passed", "time": {"start": 1755500046558, "stop": 1755500066788, "duration": 20230}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3abee97ea9125b112c9768709b230d6c"}], "uid": "c37be023aa1668dd5137d98115ed22ee"}, {"name": "test_open_maps", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open maps", "uid": "6805b12606331bab", "parentUid": "9b2b0152a79f484cf7ff400da9bf0224", "status": "failed", "time": {"start": 1755500083972, "stop": 1755500110387, "duration": 26415}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b2b0152a79f484cf7ff400da9bf0224"}], "uid": "a1ed2a41018e67d39dd589a4ec2c1056"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "b77ca79b01255105", "parentUid": "1547bf4ebc0f2064b90de7ee9d4b0d9a", "status": "passed", "time": {"start": 1755500126629, "stop": 1755500147152, "duration": 20523}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1547bf4ebc0f2064b90de7ee9d4b0d9a"}], "uid": "e9bb1812204e529485c6767282c66ee4"}, {"name": "test_open_settings", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open settings", "uid": "c920e1d39a5457ed", "parentUid": "1798b34a6d68e964f689776e603b40ae", "status": "passed", "time": {"start": 1755500164467, "stop": 1755500193941, "duration": 29474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1798b34a6d68e964f689776e603b40ae"}], "uid": "6fec943db24a17ffc89cb8435dbd34c7"}, {"name": "test_open_the_settings", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open the settings", "uid": "b8ee6665a3282604", "parentUid": "27526f65718c6d32e2ddafe8beec70f3", "status": "passed", "time": {"start": 1755500207958, "stop": 1755500237529, "duration": 29571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27526f65718c6d32e2ddafe8beec70f3"}], "uid": "8a0151ef426a4ec4845e49443ec2a111"}, {"name": "test_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试open whatsapp", "uid": "e8aa8be0b5219380", "parentUid": "a86f67524fc901b9e239b41a55bcd168", "status": "failed", "time": {"start": 1755500251685, "stop": 1755500273123, "duration": 21438}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a86f67524fc901b9e239b41a55bcd168"}], "uid": "134b51cd09c0f3f3f5664bfe08db4bfb"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaOrderBurger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "6587d831a5fec99", "parentUid": "caf4b95867e28da5919f2eca0a324167", "status": "failed", "time": {"start": 1755500287619, "stop": 1755500307141, "duration": 19522}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "caf4b95867e28da5919f2eca0a324167"}], "uid": "5866702b17345a70f73c15e82c35a889"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderTakeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "dbf0ff17969dd66a", "parentUid": "bee4551900f0d6522500f0c802a47f62", "status": "failed", "time": {"start": 1755500321856, "stop": 1755500341678, "duration": 19822}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bee4551900f0d6522500f0c802a47f62"}], "uid": "d94ef1da6fdbed2a2a316ba39ec7f816"}, {"name": "test_parking_space", "children": [{"name": "TestEllaParkingSpace", "children": [{"name": "测试parking space能正常执行", "uid": "e1fadb569d8be957", "parentUid": "8002e65cdc68fa89adcbace4cab353af", "status": "passed", "time": {"start": 1755500356386, "stop": 1755500376522, "duration": 20136}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8002e65cdc68fa89adcbace4cab353af"}], "uid": "91df28ab9c563eb71b7c27ebe03bd2a9"}, {"name": "test_play_carpenters_video", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play carpenters'video", "uid": "b52f32404e514907", "parentUid": "c9ef97a1b6f2b67ae34377997b3e40a6", "status": "passed", "time": {"start": 1755500390909, "stop": 1755500415348, "duration": 24439}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c9ef97a1b6f2b67ae34377997b3e40a6"}], "uid": "f47f53fad8ed96a4309bb6db6e7ae1ca"}, {"name": "test_play_football_video_by_youtube", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play football video by youtube", "uid": "2be3c27007c5ea", "parentUid": "f50015233729c567f6411101e9d7507e", "status": "passed", "time": {"start": 1755500429868, "stop": 1755500455655, "duration": 25787}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f50015233729c567f6411101e9d7507e"}], "uid": "288230e0b3a90da164068733d1ce2f50"}, {"name": "test_play_love_sotry", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play love sotry", "uid": "dff6c54af645754a", "parentUid": "2eafce82b79f0069cbd0ea652014bbfb", "status": "failed", "time": {"start": 1755500470020, "stop": 1755500502239, "duration": 32219}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2eafce82b79f0069cbd0ea652014bbfb"}], "uid": "395956abc96532bb04cf74abc316a3ba"}, {"name": "test_play_music_by_Audiomack", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by Audiomack", "uid": "6b97305455e1990b", "parentUid": "7f7895de67b743adcacabc0317321439", "status": "passed", "time": {"start": 1755500516536, "stop": 1755500539093, "duration": 22557}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7f7895de67b743adcacabc0317321439"}], "uid": "fee074ac550e0b9b4e5bea39a6ac4f8e"}, {"name": "test_play_taylor_swift_s_song_love_sotry", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "2caaf5389b91cfdd", "parentUid": "369a78d30a3bb706690dc0f41716f29f", "status": "failed", "time": {"start": 1755500553285, "stop": 1755500588707, "duration": 35422}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "369a78d30a3bb706690dc0f41716f29f"}], "uid": "fee77f8c40bb5ae6f21bab13e0798786"}, {"name": "test_play_the_album", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play the album", "uid": "a96ede0d1550352a", "parentUid": "1cfc5343c5c88745c65dc82ee560dd78", "status": "failed", "time": {"start": 1755500603403, "stop": 1755500638611, "duration": 35208}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1cfc5343c5c88745c65dc82ee560dd78"}], "uid": "58b703424f3437f68fd423d41eccfd46"}, {"name": "test_play_video", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play video", "uid": "d665d8af26ee1129", "parentUid": "6e35a63e6264545436bbb6f84204721d", "status": "passed", "time": {"start": 1755500653373, "stop": 1755500679284, "duration": 25911}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e35a63e6264545436bbb6f84204721d"}], "uid": "0a6d47b585a70d59df4457bfeb32ef4b"}, {"name": "test_play_video_by_youtube", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play video by youtube", "uid": "d20b72b0d68e40dc", "parentUid": "87ac2f2511f41462750c26dcbc17f7d7", "status": "passed", "time": {"start": 1755500693798, "stop": 1755500719696, "duration": 25898}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "87ac2f2511f41462750c26dcbc17f7d7"}], "uid": "fe310a2b7e3c25a7a2ac071656c5ef12"}, {"name": "test_please_show_me_where_i_am", "children": [{"name": "TestEllaPleaseShowMeWhereIAm", "children": [{"name": "测试please show me where i am能正常执行", "uid": "90249de4c88a455f", "parentUid": "9fd87d8d098fefc38dbf9094b5c04473", "status": "failed", "time": {"start": 1755500733993, "stop": 1755500756225, "duration": 22232}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9fd87d8d098fefc38dbf9094b5c04473"}], "uid": "476b6def0b2277a73bf0dfbcd5b6047b"}, {"name": "test_pls_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试pls open whatsapp", "uid": "5c85d779948a47f0", "parentUid": "f606de62a17219d1049f95130d391334", "status": "failed", "time": {"start": 1755500770998, "stop": 1755500792516, "duration": 21518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f606de62a17219d1049f95130d391334"}], "uid": "71d89bf7be06a8672fe794f8b289d664"}, {"name": "test_power_off_my_phone", "children": [{"name": "TestEllaPowerOffMyPhone", "children": [{"name": "测试power off my phone能正常执行", "uid": "23a5a6d1930c8de0", "parentUid": "669f2c441081b8047f110fce6da0af5b", "status": "skipped", "time": {"start": 1755500794247, "stop": 1755500794247, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')", "smoke"]}], "uid": "669f2c441081b8047f110fce6da0af5b"}], "uid": "9ed6b03efdd1270108dae1c5824cfbcd"}, {"name": "test_privacy_policy", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试privacy policy", "uid": "a420961df9d02bd2", "parentUid": "2d4923b35199be73f82b3fc099b5a6ce", "status": "failed", "time": {"start": 1755500807069, "stop": 1755500827069, "duration": 20000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d4923b35199be73f82b3fc099b5a6ce"}], "uid": "4031d737f80326aa10894ca653ad541f"}, {"name": "test_puppy", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试puppy", "uid": "972dfc9d92038e5f", "parentUid": "0d8eb9f405316ff1fca2e6ef8f4175bb", "status": "failed", "time": {"start": 1755500841668, "stop": 1755500863743, "duration": 22075}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d8eb9f405316ff1fca2e6ef8f4175bb"}], "uid": "db40a72c20905024ab55fba25dd6e0e5"}, {"name": "test_reboot_my_phone", "children": [{"name": "TestEllaRebootMyPhone", "children": [{"name": "测试reboot my phone能正常执行", "uid": "d73582501560b23b", "parentUid": "074f07bf2e66f00df5ce600265963bda", "status": "skipped", "time": {"start": 1755500865454, "stop": 1755500865454, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='reboot 会导致设备断开，先跳过')", "smoke"]}], "uid": "074f07bf2e66f00df5ce600265963bda"}], "uid": "f04e46f8e511a670eda47336976c0900"}, {"name": "test_redial", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试redial", "uid": "b9ec27d846cd74ff", "parentUid": "73134362df5e82df9dbacec436fd51cc", "status": "failed", "time": {"start": 1755500878434, "stop": 1755500908190, "duration": 29756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73134362df5e82df9dbacec436fd51cc"}], "uid": "ad6624cb14424d8194382f0f58d7848c"}, {"name": "test_remember_the_parking_lot", "children": [{"name": "TestEllaRememberParkingLot", "children": [{"name": "测试remember the parking lot能正常执行", "uid": "84f75d265fcaa190", "parentUid": "1bd4a56d7fec08386ad6aab127a34043", "status": "passed", "time": {"start": 1755500923028, "stop": 1755500943497, "duration": 20469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1bd4a56d7fec08386ad6aab127a34043"}], "uid": "46f3ca535feecdbe2f37236d2c699570"}, {"name": "test_remember_the_parking_space", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试remember the parking space", "uid": "8228e1e49acbaade", "parentUid": "d9577f276870a2e1accd8b1252b061f4", "status": "failed", "time": {"start": 1755500957873, "stop": 1755500977591, "duration": 19718}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9577f276870a2e1accd8b1252b061f4"}], "uid": "39d9463583101a130b62d5abf9ec5ec6"}, {"name": "test_remove_the_people_from_the_image", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试remove the people from the image", "uid": "17e3bc1a6301489b", "parentUid": "b7dbc6442cf1571b90e4e077de3d9dae", "status": "passed", "time": {"start": 1755500992344, "stop": 1755501012650, "duration": 20306}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7dbc6442cf1571b90e4e077de3d9dae"}], "uid": "bc43d87bf1e9e65f9dbdebcea5354a16"}, {"name": "test_reset_phone", "children": [{"name": "TestEllaResetPhone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "6e7affd9337554b1", "parentUid": "cac15668a449dfa5284ac8751a8bc088", "status": "skipped", "time": {"start": 1755501014105, "stop": 1755501014105, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='reset phone 会导致设备断开，先跳过')"]}], "uid": "cac15668a449dfa5284ac8751a8bc088"}], "uid": "20f77fb14c171a00344aefe112819e8f"}, {"name": "test_restart_my_phone", "children": [{"name": "TestEllaRestartMyPhone", "children": [{"name": "测试restart my phone能正常执行", "uid": "e3b85aff8f796ffc", "parentUid": "8e6b8a6f5b7ace7e38fe72173c083641", "status": "skipped", "time": {"start": 1755501014108, "stop": 1755501014108, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='重启会导致设备断开，先跳过')", "smoke"]}], "uid": "8e6b8a6f5b7ace7e38fe72173c083641"}], "uid": "37ab7708b333614b029b8e7dbb1544ed"}, {"name": "test_restart_the_phone", "children": [{"name": "TestEllaRestartPhone", "children": [{"name": "测试restart the phone能正常执行", "uid": "e845d7ff9eca6135", "parentUid": "3774bd71fb27e3ff5098f87b6107b67c", "status": "skipped", "time": {"start": 1755501014112, "stop": 1755501014112, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='重启会导致设备断开，先跳过')", "smoke"]}], "uid": "3774bd71fb27e3ff5098f87b6107b67c"}], "uid": "8520ec26c958caa18d4558c5d16d58cd"}, {"name": "test_running_on_the_grass", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试running on the grass", "uid": "83b3448b88954127", "parentUid": "44a906216f4c6a2414f6344601024496", "status": "failed", "time": {"start": 1755501027117, "stop": 1755501050646, "duration": 23529}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "44a906216f4c6a2414f6344601024496"}], "uid": "e5f3c6c2323a2333a15185788e22874b"}, {"name": "test_search_for_addresses_on_the_screen", "children": [{"name": "TestEllaSearchAddressesScreen", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "5447509a46e116e5", "parentUid": "4c52013229be65cab9dbe8706e15af5a", "status": "failed", "time": {"start": 1755501065460, "stop": 1755501087263, "duration": 21803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c52013229be65cab9dbe8706e15af5a"}], "uid": "c2898d22ab26a5d42aa6bad2edebff1b"}, {"name": "test_search_the_address_in_the_image", "children": [{"name": "TestEllaSearchAddressImage", "children": [{"name": "测试search the address in the image能正常执行", "uid": "25b19dae7e7898a9", "parentUid": "c189e03b4adbf1ffd08ccde0c003cbf1", "status": "failed", "time": {"start": 1755501101833, "stop": 1755501123678, "duration": 21845}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c189e03b4adbf1ffd08ccde0c003cbf1"}], "uid": "18c4911f1477f7e8b52e7c52b7206918"}, {"name": "test_search_whatsapp_for_me", "children": [{"name": "TestEllaSearchWhatsappMe", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "45006ddc3ec43548", "parentUid": "cd5851bdca5beda41724955a20608d69", "status": "failed", "time": {"start": 1755501138302, "stop": 1755501165239, "duration": 26937}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd5851bdca5beda41724955a20608d69"}], "uid": "000d1f465ed860bdf1ed56e67ee1ae93"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "TestEllaSearchingMethodViolentMurder", "children": [{"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "cb3ff0a46733676a", "parentUid": "ce100ec49c328fc7bcaa8647cea22b48", "status": "failed", "time": {"start": 1755501179706, "stop": 1755501202710, "duration": 23004}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ce100ec49c328fc7bcaa8647cea22b48"}], "uid": "be41a396acb219f73e244b463a77b9e4"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "TestEllaSendMyRecentPhotosMomThroughWhatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "e59cb7cd3b526a00", "parentUid": "7a8b4afc50f4eff1f1bfb333ef0634a3", "status": "failed", "time": {"start": 1755501219260, "stop": 1755501239140, "duration": 19880}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7a8b4afc50f4eff1f1bfb333ef0634a3"}], "uid": "a815543064692fc926dec55bba6ffd8f"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "TestEllaSetAppAutoRotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "dda705191d063540", "parentUid": "5ec31d73ee5a8cbd16b741e2a0c12725", "status": "passed", "time": {"start": 1755501253489, "stop": 1755501273810, "duration": 20321}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5ec31d73ee5a8cbd16b741e2a0c12725"}], "uid": "c4e1b97dbe16d5e25caab90c80f85213"}, {"name": "test_set_app_notifications", "children": [{"name": "TestEllaSetAppNotifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "510077bcecd96a52", "parentUid": "e2b37df150554a2656df5f000b5f3877", "status": "passed", "time": {"start": 1755501288430, "stop": 1755501308954, "duration": 20524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2b37df150554a2656df5f000b5f3877"}], "uid": "c477cad1f43ef2333b7849edf5ce05ed"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "TestEllaSetBatterySaverSettings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "30bf17e43a39dd9a", "parentUid": "c420f6bca101c62ba94f9e8e7f31a573", "status": "passed", "time": {"start": 1755501323608, "stop": 1755501343903, "duration": 20295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c420f6bca101c62ba94f9e8e7f31a573"}], "uid": "8b4c47455f601b3d7d6f1e100772a44a"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "TestEllaSetCallBackLastUsedSim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "f7f3ffdd6c15e978", "parentUid": "4c0f190c6f4aee497792a6d856a0035a", "status": "passed", "time": {"start": 1755501358373, "stop": 1755501383749, "duration": 25376}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c0f190c6f4aee497792a6d856a0035a"}], "uid": "7cea4873dfe6764b25d4b6368eec9cfb"}, {"name": "test_set_color_style", "children": [{"name": "TestEllaSetColorStyle", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "f5eb2245322c21f3", "parentUid": "a93fd41464aaeca8c5187ded9998a8cf", "status": "passed", "time": {"start": 1755501398160, "stop": 1755501418314, "duration": 20154}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a93fd41464aaeca8c5187ded9998a8cf"}], "uid": "ba0686e84222e058748253df8047a50b"}, {"name": "test_set_compatibility_mode", "children": [{"name": "TestEllaSetCompatibilityMode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "92cef72143d43475", "parentUid": "3b4b879cb25f4ec1c9e5f56f7b979c37", "status": "passed", "time": {"start": 1755501432865, "stop": 1755501452916, "duration": 20051}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b4b879cb25f4ec1c9e5f56f7b979c37"}], "uid": "1909cdae019ead080ae02a578b4d7316"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "TestEllaSetCoverScreenApps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "3d6a8621cf946c5b", "parentUid": "37a4a9b94837c06f3d0d560b86bbe29b", "status": "passed", "time": {"start": 1755501467355, "stop": 1755501487645, "duration": 20290}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "37a4a9b94837c06f3d0d560b86bbe29b"}], "uid": "5df35bd6f80d4c6657cbae8b27064aab"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "TestEllaSetCustomizedCoverScreen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "1ff42489c15972ba", "parentUid": "4d3fece58e4f70510cd292befe33b96a", "status": "passed", "time": {"start": 1755501501992, "stop": 1755501522119, "duration": 20127}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d3fece58e4f70510cd292befe33b96a"}], "uid": "3fa52a12d7acae5645ea3aa75477d44b"}, {"name": "test_set_date_time", "children": [{"name": "TestEllaSetDateTime", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "fad168214781956", "parentUid": "9d950448d0ff30907a7af780f114a130", "status": "passed", "time": {"start": 1755501536289, "stop": 1755501556322, "duration": 20033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d950448d0ff30907a7af780f114a130"}], "uid": "8c30598a8b1f5940667edde71b30791c"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "TestEllaSetEdgeMistouchPrevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "9b3d3443e1680243", "parentUid": "dada83ec1ab33079d5f86d4ac7ee1f04", "status": "passed", "time": {"start": 1755501570753, "stop": 1755501590736, "duration": 19983}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dada83ec1ab33079d5f86d4ac7ee1f04"}], "uid": "a30de810322f2776f8c899c615c4be77"}, {"name": "test_set_flex_still_mode", "children": [{"name": "TestEllaSetFlexStillMode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "10bd30f7006334a1", "parentUid": "a51d316b425964f68fef53807195a46f", "status": "passed", "time": {"start": 1755501604999, "stop": 1755501625585, "duration": 20586}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a51d316b425964f68fef53807195a46f"}], "uid": "0100b1b2aa4c49467859f43e6353bedb"}, {"name": "test_set_flip_case_feature", "children": [{"name": "TestEllaSetFlipCaseFeature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "ce32b9d5043b65c7", "parentUid": "d5b01634d272a358f06ff35ca4ca4a23", "status": "passed", "time": {"start": 1755501640065, "stop": 1755501660159, "duration": 20094}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d5b01634d272a358f06ff35ca4ca4a23"}], "uid": "659c6f22cfed2953c4ba8d4b8dd7294a"}, {"name": "test_set_floating_windows", "children": [{"name": "TestEllaSetFloatingWindows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "f1a2335fc00c0593", "parentUid": "e15e2e981396bcf4d57ba068a480c5b1", "status": "passed", "time": {"start": 1755501674594, "stop": 1755501694505, "duration": 19911}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e15e2e981396bcf4d57ba068a480c5b1"}], "uid": "1dc2787970a8d9c589a8ae300f462858"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "TestEllaSetFoldingScreenZone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "2bb64e9111015fac", "parentUid": "47c139cd34b0a780094ffb98e1812572", "status": "passed", "time": {"start": 1755501709038, "stop": 1755501728993, "duration": 19955}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47c139cd34b0a780094ffb98e1812572"}], "uid": "45d2d7d6fad9b4da39cc6dac0c4fa337"}, {"name": "test_set_font_size", "children": [{"name": "TestEllaSetFontSize", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "ae97570254349a81", "parentUid": "80b6fcd56edd3e33d66094a25cc5d31b", "status": "passed", "time": {"start": 1755501743438, "stop": 1755501763776, "duration": 20338}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80b6fcd56edd3e33d66094a25cc5d31b"}], "uid": "9a3389ccf2eca5621def99072303502f"}, {"name": "test_set_gesture_navigation", "children": [{"name": "TestEllaSetGestureNavigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "b25b8c84b1510e0d", "parentUid": "5a5a307b4aee14702733cda564e2594e", "status": "passed", "time": {"start": 1755501778076, "stop": 1755501803407, "duration": 25331}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a5a307b4aee14702733cda564e2594e"}], "uid": "f45d715ad98a0594e567c013238fb055"}, {"name": "test_set_languages", "children": [{"name": "TestEllaSetLanguages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "995c07e42bbb58ee", "parentUid": "3e28dcd9a9cdb251320f0e3b7e655a4d", "status": "passed", "time": {"start": 1755501817438, "stop": 1755501837336, "duration": 19898}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e28dcd9a9cdb251320f0e3b7e655a4d"}], "uid": "eedc9f2088f9e7ed345a41dcf938b3aa"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "TestEllaSetLockscreenPasswords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "b6c79c64811b995d", "parentUid": "41dd05458efc06498e30ee8a35ffd98f", "status": "passed", "time": {"start": 1755501851699, "stop": 1755501871785, "duration": 20086}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41dd05458efc06498e30ee8a35ffd98f"}], "uid": "62cf905659379d62ce72a50cba40e0f8"}, {"name": "test_set_my_fonts", "children": [{"name": "TestEllaSetMyFonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "7e3649c5cead3c40", "parentUid": "3feb62634faa8c2680759a67a0f0b2df", "status": "passed", "time": {"start": 1755501885970, "stop": 1755501906182, "duration": 20212}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3feb62634faa8c2680759a67a0f0b2df"}], "uid": "64427e107c50bb3a09e1b67c0bf1e141"}, {"name": "test_set_my_themes", "children": [{"name": "TestEllaSetMyThemes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "996118d4554ccb4", "parentUid": "b86797063b9d865d350ec21ac7112b5f", "status": "passed", "time": {"start": 1755501920538, "stop": 1755501940759, "duration": 20221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b86797063b9d865d350ec21ac7112b5f"}], "uid": "716eecd28b7d4b5ede25b5df0e5507c2"}, {"name": "test_set_nfc_tag", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试set nfc tag", "uid": "a3d165df3ebffcd2", "parentUid": "6762d305cbfc537c83db06768a9c4d2d", "status": "passed", "time": {"start": 1755501955063, "stop": 1755501981777, "duration": 26714}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6762d305cbfc537c83db06768a9c4d2d"}], "uid": "0e88e754c9726ef600c72680272f7029"}, {"name": "test_set_off_a_firework", "children": [{"name": "TestEllaSetOffFirework", "children": [{"name": "测试set off a firework能正常执行", "uid": "c38c8c516435e68d", "parentUid": "021acb4eaaacdcf72d568b2312fa9bfc", "status": "passed", "time": {"start": 1755501996136, "stop": 1755502018765, "duration": 22629}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "021acb4eaaacdcf72d568b2312fa9bfc"}], "uid": "8425a95213c647a6dbc3c547777d6228"}, {"name": "test_set_parallel_windows", "children": [{"name": "TestEllaSetParallelWindows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "62c530edb229f0e9", "parentUid": "e9b410d4bf477a3418fde54f839d7e6d", "status": "passed", "time": {"start": 1755502033366, "stop": 1755502053561, "duration": 20195}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9b410d4bf477a3418fde54f839d7e6d"}], "uid": "55c6cd519ecfe75eb797fa2c66d0d75f"}, {"name": "test_set_personal_hotspot", "children": [{"name": "TestEllaSetPersonalHotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "d7f0e92d59493a29", "parentUid": "b3579038871378a6fdcc9bb20849bdf1", "status": "passed", "time": {"start": 1755502068065, "stop": 1755502088184, "duration": 20119}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b3579038871378a6fdcc9bb20849bdf1"}], "uid": "b482fd86e6dab754c48a5bf619de5afb"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "TestEllaSetPhantomVPen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "76178af16b4619f6", "parentUid": "7c5353b9ef36136e92a92708b7e45bb3", "status": "passed", "time": {"start": 1755502102701, "stop": 1755502122824, "duration": 20123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c5353b9ef36136e92a92708b7e45bb3"}], "uid": "107e8fe279ca1fd2b153352fda86fb4a"}, {"name": "test_set_phone_number", "children": [{"name": "TestEllaSetPhoneNumber", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "289f152ae316d34e", "parentUid": "96b22c407c943460e8486fc825169a80", "status": "passed", "time": {"start": 1755502137097, "stop": 1755502156982, "duration": 19885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96b22c407c943460e8486fc825169a80"}], "uid": "e9cd1fa5db40d2ec604808d9c497fadb"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "TestEllaSetScheduledPowerOffRestart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "a3a50fc447cbdbb6", "parentUid": "58b82848dae0adadd98c95016e0b8711", "status": "passed", "time": {"start": 1755502171721, "stop": 1755502192104, "duration": 20383}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58b82848dae0adadd98c95016e0b8711"}], "uid": "e6b0ed51c0d597afdb5bf8c4e6af53c3"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "TestEllaSetScreenRefreshRate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "6d349ca2a630ab5d", "parentUid": "3e9009cde7fd47070b3529a63de695c3", "status": "passed", "time": {"start": 1755502206625, "stop": 1755502226455, "duration": 19830}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e9009cde7fd47070b3529a63de695c3"}], "uid": "c9de8a50bcaa3dc3fd1b3a9409aa8ba0"}, {"name": "test_set_screen_relay", "children": [{"name": "TestEllaSetScreenRelay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "f309194462f0435e", "parentUid": "61fe1e0731921d580561f77ccf2de7a2", "status": "passed", "time": {"start": 1755502241083, "stop": 1755502261282, "duration": 20199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "61fe1e0731921d580561f77ccf2de7a2"}], "uid": "7ebda1dd475915217421f499f80df8c5"}, {"name": "test_set_screen_timeout", "children": [{"name": "TestEllaSetScreenTimeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "d64080247fe0d3ec", "parentUid": "3f7e6a161af6de02774a99275b95e1d7", "status": "passed", "time": {"start": 1755502275675, "stop": 1755502295573, "duration": 19898}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f7e6a161af6de02774a99275b95e1d7"}], "uid": "3ad3475cd36fab63233e05c3002bb8c5"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "TestEllaSetScreenMinimumBrightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "d8d6a9cb72f9a9b2", "parentUid": "70cd5196189a628ba70f4e189ab3ed2a", "status": "passed", "time": {"start": 1755502310048, "stop": 1755502330262, "duration": 20214}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "70cd5196189a628ba70f4e189ab3ed2a"}], "uid": "ccff2bc4a244de015f51207ad9fb9973"}, {"name": "test_set_sim_ringtone", "children": [{"name": "TestEllaSetSimRingtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "58fcfe1877b70f21", "parentUid": "b540694383c716efd22be6cf332cff66", "status": "passed", "time": {"start": 1755502344477, "stop": 1755502364735, "duration": 20258}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b540694383c716efd22be6cf332cff66"}], "uid": "f521b50083932bc320d541b59f4fa12e"}, {"name": "test_set_smart_hub", "children": [{"name": "TestEllaSetSmartHub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "36628fbfae9837fe", "parentUid": "9982a7cb76e7edde540c33f77bcb4131", "status": "passed", "time": {"start": 1755502379321, "stop": 1755502399431, "duration": 20110}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9982a7cb76e7edde540c33f77bcb4131"}], "uid": "9159365ae1f13aa7202a2ecabae352cd"}, {"name": "test_set_smart_panel", "children": [{"name": "TestEllaSetSmartPanel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "aa3123e8759f39e5", "parentUid": "8186149225876d2c7f48b35cdec99dd3", "status": "passed", "time": {"start": 1755502413908, "stop": 1755502434127, "duration": 20219}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8186149225876d2c7f48b35cdec99dd3"}], "uid": "6d2c866f4f10ac8e5f2e0399839eca9f"}, {"name": "test_set_special_function", "children": [{"name": "TestEllaSetSpecialFunction", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "d4295feb52943498", "parentUid": "a485e9dc82964f874c4ca66999bcf15a", "status": "passed", "time": {"start": 1755502448608, "stop": 1755502468888, "duration": 20280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a485e9dc82964f874c4ca66999bcf15a"}], "uid": "1385459e098e23ca404584fba544f87b"}, {"name": "test_set_split_screen_apps", "children": [{"name": "TestEllaSetSplitScreenApps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "4204334de6610ed", "parentUid": "e704a416305aeb7b8861f78da3d399fd", "status": "passed", "time": {"start": 1755502483424, "stop": 1755502503269, "duration": 19845}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e704a416305aeb7b8861f78da3d399fd"}], "uid": "18aa5f1d09e55e245cdba84af89bdb4f"}, {"name": "test_set_timer", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试set timer", "uid": "905b452af4c83531", "parentUid": "709eb7cf5d71d5a191206f8c5fde3417", "status": "passed", "time": {"start": 1755502517955, "stop": 1755502542549, "duration": 24594}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "709eb7cf5d71d5a191206f8c5fde3417"}], "uid": "048485ad7321fdc8f5337562645b0668"}, {"name": "test_set_timezone", "children": [{"name": "TestEllaSetTimezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "580b4adaa8def73c", "parentUid": "f68e969f0dc6fa17bc3cbb1c76e0468d", "status": "passed", "time": {"start": 1755502557069, "stop": 1755502577028, "duration": 19959}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f68e969f0dc6fa17bc3cbb1c76e0468d"}], "uid": "6b5e2d316297d46c06b128e02645ba18"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "TestEllaSetUltraPowerSaving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "333e71c4eb012946", "parentUid": "c853a846bf2e6118074af640a3bdfded", "status": "passed", "time": {"start": 1755502591417, "stop": 1755502611565, "duration": 20148}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c853a846bf2e6118074af640a3bdfded"}], "uid": "516f9e12e9fe59099bceb66c299b4b06"}, {"name": "test_start_boosting_phone", "children": [{"name": "TestEllaStartBoostingPhone", "children": [{"name": "测试start boosting phone能正常执行", "uid": "3c70382151a25d97", "parentUid": "ea72904cc48cdab04b6a32328bcff936", "status": "passed", "time": {"start": 1755502626119, "stop": 1755502645058, "duration": 18939}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ea72904cc48cdab04b6a32328bcff936"}], "uid": "4dfd3dff348daec46a2840ec95737d64"}, {"name": "test_start_running", "children": [{"name": "TestEllaStartRunning", "children": [{"name": "测试start running能正常执行", "uid": "1e52d2148711dd51", "parentUid": "a24fb40874550e9588bb7a1e759255e9", "status": "passed", "time": {"start": 1755502659288, "stop": 1755502686615, "duration": 27327}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a24fb40874550e9588bb7a1e759255e9"}], "uid": "d46bf7e5557e0738a865ecc07e2d584c"}, {"name": "test_start_walking", "children": [{"name": "TestEllaStartWalking", "children": [{"name": "测试start walking能正常执行", "uid": "422d5172f04f4d88", "parentUid": "c6ec8ec07b5dd2102a991e8a20680e71", "status": "failed", "time": {"start": 1755502700860, "stop": 1755502720800, "duration": 19940}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c6ec8ec07b5dd2102a991e8a20680e71"}], "uid": "a4e7eb3a60d774ac67cd2cc111cb9845"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试summarize content on this page", "uid": "f764933e115da665", "parentUid": "0c1645a4da645d7a1d71b140a4cd79e2", "status": "passed", "time": {"start": 1755502735267, "stop": 1755502754931, "duration": 19664}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0c1645a4da645d7a1d71b140a4cd79e2"}], "uid": "3973a80f2c9dc60eba1128908a3b9a37"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Summarize what I'm reading", "uid": "893a242d4f3c4168", "parentUid": "cb427ad26aa4429b88871b40f64cfe5f", "status": "failed", "time": {"start": 1755502768861, "stop": 1755502788565, "duration": 19704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb427ad26aa4429b88871b40f64cfe5f"}], "uid": "b6c93448ab1517917d641f5403c05e57"}, {"name": "test_switch_to_davido_voice", "children": [{"name": "TestEllaSwitchDavidoVoice", "children": [{"name": "测试Switch to davido voice能正常执行", "uid": "5b556f0e4be6c4d2", "parentUid": "597cb599122b854d604c01a270b1a8d0", "status": "passed", "time": {"start": 1755502802990, "stop": 1755502822974, "duration": 19984}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "597cb599122b854d604c01a270b1a8d0"}], "uid": "16aca23b24bc10dfd8554c557da8b449"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "3b64e6faa650c1b9", "parentUid": "0bfb9433b5ffe1874f0d5456da733833", "status": "passed", "time": {"start": 1755502837015, "stop": 1755502857027, "duration": 20012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0bfb9433b5ffe1874f0d5456da733833"}], "uid": "6dfd47afc83ade6b7e64a599884aa82b"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "TestEllaSwitchPerformanceMode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "7659a0165dec7300", "parentUid": "e8f8d6344d1896b53428623c96ae9737", "status": "passed", "time": {"start": 1755502871234, "stop": 1755502891230, "duration": 19996}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8f8d6344d1896b53428623c96ae9737"}], "uid": "660b4eae367594166e98133010b58c55"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchPowerSavingMode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "84a3333e716ccffd", "parentUid": "69d2b561fc70829d0f0c3bcbc22bd24b", "status": "passed", "time": {"start": 1755502905384, "stop": 1755502925587, "duration": 20203}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69d2b561fc70829d0f0c3bcbc22bd24b"}], "uid": "89fed13c0557d811b5a98a81c18249c0"}, {"name": "test_switching_charging_speed", "children": [{"name": "TestEllaSwitchingChargingSpeed", "children": [{"name": "测试switching charging speed能正常执行", "uid": "be2104ab93275ecd", "parentUid": "364923db55b7a4749bd0f0a3d56b478a", "status": "passed", "time": {"start": 1755502939827, "stop": 1755502959419, "duration": 19592}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "364923db55b7a4749bd0f0a3d56b478a"}], "uid": "2bc0a442c3b51933b3e020072f001bf8"}, {"name": "test_take_notes", "children": [{"name": "TestEllaTakeNotes", "children": [{"name": "测试take notes能正常执行", "uid": "b443e1f1845998b3", "parentUid": "44b041dc5921837d99b03ee251fe9a80", "status": "failed", "time": {"start": 1755502973579, "stop": 1755502997522, "duration": 23943}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "44b041dc5921837d99b03ee251fe9a80"}], "uid": "9ce4c8d89aae56754ff0ae0d7cf834cc"}, {"name": "test_tell_me_a_joke", "children": [{"name": "TestEllaTellMeJoke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "ce671eab76db995d", "parentUid": "972f4768d4110bc9be4051d713f5f1d4", "status": "passed", "time": {"start": 1755503012049, "stop": 1755503032018, "duration": 19969}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "972f4768d4110bc9be4051d713f5f1d4"}], "uid": "3febd52b6aecbf31d119447b90331a44"}, {"name": "test_tell_me_joke", "children": [{"name": "TestEllaTellMeJoke", "children": [{"name": "测试tell me joke能正常执行", "uid": "6a2210c6e321bc0e", "parentUid": "7d17397dac8db6170e4185f194ee95e9", "status": "failed", "time": {"start": 1755503046199, "stop": 1755503068153, "duration": 21954}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d17397dac8db6170e4185f194ee95e9"}], "uid": "eeee5549335b4d4ecf3fac8dfda2e40d"}, {"name": "test_the_mobile_phone_is_very_hot", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试the mobile phone is very hot", "uid": "ced88cbf8385740", "parentUid": "3bfb94cf2cc472b22c0df52f847f1f62", "status": "passed", "time": {"start": 1755503082614, "stop": 1755503103467, "duration": 20853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3bfb94cf2cc472b22c0df52f847f1f62"}], "uid": "644948ffc4704bfb385cb254e8d9a71f"}, {"name": "test_the_second", "children": [{"name": "TestEllaSecond", "children": [{"name": "测试the second返回正确的不支持响应", "uid": "d3df6c7fbbcf4def", "parentUid": "ba446261c47bba6a43d497764fa39a1e", "status": "failed", "time": {"start": 1755503117826, "stop": 1755503137320, "duration": 19494}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ba446261c47bba6a43d497764fa39a1e"}], "uid": "3ef96941774e396d5f61e6c374c2a964"}, {"name": "test_there_are_many_yellow_sunflowers_on_the_ground", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试there are many yellow sunflowers on the ground", "uid": "4e2648abb16ff5b8", "parentUid": "6a020ec60475fc27227dfd7d129c0666", "status": "failed", "time": {"start": 1755503151747, "stop": 1755503173682, "duration": 21935}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a020ec60475fc27227dfd7d129c0666"}], "uid": "4b7ffea1eaf84fdb04dd8c191f0a34a7"}, {"name": "test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试There are transparent, glowing multicolored soap bubbles around it", "uid": "d7ee269013f09469", "parentUid": "1ac3a98fe00494d546e4e19879238daa", "status": "failed", "time": {"start": 1755503187965, "stop": 1755503209685, "duration": 21720}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ac3a98fe00494d546e4e19879238daa"}], "uid": "4abc82e646169b13fe4bfdca3b0227bf"}, {"name": "test_there_is_a_colorful_butterfly_beside_it", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试there is a colorful butterfly beside it", "uid": "6aeb533208e0773f", "parentUid": "ea0cb2908e9182f02032e5a6ed562f29", "status": "failed", "time": {"start": 1755503224026, "stop": 1755503246190, "duration": 22164}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ea0cb2908e9182f02032e5a6ed562f29"}], "uid": "d28054bcefb0ae260c73232793dd123b"}, {"name": "test_three_little_pigs", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Three Little Pigs", "uid": "3afa524a119e3a10", "parentUid": "781eb57a3919119cd50bf17689fab4a1", "status": "failed", "time": {"start": 1755503260669, "stop": 1755503282774, "duration": 22105}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "781eb57a3919119cd50bf17689fab4a1"}], "uid": "a385499ab54be564b0a879c66033c6d0"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "TestEllaTurnOffDrivingMode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "f91b1dc90fffec91", "parentUid": "24ecd251ff3a56a4c2b20fac8ab55f2f", "status": "failed", "time": {"start": 1755503297233, "stop": 1755503316995, "duration": 19762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "24ecd251ff3a56a4c2b20fac8ab55f2f"}], "uid": "ca08b2eb3eff172bf5d579af818a108f"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "TestEllaTurnOffShowBatteryPercentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "1e3aa146f0f14553", "parentUid": "a93695196bd86c4de1585781fedbe080", "status": "passed", "time": {"start": 1755503331142, "stop": 1755503350931, "duration": 19789}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a93695196bd86c4de1585781fedbe080"}], "uid": "492b5eb198aa5b8127740d2701bc4490"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "TestEllaTurnDrivingMode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "adc4d3baca711ab8", "parentUid": "6160fde1e3814087ef49c469733a5e59", "status": "passed", "time": {"start": 1755503364942, "stop": 1755503386815, "duration": 21873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6160fde1e3814087ef49c469733a5e59"}], "uid": "7dbd97d94de7ac9fc92ce34bec95b544"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "TestEllaTurnHighBrightnessMode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "ca63fd21108cd88d", "parentUid": "afa769cc76891525f7fca4f496a4aa9e", "status": "failed", "time": {"start": 1755503400629, "stop": 1755503421629, "duration": 21000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "afa769cc76891525f7fca4f496a4aa9e"}], "uid": "9751732cdae9d7a072c84060577699cd"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "TestEllaTurnShowBatteryPercentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "743d8d64aae8d5f3", "parentUid": "9a14e86a2195f01365f057ce6c9b9e71", "status": "passed", "time": {"start": 1755503435976, "stop": 1755503455814, "duration": 19838}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a14e86a2195f01365f057ce6c9b9e71"}], "uid": "d5d7ce8427ad486d072c9aae7e2d5b3f"}, {"name": "test_vedio_call_number_by_whatsapp", "children": [{"name": "TestEllaVedioCallNumberWhatsapp", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "6d46a40ce803f513", "parentUid": "0d26a41f5b9615ad2942aaedf4c3033b", "status": "failed", "time": {"start": 1755503469728, "stop": 1755503494396, "duration": 24668}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d26a41f5b9615ad2942aaedf4c3033b"}], "uid": "2aeb91366c9de96caf75e843c25ff4e8"}, {"name": "test_view_in_notebook", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试view in notebook", "uid": "e7fa0fed0f28cc86", "parentUid": "c3ef4d52b4f3af521894d73b37ffcd6c", "status": "passed", "time": {"start": 1755503508944, "stop": 1755503542089, "duration": 33145}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c3ef4d52b4f3af521894d73b37ffcd6c"}], "uid": "2a41ee4b2b23e90432d1271bde83c670"}, {"name": "test_voice_setting_page", "children": [{"name": "TestEllaVoiceSettingPage", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "8779cfa151cd8b27", "parentUid": "087c3d15647e1625a6f4e506048dafc7", "status": "failed", "time": {"start": 1755503556431, "stop": 1755503580071, "duration": 23640}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "087c3d15647e1625a6f4e506048dafc7"}], "uid": "20224dbf8d73e834d2be6a17094deb05"}, {"name": "test_what_date_is_it", "children": [{"name": "TestEllaWhatDateIsIt", "children": [{"name": "测试what date is it能正常执行", "uid": "7edfe091bfb65cfc", "parentUid": "a6477b2e019a056f6425d2950e664eda", "status": "passed", "time": {"start": 1755503594479, "stop": 1755503614526, "duration": 20047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a6477b2e019a056f6425d2950e664eda"}], "uid": "462cfd09072e082d758a28b5f86133d1"}, {"name": "test_what_is_the_weather_today", "children": [{"name": "TestEllaWhatIsWeatherToday", "children": [{"name": "测试what is the weather today能正常执行", "uid": "3502fc0fb5c7596d", "parentUid": "a414b6a93668541af19f36ef5de69c30", "status": "passed", "time": {"start": 1755503628737, "stop": 1755503655895, "duration": 27158}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a414b6a93668541af19f36ef5de69c30"}], "uid": "e3099a6471a6935ea4a79d5929efd816"}, {"name": "test_what_s_the_date_today", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试what's the date today", "uid": "7d15a70337843500", "parentUid": "f595c0dd6f1fd9a4e7baf1d329bad64c", "status": "passed", "time": {"start": 1755503669750, "stop": 1755503689735, "duration": 19985}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f595c0dd6f1fd9a4e7baf1d329bad64c"}], "uid": "59a37907804b8a3b0259567688977de8"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "TestEllaWhatSWheatherToday", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "1c840d107195e187", "parentUid": "34ec790ead0442abc792eadbded82b45", "status": "passed", "time": {"start": 1755503703863, "stop": 1755503723945, "duration": 20082}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34ec790ead0442abc792eadbded82b45"}], "uid": "93ccf0164b324d8401f93f28368fa587"}, {"name": "test_what_s_your_name", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试what's your name", "uid": "1b84d57f52a023ae", "parentUid": "b844a592b24930bb05406461a3b55d12", "status": "failed", "time": {"start": 1755503738391, "stop": 1755503758272, "duration": 19881}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b844a592b24930bb05406461a3b55d12"}], "uid": "150beefb27e3fb4ef7f8d5b7c8b79550"}, {"name": "test_what_time_is_it", "children": [{"name": "TestEllaWhatTimeIsIt", "children": [{"name": "测试what time is it能正常执行", "uid": "7488546749f49d74", "parentUid": "9a1707cab6ba21805e5557c27878166f", "status": "passed", "time": {"start": 1755503772918, "stop": 1755503792961, "duration": 20043}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a1707cab6ba21805e5557c27878166f"}], "uid": "62cbf432c6942e953309364cba948cfd"}, {"name": "test_what_time_is_it_in_china", "children": [{"name": "TestEllaWhatTimeIsItChina", "children": [{"name": "测试what time is it in china能正常执行", "uid": "ced91b5cbc3f13c7", "parentUid": "3d53633b743425d02140cc93addc1f0b", "status": "passed", "time": {"start": 1755503807286, "stop": 1755503827297, "duration": 20011}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3d53633b743425d02140cc93addc1f0b"}], "uid": "4a9f7a7ef35e6b31c4f5af99323fa779"}, {"name": "test_what_time_is_it_in_london", "children": [{"name": "TestEllaWhatTimeIsItLondon", "children": [{"name": "测试what time is it in London能正常执行", "uid": "aaa5ad47c39b0896", "parentUid": "9e6b9c0060829efcc73d4ea740df1b49", "status": "passed", "time": {"start": 1755503841503, "stop": 1755503861474, "duration": 19971}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e6b9c0060829efcc73d4ea740df1b49"}], "uid": "af53bd7e2482c27610ecc08ef1183aeb"}, {"name": "test_where_is_my_car", "children": [{"name": "TestEllaWhereIsMyCar", "children": [{"name": "测试where is my car能正常执行", "uid": "80b713fe8f81f97a", "parentUid": "c5f06521808b3dfc5047ef209e95fcbc", "status": "failed", "time": {"start": 1755503875692, "stop": 1755503895478, "duration": 19786}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5f06521808b3dfc5047ef209e95fcbc"}], "uid": "8cbe670b8f03e2204b3dddfa23a6e7bb"}, {"name": "test_where_s_my_car", "children": [{"name": "TestEllaWhereSMyCar", "children": [{"name": "测试where`s my car能正常执行", "uid": "a2f54ffc67eb0791", "parentUid": "29ba7f25d098ddc58f6283babdc91d05", "status": "passed", "time": {"start": 1755503910166, "stop": 1755503930283, "duration": 20117}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29ba7f25d098ddc58f6283babdc91d05"}], "uid": "f4119e4a1ede5a25ce07bb10ab021122"}, {"name": "test_yandex_eats", "children": [{"name": "TestEllaYandexEats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "a1b32f0518e05e8", "parentUid": "3c01adf30480b292f5f2b75550755b66", "status": "passed", "time": {"start": 1755503944641, "stop": 1755503964691, "duration": 20050}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3c01adf30480b292f5f2b75550755b66"}], "uid": "e099b3554c54efdee17091c4c12f77c7"}], "uid": "09cb3650ff0a2cc2af23d31dd3c975a2"}]}