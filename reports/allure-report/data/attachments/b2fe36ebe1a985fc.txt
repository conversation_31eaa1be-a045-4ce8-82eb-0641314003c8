2025-08-18 12:12:35 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-08-18 12:12:35 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:299 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-18 12:12:35 | INFO | tools.adb_process_monitor:clear_all_running_processes:1015 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-18 12:12:35 | INFO | tools.adb_process_monitor:clear_all_running_processes:1031 | ⚡ 优先使用命令直接清理...
2025-08-18 12:12:38 | INFO | tools.adb_process_monitor:clear_all_running_processes:1037 | 💪 强制停止顽固应用...
2025-08-18 12:12:42 | INFO | tools.adb_process_monitor:clear_all_running_processes:1047 | 🎉 应用进程清理完成，共清理 30 个应用
2025-08-18 12:12:44 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:301 | ✅ 应用进程清理完成
2025-08-18 12:12:44 | INFO | pages.apps.ella.dialogue_page:start_app:201 | 启动Ella应用
2025-08-18 12:12:44 | INFO | pages.apps.ella.dialogue_page:start_app:209 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:12:48 | INFO | pages.apps.ella.dialogue_page:_check_app_started:267 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-08-18 12:12:48 | INFO | pages.apps.ella.dialogue_page:start_app:214 | ✅ Ella应用启动成功（指定Activity）
2025-08-18 12:12:48 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:288 | 等待Ella页面加载完成 (超时: 15秒)
2025-08-18 12:12:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-08-18 12:12:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:12:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-18 12:12:48 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:292 | ✅ 输入框已出现，页面加载完成
2025-08-18 12:12:48 | INFO | testcases.test_ella.base_ella_test:ella_app:333 | ✅ Ella应用启动成功
2025-08-18 12:12:48 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:366 | 初始状态None- 使用命令document summary，状态: 
2025-08-18 12:12:48 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:662 | 确保在对话页面...
2025-08-18 12:12:48 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:12:49 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:12:49 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:12:49 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:12:49 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:673 | ✅ 已在对话页面
2025-08-18 12:12:49 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-18 12:12:49 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-18 12:12:49 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:374 | 🎯 检测到多模态指令: document
2025-08-18 12:12:49 | INFO | testcases.test_ella.base_ella_test:_execute_multimodal_operation:1212 | 🚀 开始执行多模态操作: document
2025-08-18 12:12:49 | INFO | pages.apps.ella.ella_multimodal_handler:execute_multimodal_function:49 | 执行多模态功能: document
2025-08-18 12:12:49 | INFO | pages.apps.ella.ella_multimodal_handler:_open_multimodal_entrance:75 | 点击多模态入口
2025-08-18 12:12:49 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [多模态入口], 超时时间: 5秒
2025-08-18 12:12:49 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:12:49 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [多模态入口]
2025-08-18 12:12:50 | INFO | core.base_element:click:231 | 点击元素成功 [多模态入口]
2025-08-18 12:12:53 | INFO | pages.apps.ella.ella_multimodal_handler:_open_multimodal_entrance:86 | ✅ 成功点击多模态入口
2025-08-18 12:12:53 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_document_flow:102 | 执行文档功能流程
2025-08-18 12:12:53 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_documents:661 | 开始推送测试文档到设备
2025-08-18 12:12:53 | INFO | tools.file_pusher:check_device_connection:444 | 检测到 1 个连接的设备
2025-08-18 12:12:53 | INFO | tools.file_pusher:push_documents_to_device:62 | 开始推送文档文件到设备
2025-08-18 12:12:53 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: bcy_doc.txt
2025-08-18 12:12:53 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: bcy_doc2.txt
2025-08-18 12:12:54 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: bcy_doc.txt
2025-08-18 12:12:54 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: bcy_doc2.txt
2025-08-18 12:12:54 | INFO | tools.file_pusher:push_documents_to_device:88 | 文档推送完成: 4/4 个文件成功
2025-08-18 12:12:54 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_documents:671 | ✅ 测试文档推送成功
2025-08-18 12:12:56 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_documents:679 | 设备中的文件: ['QuarkBrowser_V7.15.1.890_android_pf3300_(zh-cn)_release_(Build250813220216-arm64).apk', 'QuarkDownloads', 'add_all_the_numbers_to_lucy.jpg', 'add_the_lucy_s_number_in_this_picture.jpg', 'add_the_moms_and_lucys_number.jpg', 'add_the_number_on_the_screen_to_contacts.jpg', 'add_this_number_to_lucy.jpg', 'bcy_doc - 副本.txt', 'bcy_doc.txt', 'bcy_doc2.txt', 'dial_the_number_on_the_screen.jpg', 'fusion', 'save_the_number_on_the_screen_to_contact_lulu.jpg', '哔哩哔哩.apk']
2025-08-18 12:12:56 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [多模态-document], 超时时间: 5秒
2025-08-18 12:12:56 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:12:56 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [多模态-document]
2025-08-18 12:12:57 | INFO | core.base_element:click:231 | 点击元素成功 [多模态-document]
2025-08-18 12:12:59 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_document_flow:116 | ✅ 点击document按钮成功
2025-08-18 12:12:59 | INFO | pages.apps.ella.ella_multimodal_handler:_select_file:255 | 尝试选择文件
2025-08-18 12:13:09 | INFO | pages.apps.ella.ella_multimodal_handler:_select_specific_file:286 | 尝试选择特定文件: bcy_doc.txt
2025-08-18 12:13:12 | INFO | pages.apps.ella.ella_multimodal_handler:_select_specific_file:293 | ✅ 成功选择文件: bcy_doc.txt
2025-08-18 12:13:12 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_document_flow:123 | ✅ 文档功能流程完成
2025-08-18 12:13:12 | INFO | testcases.test_ella.base_ella_test:_execute_multimodal_operation:1223 | ✅ 多模态功能执行成功: document
2025-08-18 12:13:14 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:662 | 确保在对话页面...
2025-08-18 12:13:14 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:13:14 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:13:14 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:13:14 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:13:14 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:673 | ✅ 已在对话页面
2025-08-18 12:13:14 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: document summary
2025-08-18 12:13:14 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-18 12:13:14 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-18 12:13:14 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: document summary
2025-08-18 12:13:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-18 12:13:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:13:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-18 12:13:15 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-08-18 12:13:15 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-18 12:13:15 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:13:15 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-18 12:13:16 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: document summary
2025-08-18 12:13:16 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-08-18 12:13:16 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-08-18 12:13:16 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-08-18 12:13:16 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:13:16 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-08-18 12:13:17 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-08-18 12:13:17 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-08-18 12:13:17 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-08-18 12:13:17 | INFO | testcases.test_ella.base_ella_test:_execute_command:870 | ✅ 成功执行命令: document summary
2025-08-18 12:13:17 | INFO | testcases.test_ella.base_ella_test:_handle_popup_after_command:178 | handle_popup_after_command:处理弹窗
2025-08-18 12:13:17 | INFO | core.popup_tool:detect_and_close_popup_once:735 | 执行单次弹窗检测和关闭
2025-08-18 12:13:24 | INFO | core.popup_tool:detect_and_close_popup_once:739 | 未检测到弹窗，无需处理
2025-08-18 12:13:24 | INFO | testcases.test_ella.base_ella_test:_get_response_timeout:1259 | 🎯 多模态命令 (document) 使用专用超时时间: 5秒
2025-08-18 12:13:24 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 5秒
2025-08-18 12:13:25 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:60 | ✅ 通过TTS按钮检测到响应
2025-08-18 12:13:29 | INFO | testcases.test_ella.base_ella_test:_get_final_status_with_page_info:440 | 状态检查时当前应用包名: com.transsion.aivoiceassistant
2025-08-18 12:13:29 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:558 | 状态检查完成，现在获取响应文本
2025-08-18 12:13:29 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:562 | 第1次尝试确保在Ella页面以获取响应
2025-08-18 12:13:29 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:662 | 确保在对话页面...
2025-08-18 12:13:29 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:13:29 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:13:29 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:13:29 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:13:29 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:673 | ✅ 已在对话页面
2025-08-18 12:13:29 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:566 | ✅ 已确认在Ella对话页面，可以获取响应
2025-08-18 12:13:29 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:13:30 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:13:30 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:13:30 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:13:30 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:813 | 检查是否在Ella页面...
2025-08-18 12:13:30 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:13:30 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:13:30 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:13:30 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:13:30 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:822 | ✅ 当前在Ella页面
2025-08-18 12:13:30 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-08-18 12:13:31 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | asr_txt节点不存在，已达到最大重试次数
2025-08-18 12:13:33 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | robot_text节点不存在，已达到最大重试次数
2025-08-18 12:13:34 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_name节点不存在，已达到最大重试次数
2025-08-18 12:13:36 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点不存在，已达到最大重试次数
2025-08-18 12:13:36 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:154 | 尝试获取其他有效的响应文本
2025-08-18 12:13:36 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:549 | 从TextView元素获取响应
2025-08-18 12:13:37 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:565 | 查找RecyclerView中的最新消息
2025-08-18 12:13:38 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:397 | 从dump正则提取文本: Dialogue Explore Swipe down to view earlier chats Exit Summarization and Q&A DeepSeek-R1 Feel free to ask me any questions… 12:13
2025-08-18 12:13:38 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:166 | ✅ 获取到响应文本: Dialogue Explore Swipe down to view earlier chats Exit Summarization and Q&A DeepSeek-R1 Feel free to ask me any questions… 12:13
2025-08-18 12:13:38 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:171 | 未获取到有效的响应文本
2025-08-18 12:13:38 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:596 | 最终获取的AI响应: '['', '', '', '', 'Dialogue Explore Swipe down to view earlier chats Exit Summarization and Q&A DeepSeek-R1 Feel free to ask me any questions… 12:13']'
2025-08-18 12:13:38 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:407 | ✅ 多模态操作执行完成: document
2025-08-18 12:13:38 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaDocumentSummary\test_completed.png
2025-08-18 12:13:38 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1373 | 🎉 document summary 测试完成 (多模态: document)
2025-08-18 12:13:38 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1010 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['', '', '', '', 'Dialogue Explore Swipe down to view earlier chats Exit Summarization and Q&A DeepSeek-R1 Feel free to ask me any questions… 12:13']
2025-08-18 12:13:38 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1057 | ⚠️ 响应未包含期望内容: 'AIGC'
2025-08-18 12:13:38 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1057 | ⚠️ 响应未包含期望内容: 'document provides a summary of the application'
2025-08-18 12:13:38 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1064 | ❌ 部分期望内容未找到 (0/2)
2025-08-18 12:13:38 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1065 | 缺失内容: ['AIGC', 'document provides a summary of the application']
2025-08-18 12:13:38 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1066 | 搜索文本: 'Dialogue Explore Swipe down to view earlier chats Exit Summarization and Q&A DeepSeek-R1 Feel free to ask me any questions… 12:13'
2025-08-18 12:13:39 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaDocumentSummary\failure_test_document_summary_20250818_121338.png
2025-08-18 12:13:39 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaDocumentSummary\failure_test_document_summary_20250818_121338.png
2025-08-18 12:13:39 | INFO | pages.apps.ella.dialogue_page:stop_app:308 | 停止Ella应用
2025-08-18 12:13:40 | INFO | pages.apps.ella.dialogue_page:stop_app:319 | ✅ Ella应用已成功停止
