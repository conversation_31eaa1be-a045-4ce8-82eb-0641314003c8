2025-08-18 12:10:26 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-08-18 12:10:26 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:299 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-18 12:10:26 | INFO | tools.adb_process_monitor:clear_all_running_processes:1015 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-18 12:10:26 | INFO | tools.adb_process_monitor:clear_all_running_processes:1031 | ⚡ 优先使用命令直接清理...
2025-08-18 12:10:29 | INFO | tools.adb_process_monitor:clear_all_running_processes:1037 | 💪 强制停止顽固应用...
2025-08-18 12:10:33 | INFO | tools.adb_process_monitor:clear_all_running_processes:1047 | 🎉 应用进程清理完成，共清理 30 个应用
2025-08-18 12:10:35 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:301 | ✅ 应用进程清理完成
2025-08-18 12:10:35 | INFO | pages.apps.ella.dialogue_page:start_app:201 | 启动Ella应用
2025-08-18 12:10:36 | INFO | pages.apps.ella.dialogue_page:start_app:209 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:10:39 | INFO | pages.apps.ella.dialogue_page:_check_app_started:267 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-08-18 12:10:39 | INFO | pages.apps.ella.dialogue_page:start_app:214 | ✅ Ella应用启动成功（指定Activity）
2025-08-18 12:10:39 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:288 | 等待Ella页面加载完成 (超时: 15秒)
2025-08-18 12:10:39 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-08-18 12:10:39 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:10:39 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-18 12:10:39 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:292 | ✅ 输入框已出现，页面加载完成
2025-08-18 12:10:39 | INFO | testcases.test_ella.base_ella_test:ella_app:333 | ✅ Ella应用启动成功
2025-08-18 12:10:39 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:366 | 初始状态None- 使用命令Change the style of this image to 3D cartoon，状态: 
2025-08-18 12:10:39 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:662 | 确保在对话页面...
2025-08-18 12:10:39 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:10:39 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:10:39 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:10:39 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:10:40 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:673 | ✅ 已在对话页面
2025-08-18 12:10:40 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-18 12:10:40 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-18 12:10:40 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:374 | 🎯 检测到多模态指令: gallery
2025-08-18 12:10:40 | INFO | testcases.test_ella.base_ella_test:_execute_multimodal_operation:1212 | 🚀 开始执行多模态操作: gallery
2025-08-18 12:10:40 | INFO | pages.apps.ella.ella_multimodal_handler:execute_multimodal_function:49 | 执行多模态功能: gallery
2025-08-18 12:10:40 | INFO | pages.apps.ella.ella_multimodal_handler:_open_multimodal_entrance:75 | 点击多模态入口
2025-08-18 12:10:40 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [多模态入口], 超时时间: 5秒
2025-08-18 12:10:40 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:10:40 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [多模态入口]
2025-08-18 12:10:41 | INFO | core.base_element:click:231 | 点击元素成功 [多模态入口]
2025-08-18 12:10:44 | INFO | pages.apps.ella.ella_multimodal_handler:_open_multimodal_entrance:86 | ✅ 成功点击多模态入口
2025-08-18 12:10:44 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_gallery_flow:136 | 执行图库功能流程
2025-08-18 12:10:44 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_images:693 | 开始推送测试图片到设备
2025-08-18 12:10:44 | INFO | tools.file_pusher:check_device_connection:444 | 检测到 1 个连接的设备
2025-08-18 12:10:44 | INFO | tools.file_pusher:push_images_to_device:106 | 开始推送图片文件到设备
2025-08-18 12:10:44 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: IMG_20250812_151520_276.jpg
2025-08-18 12:10:45 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: IMG_20250812_151520_276.jpg
2025-08-18 12:10:45 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: qr_code.png
2025-08-18 12:10:45 | INFO | tools.file_pusher:_push_single_file:420 | ✅ 文件推送成功: qr_code.png
2025-08-18 12:10:45 | INFO | tools.file_pusher:_refresh_media_library:495 | 刷新媒体库: /sdcard/DCIM/Camera
2025-08-18 12:10:46 | INFO | tools.file_pusher:_refresh_media_library:502 | ✅ 媒体扫描器扫描成功
2025-08-18 12:10:46 | INFO | tools.file_pusher:_refresh_media_library:511 | ✅ 媒体库刷新成功
2025-08-18 12:10:46 | INFO | tools.file_pusher:_refresh_media_library:520 | ✅ 媒体存储服务重启成功
2025-08-18 12:10:49 | INFO | tools.file_pusher:push_images_to_device:136 | 图片推送完成: 4/4 个文件成功
2025-08-18 12:10:49 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_images:703 | ✅ 测试图片推送成功
2025-08-18 12:10:51 | INFO | pages.apps.ella.ella_multimodal_handler:_prepare_test_images:711 | 设备中的图片: ['IMG_20250812_151520_276.jpg', 'IMG_20250815_145547_806.jpg', 'IMG_20250815_145634_783.jpg', 'qr_code.png']
2025-08-18 12:10:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [多模态-gallery], 超时时间: 5秒
2025-08-18 12:10:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:10:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [多模态-gallery]
2025-08-18 12:10:52 | INFO | core.base_element:click:231 | 点击元素成功 [多模态-gallery]
2025-08-18 12:10:57 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_gallery_flow:150 | ✅ 点击gallery按钮成功
2025-08-18 12:10:57 | INFO | pages.apps.ella.ella_multimodal_handler:_select_photo:437 | 尝试选择照片
2025-08-18 12:11:05 | INFO | pages.apps.ella.ella_multimodal_handler:_select_any_image:491 | 尝试选择任何可用图片
2025-08-18 12:11:08 | INFO | pages.apps.ella.ella_multimodal_handler:_select_any_image:510 | ✅ 选择图片 (第2个ImageView)
2025-08-18 12:11:08 | INFO | pages.apps.ella.ella_multimodal_handler:_try_confirm_photo_selection:555 | 尝试确认照片选择
2025-08-18 12:11:39 | INFO | pages.apps.ella.ella_multimodal_handler:_try_confirm_photo_selection:590 | 未找到确认按钮，照片可能已自动选择
2025-08-18 12:11:39 | INFO | pages.apps.ella.ella_multimodal_handler:_handle_gallery_flow:157 | ✅ 图库功能流程完成
2025-08-18 12:11:39 | INFO | testcases.test_ella.base_ella_test:_execute_multimodal_operation:1223 | ✅ 多模态功能执行成功: gallery
2025-08-18 12:11:41 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:662 | 确保在对话页面...
2025-08-18 12:11:41 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:11:41 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:11:41 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:11:41 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:11:41 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:673 | ✅ 已在对话页面
2025-08-18 12:11:41 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: Change the style of this image to 3D cartoon
2025-08-18 12:11:41 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-18 12:11:42 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-18 12:11:42 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: Change the style of this image to 3D cartoon
2025-08-18 12:11:42 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-18 12:11:42 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:11:42 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-18 12:11:42 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-08-18 12:11:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-18 12:11:43 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:11:43 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-18 12:11:43 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: Change the style of this image to 3D cartoon
2025-08-18 12:11:43 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-08-18 12:11:43 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-08-18 12:11:43 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-08-18 12:11:44 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-18 12:11:44 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-08-18 12:11:44 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-08-18 12:11:44 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-08-18 12:11:44 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-08-18 12:11:44 | INFO | testcases.test_ella.base_ella_test:_execute_command:870 | ✅ 成功执行命令: Change the style of this image to 3D cartoon
2025-08-18 12:11:44 | INFO | testcases.test_ella.base_ella_test:_handle_popup_after_command:178 | handle_popup_after_command:处理弹窗
2025-08-18 12:11:44 | INFO | core.popup_tool:detect_and_close_popup_once:735 | 执行单次弹窗检测和关闭
2025-08-18 12:11:50 | INFO | core.popup_tool:detect_and_close_popup_once:739 | 未检测到弹窗，无需处理
2025-08-18 12:11:50 | INFO | testcases.test_ella.base_ella_test:_get_response_timeout:1259 | 🎯 多模态命令 (gallery) 使用专用超时时间: 30秒
2025-08-18 12:12:20 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 30秒
2025-08-18 12:12:22 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:60 | ✅ 通过TTS按钮检测到响应
2025-08-18 12:12:25 | INFO | testcases.test_ella.base_ella_test:_get_final_status_with_page_info:440 | 状态检查时当前应用包名: com.transsion.aivoiceassistant
2025-08-18 12:12:25 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:558 | 状态检查完成，现在获取响应文本
2025-08-18 12:12:25 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:562 | 第1次尝试确保在Ella页面以获取响应
2025-08-18 12:12:25 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:662 | 确保在对话页面...
2025-08-18 12:12:25 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:12:26 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:673 | ✅ 已在对话页面
2025-08-18 12:12:26 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:566 | ✅ 已确认在Ella对话页面，可以获取响应
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:12:26 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:813 | 检查是否在Ella页面...
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:1998 | 检查当前进程是否是Ella...
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2005 | 当前应用: com.transsion.aivoiceassistant
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2006 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-18 12:12:26 | INFO | pages.base.system_status_checker:ensure_ella_process:2015 | ✅ 当前在Ella应用进程
2025-08-18 12:12:26 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:822 | ✅ 当前在Ella页面
2025-08-18 12:12:26 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-08-18 12:12:28 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:300 | asr_txt文本不符合AI响应格式: Change the style of this image to 3D cartoon，已达到最大重试次数
2025-08-18 12:12:31 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:300 | robot_text文本不符合AI响应格式: Image received! I can help you describe the image, remove people, extend image, and you can ask me directly.，已达到最大重试次数
2025-08-18 12:12:32 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_name节点不存在，已达到最大重试次数
2025-08-18 12:12:33 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点不存在，已达到最大重试次数
2025-08-18 12:12:33 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:596 | 最终获取的AI响应: '['Change the style of this image to 3D cartoon', 'Image received! I can help you describe the image, remove people, extend image, and you can ask me directly.', '', '']'
2025-08-18 12:12:33 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:407 | ✅ 多模态操作执行完成: gallery
2025-08-18 12:12:33 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaChangeStyleThisImageDCartoon\test_completed.png
2025-08-18 12:12:33 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1373 | 🎉 Change the style of this image to 3D cartoon 测试完成 (多模态: gallery)
2025-08-18 12:12:33 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1010 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['Change the style of this image to 3D cartoon', 'Image received! I can help you describe the image, remove people, extend image, and you can ask me directly.', '', '']
2025-08-18 12:12:33 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1054 | ✅ 响应包含期望内容: 'Image received'
2025-08-18 12:12:33 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1054 | ✅ 响应包含期望内容: 'I can help you describe the image'
2025-08-18 12:12:33 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1054 | ✅ 响应包含期望内容: 'remove people'
2025-08-18 12:12:33 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1054 | ✅ 响应包含期望内容: 'extend image'
2025-08-18 12:12:33 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1054 | ✅ 响应包含期望内容: 'you can ask me directly'
2025-08-18 12:12:33 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1057 | ⚠️ 响应未包含期望内容: 'Sorry'
2025-08-18 12:12:33 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1057 | ⚠️ 响应未包含期望内容: 'a bit difficult'
2025-08-18 12:12:33 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1064 | ❌ 部分期望内容未找到 (5/7)
2025-08-18 12:12:33 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1065 | 缺失内容: ['Sorry', 'a bit difficult']
2025-08-18 12:12:33 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1066 | 搜索文本: 'Change the style of this image to 3D cartoon Image received! I can help you describe the image, remove people, extend image, and you can ask me directly.'
2025-08-18 12:12:34 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaChangeStyleThisImageDCartoon\failure_test_change_the_style_of_this_image_to_d_cartoon_20250818_121233.png
2025-08-18 12:12:34 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaChangeStyleThisImageDCartoon\failure_test_change_the_style_of_this_image_to_d_cartoon_20250818_121233.png
2025-08-18 12:12:34 | INFO | pages.apps.ella.dialogue_page:stop_app:308 | 停止Ella应用
2025-08-18 12:12:35 | INFO | pages.apps.ella.dialogue_page:stop_app:319 | ✅ Ella应用已成功停止
